/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-03-21 18:42:54
 * @LastEditTime: 2024-12-20 13:12:19
 * @LastEditors: shaojun
 * @Description:
 */

import type { AliasOptions } from 'vite'
import path from 'node:path'
import process from 'node:process'

const cfeRoot = path.resolve(__dirname, 'packages')

export const alias = (): AliasOptions => {
  if (process.env.DOC_ENV === 'production') {
    return [
      {
        find: /^@cfe-node\/(server|utils|koa-swagger-decorator)$/,
        replacement: path.resolve(`${cfeRoot}/$1`),
      },
    ] as AliasOptions
  } else {
    return [
      {
        find: /^@cfe-node\/(server)$/,
        replacement: path.resolve(`${cfeRoot}/$1`, 'src/app.ts'),
      },
      {
        find: /^@cfe-node\/(server|utils|koa-swagger-decorator)$/,
        replacement: path.resolve(`${cfeRoot}/$1`, 'src/index.ts'),
      },
    ] as AliasOptions
  }
}
