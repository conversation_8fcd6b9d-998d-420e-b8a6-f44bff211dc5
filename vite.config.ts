/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-04-15 18:32:25
 * @LastEditTime: 2025-07-09 17:26:33
 * @LastEditors: s<PERSON><PERSON>
 * @Description: 
 */
import { resolve } from 'node:path'

import vue from '@vitejs/plugin-vue'
import { ConfigEnv, defineConfig } from 'vite'
import { createAllAliases } from './configs/alias'
// 创建包的别名配置
function createPackageAlias(packageName: string) {
  return {
    '@': resolve(process.cwd(), `./packages/${packageName}/src`),
  }
}
const projectRoot = resolve(process.cwd(), '.')
export default defineConfig((_config: ConfigEnv) => {
  return {
    plugins: [vue()],
    resolve: {
      alias: createAllAliases(projectRoot),
    },
  }
});
