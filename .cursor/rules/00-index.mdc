---
alwaysApply: false
---
# 🏗️ CFE Components Plus - Cursor 规则索引

## 📋 规则文件映射

### 🎯 核心规则
- `01-project-overview.mdc` - 项目架构和目录结构
- `04-code-standards.mdc` - TypeScript/Vue 代码规范
- `05-vue-compatibility.mdc` - Vue2/3 兼容性处理

### 🔧 功能开发
- `02-components-guide.mdc` - 组件库开发指南
- `08-hooks-and-composables.mdc` - Composables 开发
- `packages-cli-guide.mdc` - CLI 工具开发

### ⚙️ 环境构建
- `03-development-guide.mdc` - 开发环境和工作流
- `06-build-system.mdc` - 构建系统和发布
- `07-monorepo-structure.mdc` - Monorepo 架构

## 🗺️ 任务规则组合

| 任务类型 | 主要规则 | 辅助规则 |
|---------|---------|---------|
| 🧩 组件开发 | 02 | 04, 05 |
| 🛠️ CLI开发 | packages-cli | 04 |
| 🔧 Composables | 08 | 04, 05 |
| 📦 包管理 | 07 | 03 |
| 🏗️ 构建部署 | 06 | 03 |
| 📝 代码审查 | 04 | 05 |

## 🎯 AI处理原则

### 优先级
1. 项目特定规则 > 通用规范
2. Vue兼容性要求 > 单版本特性  
3. TypeScript严格模式 > 宽松模式
4. Monorepo架构 > 单包模式

### 关键词映射
```
component/组件 → 02 + 04 + 05
cli/命令行 → packages-cli + 04  
hook/composable → 08 + 04 + 05
build/构建 → 06 + 03
package/包 → 07 + 03
test/测试 → 04
vue → 05 + 02
typescript → 04
```

---
> 💡 **使用说明**: 根据用户请求快速定位相关规则文件，优先阅读主要规则，必要时结合辅助规则提供完整解决方案。
