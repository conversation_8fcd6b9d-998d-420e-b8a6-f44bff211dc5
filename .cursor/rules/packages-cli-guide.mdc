---
description: 
globs: packages/cli/*
alwaysApply: false
---
# CFE-CLI工具开发规范

## 1. 整体架构

### 1.1 目录结构
```
packages/cli/
├── dist/            # 构建输出目录
├── src/             # 源代码目录
│   ├── commands/    # 命令实现目录
│   │   ├── command-name/  # 每个命令单独一个目录
│   │   │   ├── index.ts   # 命令导出文件
│   │   │   ├── command-name.ts  # 主要实现
│   │   │   ├── type.ts    # 命令专用类型定义
│   │   │   ├── config-extractor.ts # 配置提取器（如需要）
│   │   │   ├── *.schema.json # JSON Schema（如需要）
│   │   │   ├── design.md  # 设计文档
│   │   │   └── README.md  # 使用说明
│   │   ├── index.ts       # 命令注册
│   │   └── type.ts        # 全局CLI通用类型定义
│   ├── common/      # 通用工具和框架
│   │   ├── MergeConfig.ts # 通用配置合并框架
│   │   ├── LoadConfig.ts  # 配置文件加载
│   │   └── ...      # 其他通用模块
│   ├── utils/       # 工具函数
│   │   ├── logger.ts      # 日志工具（必须使用）
│   │   └── ...      # 其他工具模块
│   ├── cli.ts       # CLI入口
│   └── index.ts     # 包入口
├── tests/           # 测试目录
├── examples/        # 示例代码
├── build.config.ts  # 构建配置
├── *.schema.json    # 全局配置 Schema
├── package.json     # 包配置
└── tsconfig.json    # TypeScript配置
```

### 1.2 核心依赖
- `cac`: 用于构建命令行程序
- `consola`: 统一的日志输出工具
- `unbuild`: 用于构建工具包

## 2. 命令开发规范

### 2.1 命令结构规范

每个命令必须遵循以下结构：
1. 在`commands`目录下为每个命令创建单独的目录
2. 每个命令目录包含：
   - `index.ts`: 导出命令定义和创建函数
   - `command-name.ts`: 命令核心实现
   - `type.ts`: 命令专用类型定义
   - `config-extractor.ts`: 配置提取器（如需要复杂配置）
   - `*.schema.json`: JSON Schema文件（如需要配置文件）
   - `design.md`: 设计文档
   - `README.md`: 使用说明文档

### 2.2 命令接口规范

每个命令必须实现`ICommand`接口：

```typescript
export interface ICommand {
  name: string        // 命令名称
  description: string // 命令描述
  create: (cli: CAC) => Command // 命令创建函数
}
```

### 2.3 命令实现规范

1. **命令创建函数模板**（使用配置合并框架）:
```typescript
import { createConfigMerger } from '../../common/MergeConfig'
import { error } from '../../utils/logger'
import { CommandConfigExtractor } from './config-extractor'

export function create(cli: CAC): Command {
  return cli.command('command-name [arg]', '命令描述')
    .option('-o, --option', '选项描述', { default: defaultValue })
    .option('-c, --config <path>', '专用配置文件路径')
    .action(async (arg: ArgType, options: Partial<OptionsType>) => {
      try {
        // 创建配置合并器
        const configMerger = createConfigMerger(
          new CommandConfigExtractor(),
          {
            mainConfigFile: 'cfe.config.json',
            dedicatedConfigFile: options.config,
            verbose: options.log,
          }
        )

        // 合并配置
        const { finalOptions } = await configMerger.mergeConfig(options)

        // 实例化命令类
        const commandInstance = new CommandClass(finalOptions)
        
        // 执行命令
        const exitCode = await commandInstance.run()
        process.exit(exitCode)
      } catch (err) {
        error(`命令执行失败: ${err}`)
        process.exit(1)
      }
    })
}
```

2. **命令导出模板**:
```typescript
export const commandName: ICommand = {
  name,
  description,
  create,
}
```

## 3. 日志记录规范

### 3.1 Logger 使用规则

**强制要求**：所有命令实现中必须使用统一的 logger，禁止直接使用 `console.log`、`console.error` 等原生方法。

#### 3.1.1 导入 Logger
```typescript
import { error, info, log, success, warn } from '../../utils/logger'
```

#### 3.1.2 Logger 方法分类

| 方法 | 用途 | 示例 |
|------|------|------|
| `log()` | 普通信息输出 | `log('源目录: /path/to/source')` |
| `info()` | 重要信息提示 | `info('🔧 初始化同步工具...')` |
| `success()` | 成功状态显示 | `success('✅ 同步完成！')` |
| `warn()` | 警告信息 | `warn('⚠️ 这是预演模式，实际文件未被修改')` |
| `error()` | 错误信息 | `error('❌ 同步失败:', err)` |

#### 3.1.3 使用原则

1. **信息层级化**：
   - 使用 `info()` 显示主要操作步骤
   - 使用 `log()` 显示详细信息和数据
   - 使用 `success()` 显示成功完成的操作
   - 使用 `warn()` 显示警告和注意事项
   - 使用 `error()` 显示错误信息

2. **Emoji 使用规范**：
   - 🔧 初始化相关
   - ✅ 验证和成功状态
   - 📊 数据分析
   - 📋 计划和预览
   - 🚀 开始执行
   - 🗑️ 删除操作
   - 📁 目录操作
   - 📄 文件操作
   - 💾 备份操作
   - ⚠️ 警告提示
   - ❌ 错误状态

3. **日志格式一致性**：
```typescript
// ✅ 正确示例
info('🔧 初始化同步工具...')
log(`源目录: ${sourcePath}`)
success('✅ 同步完成！')
warn('⚠️ 注意：这将会修改目标目录的文件！')

// ❌ 错误示例 - 禁止使用
console.log('开始同步...')
console.error('同步失败')
```

#### 3.1.4 条件日志输出
```typescript
// 根据 options.log 控制详细日志输出
if (this.options.log) {
  info('🔧 初始化同步工具...')
  log(`源目录: ${this.options.from}`)
  log(`目标目录: ${this.options.to}`)
}

// 错误信息总是输出，不受 log 选项控制
error('❌ 同步失败:', err)
```

## 4. 类型定义规范

### 4.1 类型分层设计

#### 全局通用类型 (`src/commands/type.ts`)
- 使用**泛型设计**，支持多命令复用
- 包含配置策略、通用接口等
- 示例：
```typescript
// 泛型配置合并结果
export interface MergedConfig<T> {
  final: T
  sources: Record<keyof T, ConfigWithSource>
  warnings: string[]
}
```

#### 命令专用类型 (`src/commands/command-name/type.ts`)
- 专注于业务相关的类型定义
- 避免创建未使用的类型别名
- 示例：
```typescript
// 命令选项类型
export interface CommandOptions extends CommandConfig{
  /** 选项1描述 */
  option1: string
  /** 选项2描述 */
  option2: boolean
  /** 输出详细日志 */
  log?: boolean
}

// 命令配置类型
export interface CommandConfig {
  /** 全局配置 */
  global?: CommandGlobalConfig
  /** 任务列表 */
  tasks?: CommandTask[]
}

// 其他业务相关类型
export interface CommandTask {
  name: string
  enabled?: boolean
  option1?: string
  option2?: boolean
}
```

### 4.2 类型命名规范

- **通用类型**：使用泛型，如 `ConfigExtractor<T>`
- **业务类型**：命令前缀 + 功能描述，如 `SyncTask`、`BatchToolOptions`
- **枚举类型**：使用 PascalCase，如 `ConfigSource`
- **接口类型**：使用 PascalCase，如 `CommandOptions`

### 4.3 避免重复定义

❌ **错误做法**：
```typescript
// 创建未使用的类型别名
export type CommandConfigLoader = SomeUnusedInterface<CommandOptions>
export type CommandMergedConfig = MergedConfig<CommandOptions>
```

✅ **正确做法**：
```typescript
// 直接使用通用类型，无需创建别名
import type { ConfigExtractor } from '../../common/MergeConfig'
export class CommandConfigExtractor implements ConfigExtractor<CommandConfig, CommandOptions> {
  // 实现配置提取逻辑
}
```

## 5. 通用配置合并框架

### 5.1 框架概述

CFE CLI 提供了统一的配置合并框架 (`MergeConfig.ts`)，用于处理复杂的配置优先级和合并逻辑。

**核心特性**：
- 🔧 **泛型设计**：支持任意类型的配置和选项
- 📊 **配置来源追踪**：记录每个配置项的来源
- 🎯 **灵活的提取器模式**：通过 `ConfigExtractor` 接口定制提取逻辑
- 🚀 **标准化优先级**：命令行 > 专用配置 > 主配置 > 默认值
- 📝 **详细日志**：可选的详细配置加载过程日志

### 5.2 核心接口

#### ConfigExtractor 接口
```typescript
export interface ConfigExtractor<TConfig, TOptions> {
  /**
   * 从主配置文件中提取命令相关配置
   */
  extractFromMainConfig: (mainConfig: Record<string, any>) => TConfig | null
  
  /**
   * 从专用配置文件中提取选项
   */
  extractDedicatedOptions: (config: TConfig) => Partial<TOptions>
  
  /**
   * 从配置文件中提取全局选项
   */
  extractGlobalOptions: (config: TConfig) => Partial<TOptions>
  
  /**
   * 获取默认配置
   */
  getDefaultOptions: () => TOptions
  
  /**
   * 验证最终配置是否有效
   */
  validateOptions: (options: TOptions) => void
}
```

#### ConfigSource 枚举
```typescript
export enum ConfigSource {
  CLI = 'cli',                    // 命令行参数
  DEDICATED_CONFIG = 'dedicated', // 专用配置文件
  MAIN_CONFIG = 'main',          // 主配置文件
  DEFAULT = 'default',           // 默认值
}
```

### 5.3 配置提取器实现

每个需要复杂配置的命令应该创建专门的配置提取器：

```typescript
// src/commands/command-name/config-extractor.ts
import type { ConfigExtractor } from '../../common/MergeConfig'
import type { UserConfig } from '../type'
import type { CommandConfig, CommandOptions } from './type'

export class CommandConfigExtractor implements ConfigExtractor<CommandConfig, CommandOptions> {
  /**
   * 从主配置文件中提取命令配置
   */
  extractFromMainConfig(mainConfig: UserConfig): CommandConfig | null {
    return mainConfig.commandName || null
  }

  /**
   * 从专用配置文件中提取选项
   */
  extractDedicatedOptions(config: CommandConfig): Partial<CommandOptions> {
    return {
      ...config,
      // 提取配置文件中的直接选项
    }
  }

  /**
   * 从配置文件中提取全局选项
   */
  extractGlobalOptions(config: CommandConfig): Partial<CommandOptions> {
    const global = config.global || {}
    
    return {
      log: global.log,
      dryRun: global.dryRun,
      force: global.force,
      // 其他全局配置项...
    }
  }

  /**
   * 获取默认配置
   */
  getDefaultOptions(): CommandOptions {
    return {
      config: 'cfe.config.json',
      dryRun: false,
      force: false,
      log: true,
      // 命令特定的默认配置...
    }
  }

  /**
   * 验证最终配置是否有效
   */
  validateOptions(options: CommandOptions): void {
    if (!options.config) {
      throw new Error('缺少必要参数：请指定配置文件路径')
    }
    
    // 根据具体命令需求添加其他验证逻辑
    // 例如：验证必需的任务列表、文件路径等
  }
}
```

### 5.4 在命令中使用配置合并框架

```typescript
// src/commands/command-name/index.ts
import type { CAC, Command } from 'cac'
import type { CommandOptions } from './type'
import { createConfigMerger } from '../../common/MergeConfig'
import { logger } from '../../utils/logger'
import { CommandConfigExtractor } from './config-extractor'
import { CommandClass } from './command-name'

export function create(cli: CAC): Command {
  return cli
    .command('command-name', '命令描述')
    .option('-o, --option1 <value>', '选项1描述')
    .option('-c, --config <path>', '专用配置文件路径')
    .option('--log', '输出详细日志', { default: false })
    .action(async (options: Partial<CommandOptions>) => {
      try {
        // 创建配置合并器
        const configMerger = createConfigMerger(
          new CommandConfigExtractor(),
          {
            mainConfigFile: 'cfe.config.json',
            dedicatedConfigFile: options.config,
            verbose: options.log,
          }
        )

        // 合并配置
        const { finalOptions, sources, loadedConfigs } = await configMerger.mergeConfig(options)

        if (finalOptions.log) {
          logger.info(`command-name 最终配置: ${JSON.stringify(finalOptions, null, 2)}`)
          
          // 开发模式下显示配置来源信息
          if (process.env.NODE_ENV === 'development') {
            logger.info(`配置来源: ${JSON.stringify(sources, null, 2)}`)
            logger.info(`加载的配置文件: ${JSON.stringify(loadedConfigs, null, 2)}`)
          }
        }

        // 创建命令实例并执行
        const commandInstance = new CommandClass(finalOptions)
        const exitCode = await commandInstance.run()
        process.exit(exitCode)
      } catch (error) {
        logger.error(`命令执行失败: ${error}`)
        process.exit(1)
      }
    })
}
```

### 5.5 配置优先级和合并规则

#### 优先级顺序（从高到低）
1. **命令行参数** (`ConfigSource.CLI`)
2. **专用配置文件** (`ConfigSource.DEDICATED_CONFIG`)
3. **主配置文件** (`ConfigSource.MAIN_CONFIG`)
4. **默认值** (`ConfigSource.DEFAULT`)

#### 合并规则
- **基本类型** (string/boolean/number)：完全覆盖
- **数组类型**：完全替换，不合并
- **对象类型**：深度合并

#### 示例
```typescript
// 命令行参数
{ include: ["**/*.js"], backup: true }

// 配置文件
{ include: ["**/*.vue", "**/*.ts"], exclude: ["**/*.test.*"], backup: false }

// 最终结果
{
  include: ["**/*.js"],        // 命令行覆盖（数组完全替换）
  exclude: ["**/*.test.*"],    // 保留配置文件值
  backup: true                 // 命令行覆盖
}
```

### 5.6 配置合并详细逻辑

#### 5.6.1 配置提取流程
1. **加载专用配置文件**：如果通过 `--config` 指定了专用配置文件
2. **加载主配置文件**：加载项目根目录的 `cfe.config.json`
3. **提取任务配置**：优先从专用配置提取，否则从主配置提取
4. **提取全局配置**：合并专用和主配置的全局设置
5. **按优先级合并**：命令行 > 专用配置 > 主配置 > 默认值
6. **验证最终配置**：确保配置的有效性

#### 5.6.2 配置合并特性
- **深度合并对象**：对象类型的配置项会递归合并
- **数组完全替换**：高优先级的数组会完全替换低优先级的数组
- **基本类型覆盖**：string/boolean/number 类型直接覆盖
- **配置来源追踪**：记录每个配置项来自哪个源

#### 5.6.3 实际合并示例
```typescript
// 默认配置
{
  log: true,
  exclude: ['node_modules/**'],
  global: { timeout: 30 }
}

// 主配置文件
{
  log: false,
  exclude: ['dist/**', 'build/**'],
  global: { timeout: 60, parallel: true }
}

// 命令行参数
{
  log: true,
  include: ['src/**']
}

// 最终合并结果
{
  log: true,                           // 命令行覆盖
  exclude: ['dist/**', 'build/**'],    // 主配置文件覆盖（数组完全替换）
  include: ['src/**'],                 // 命令行新增
  global: { timeout: 60, parallel: true } // 对象深度合并
}
```

### 5.7 最佳实践

#### 5.7.1 配置提取器设计原则
1. **单一职责**：每个方法只负责一种配置提取
2. **容错处理**：优雅处理缺失或无效的配置
3. **类型安全**：充分利用 TypeScript 类型检查
4. **默认值合理**：提供合理的默认配置
5. **配置扁平化**：在 `extractDedicatedOptions` 中通过 `...config` 展开配置
6. **全局配置隔离**：在 `extractGlobalOptions` 中只提取全局相关配置

#### 5.7.2 何时使用配置合并框架
- **复杂配置需求**：需要多层配置优先级
- **多任务支持**：配置文件包含任务列表
- **全局和局部配置**：需要区分全局和特定配置
- **配置来源追踪**：需要了解配置项的来源

#### 5.7.3 简单配置的替代方案
对于简单命令，可以直接使用基础配置加载：
```typescript
import { readConfigJsonSync } from '../../common/LoadConfig'

// 简单配置合并
const userConfig = readConfigJsonSync<UserConfig>({ 
  root: process.cwd(), 
  configFile: 'cfe.config.json' 
})
const mergedOptions = { ...defaultOptions, ...userConfig.commandName, ...cliOptions }
```

#### 5.7.4 配置提取器实现注意事项
1. **extractDedicatedOptions 方法**：
   - 使用 `...config` 展开配置对象，实现配置的扁平化
   - 这样可以直接将配置文件的属性映射到选项对象
   
2. **extractGlobalOptions 方法**：
   - 只提取 `config.global` 中的配置项
   - 避免将非全局配置混入全局选项
   
3. **配置文件结构适配**：
   - 确保配置文件的结构与提取器的预期匹配
   - 支持可选的 `global` 配置节点
   
4. **类型安全**：
   - 返回类型明确为 `Partial<TOptions>` 而非 `null`
   - 利用 TypeScript 的类型检查确保配置的正确性

## 6. 命令实现类规范

### 6.1 类结构

```typescript
import { error, info, success } from '../../utils/logger'

export class CommandClass {
  private options: CommandOptions

  constructor(options: CommandOptions) {
    this.options = options
  }

  // 主执行函数
  public async run(): Promise<number> {
    try {
      // 初始化
      this.initialize()
      
      // 执行核心逻辑
      await this.execute()
      
      // 完成处理
      return this.finalize()
    } catch (err) {
      // 错误处理
      return this.handleError(err)
    }
  }

  // 其他辅助方法
  private initialize(): void {
    if (this.options.log) {
      info('🔧 初始化工具...')
    }
  }
  
  private async execute(): Promise<void> {}
  
  private finalize(): number { 
    success('✅ 操作完成！')
    return 0 
  }
  
  private handleError(err: Error): number { 
    error('❌ 操作失败:', err)
    return 1 
  }
}
```

## 7. 配置文件规范

### 7.1 JSON Schema 设计

#### Schema 文件组织
- **全局配置 Schema**：`cfe.config.schema.json`
- **命令专用 Schema**：`src/commands/command-name/command-name.schema.json`

#### Schema 引用关系
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "$id": "https://cfe-components-plus.com/schemas/cfe.config.json",
  "properties": {
    "commandName": {
      "$ref": "src/commands/command-name/command-name.schema.json#/definitions/CommandConfig"
    }
  }
}
```

### 7.2 配置优先级策略

**优先级顺序**（从高到低）：
1. **命令行参数** (`CLI_ARGS`)
2. **专用配置文件** (`DEDICATED_CONFIG`) 
3. **主配置文件** (`MAIN_CONFIG`)
4. **默认值** (`DEFAULT`)

**合并规则**：
- **基本类型**：完全覆盖
- **数组**：完全替换（不合并）
- **对象**：深度合并

### 7.3 构建配置

在 `build.config.ts` 中添加 Schema 文件复制：

```typescript
export default createLibConfig({
  // ... 其他配置
  hooks: {
    'build:done': async () => {
      // 复制 Schema 文件到构建目录
      const distDir = resolve(packageRoot, 'dist')
      
      // 复制主配置 schema
      copyFileSync(
        resolve(packageRoot, 'cfe.config.schema.json'),
        resolve(distDir, 'cfe.config.schema.json')
      )
      
      // 复制命令专用 schema（保持目录结构）
      // ...
    }
  }
})
```

## 8. 设计文档规范

每个命令应提供一个设计文档，包含以下内容：
1. **需求背景**：问题描述和解决目标
2. **功能概述**：核心功能和特性
3. **架构设计**：使用 Mermaid 流程图、类图等
4. **配置文件格式**：JSON Schema 和示例
5. **实现注意事项**：技术细节和注意点
6. **配置优先级**：配置来源和合并规则

## 9. 开发最佳实践

### 9.1 保持简洁原则

❌ **避免过度设计**：
- 不需要专门的类型验证文件
- 不需要复杂的运行时验证
- 不需要向后兼容（新项目）

✅ **推荐做法**：
- 依赖 TypeScript 编译器进行类型检查
- 使用 JSON Schema 进行配置验证
- 专注核心功能实现
- 统一使用 logger 进行日志输出
- 合理使用配置合并框架

### 9.2 代码组织原则

1. **单一职责**：每个文件只负责一个明确的功能
2. **类型安全**：充分利用 TypeScript 类型系统
3. **模块化**：通过导入/导出管理依赖关系
4. **可扩展**：使用泛型设计支持未来扩展
5. **日志统一**：使用标准 logger 而非原生 console 方法
6. **配置标准化**：复杂配置使用通用配置合并框架

### 9.3 命名一致性

- **文件命名**：kebab-case（如 `sync-folder.ts`）
- **类型命名**：PascalCase（如 `SyncFolderOptions`）
- **变量命名**：camelCase（如 `configExtractor`）
- **常量命名**：UPPER_SNAKE_CASE（如 `DEFAULT_MERGE_STRATEGY`）

### 9.4 工具类组织

#### 模块化设计
```typescript
// utils/index.ts - 工具类导出
export { FolderDiffer } from './folder-differ'
export { FolderSyncer } from './folder-syncer'
export { PathValidator } from './path-validator'

// 在命令中导入
import { FolderDiffer, FolderSyncer, PathValidator } from './utils'
```

#### Logger 在工具类中的使用
```typescript
// utils/folder-syncer.ts
import { error, info, log, success, warn } from '../../../utils/logger'

export class FolderSyncer {
  public async sync(plan: SyncPlan, options: { log?: boolean }): Promise<void> {
    if (options.log) {
      info('🚀 开始同步文件夹...')
      log(`源目录: ${plan.sourcePath}`)
    }
    
    try {
      // 执行同步逻辑
      success('✅ 同步完成！')
    } catch (err) {
      error('同步失败:', err)
    }
  }
}
```

## 10. 测试规范

1. **命令测试**：测试命令的选项解析和基本流程
2. **核心逻辑测试**：测试命令实现类的核心逻辑
3. **配置合并测试**：测试配置提取器和合并逻辑
4. **集成测试**：测试整个命令流程
5. **类型测试**：通过 TypeScript 编译确保类型正确
6. **Logger 测试**：确保使用正确的 logger 方法

## 11. 添加新命令流程

1. **创建命令目录**：`src/commands/new-command/`
2. **实现核心文件**：
   - `index.ts` - 命令导出（使用配置合并框架）
   - `new-command.ts` - 主要实现（使用 logger）
   - `type.ts` - 类型定义
   - `config-extractor.ts` - 配置提取器（如需复杂配置）
3. **添加配置支持**（如需要）：
   - `new-command.schema.json` - JSON Schema
   - 更新全局配置 Schema 引用
4. **编写文档**：
   - `design.md` - 设计文档
   - `README.md` - 使用说明
5. **注册命令**：在 `commands/index.ts` 中注册
6. **添加测试**：编写相应的测试用例

## 12. 质量保证

### 12.1 构建验证
- 确保 TypeScript 编译无错误
- 确保 lint 检查通过
- 确保所有 Schema 文件正确复制
- 确保没有使用原生 console 方法
- 验证配置合并逻辑正确性

### 12.2 代码审查要点
- 类型定义是否合理和一致
- 是否遵循简洁性原则
- 是否有不必要的重复代码
- 配置优先级是否正确实现
- **是否正确使用 logger**
- **是否正确使用配置合并框架**
- 配置提取器实现是否完整

### 12.3 文档完整性
- 设计文档是否完整
- 使用说明是否清晰
- 类型定义是否有适当注释
- 配置示例是否完整

## 13. 常见问题

### Q: 何时需要创建 JSON Schema？
A: 当命令需要配置文件支持时，应该创建对应的 JSON Schema 来提供 IDE 支持和验证。

### Q: 何时使用通用配置合并框架？
A: 当命令需要复杂的配置优先级、多任务支持、或需要追踪配置来源时使用。简单配置可以直接使用基础配置加载。

### Q: 如何处理配置优先级？
A: 使用通用配置合并框架，通过实现 ConfigExtractor 接口来定制配置提取逻辑，框架会自动处理优先级合并。

### Q: 是否需要向后兼容？
A: 新开发的功能通常不需要向后兼容，保持代码简洁。已有功能的重大变更需要考虑向后兼容。

### Q: 如何避免创建未使用的类型？
A: 避免创建未实际使用的类型别名。直接使用通用框架类型，通过实现 `ConfigExtractor` 接口来定制配置逻辑，无需创建额外的类型别名。

### Q: 为什么必须使用 logger 而不是 console？
A: 使用统一的 logger 可以：
- 提供一致的日志格式和样式
- 支持日志级别控制
- 便于调试和问题排查
- 保持代码风格统一
- 支持彩色输出和emoji美化

### Q: 在工具类中如何使用 logger？
A: 工具类应该从 `../../../utils/logger` 导入 logger 方法，并根据操作类型选择合适的日志级别。错误信息总是输出，其他信息根据传入的 options 控制。

### Q: 配置提取器的核心方法如何实现？
A: 
- **extractFromMainConfig**: 从主配置文件中提取命令特定的配置节点，如 `mainConfig.syncFolder`
- **extractDedicatedOptions**: 使用 `...config` 展开配置对象，实现配置扁平化
- **extractGlobalOptions**: 只从 `config.global` 中提取全局配置项，避免混合非全局配置
- **getDefaultOptions**: 提供完整的默认配置，包括所有必需选项的合理默认值
- **validateOptions**: 验证最终合并配置的有效性，抛出明确的错误信息

### Q: 配置合并的优先级如何工作？
A: 配置合并按以下顺序进行：
1. 首先加载默认配置作为基础
2. 深度合并全局配置（主配置和专用配置的全局设置）
3. 深度合并专用配置或主配置的直接选项
4. 最后合并命令行参数，具有最高优先级
配置来源会被追踪，便于调试和问题排查。

### Q: 什么时候需要创建配置提取器？
A: 当命令需要复杂配置处理时创建配置提取器（实现 `ConfigExtractor` 接口）：
- 需要支持专用配置文件
- 需要全局和局部配置分离
- 需要配置优先级和来源追踪
- 有复杂的配置验证需求
简单命令可以直接使用基础配置加载函数。

### Q: 配置提取器的类型参数如何设计？
A: 配置提取器使用两个泛型参数：
- `TConfig`: 配置文件的结构类型（如 `SyncFolderConfig`）
- `TOptions`: 最终选项的类型（如 `SyncFolderOptions`）
这样设计可以保证类型安全，并支持配置文件结构与最终选项结构的差异。
