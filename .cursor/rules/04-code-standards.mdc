---
description: 
globs: 
alwaysApply: false
---
# CFE Components Plus - 代码规范

## 🔧 TypeScript 规范

### 基础配置
项目严格使用 TypeScript 开发，配置文件位于 [tsconfig.json](mdc:tsconfig.json)。

**核心要求**:
- ✅ 所有代码必须有类型定义
- ✅ 启用严格模式 (`strict: true`)
- ✅ 禁止使用 `any` 类型 (`noImplicitAny: true`)
- ✅ 必须处理可能的 `undefined` 值

### 类型定义规范
```typescript
// ✅ 推荐 - 明确的接口定义
export interface ComponentProps {
  /** 组件标题 */
  title: string
  /** 是否可见 */
  visible?: boolean
  /** 尺寸选项 */
  size: 'small' | 'medium' | 'large'
  /** 点击处理函数 */
  onClick?: (event: MouseEvent) => void
}

// ✅ 推荐 - 使用泛型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// ❌ 避免 - 过于宽泛的类型
export interface ComponentProps {
  config: any                    // 应该明确具体类型
  callback: Function             // 应该定义具体的函数签名
  options: Record<string, any>   // 应该定义具体的选项类型
}
```

### 导入导出规范
```typescript
// ✅ 推荐 - 命名导出（支持 tree-shaking）
export { Button } from './button'
export { Input } from './input'
export type { ButtonProps, InputProps } from './types'

// ✅ 推荐 - 类型导入
import type { ComponentProps } from './types'
import type { PropType } from 'vue'

// ❌ 避免 - 默认导出
export default Button  // 不利于 tree-shaking

// ❌ 避免 - 混合导入类型
import { ComponentProps, someFunction } from './types'  // 应该分离类型和值
```

## 🎨 代码风格规范

### ESLint 配置
代码风格由 ESLint 和 Prettier 统一控制，配置位于 [eslint.config.mjs](mdc:eslint.config.mjs)。

**核心风格规则**:
```typescript
// ✅ 使用单引号
const message = 'Hello World'

// ✅ 使用 2 空格缩进
const config = {
  name: 'value',
  nested: {
    key: 'value'
  }
}

// ✅ 语句结尾使用分号
const result = computeValue();

// ✅ 文件末尾必须有空行
export { Component }
// <- 这里有空行

// ✅ 最大行长度 100 字符
const longVariableName = 'this is a very long string that should not exceed the line limit'
```

### 命名约定
```typescript
// ✅ 变量和函数 - camelCase
const userName = 'john'
const getUserInfo = () => ({ name: userName })

// ✅ 常量 - SCREAMING_SNAKE_CASE
const MAX_RETRY_COUNT = 3
const API_BASE_URL = 'https://api.example.com'

// ✅ 类型和接口 - PascalCase
interface UserConfig {
  name: string
}
type UserRole = 'admin' | 'user'

// ✅ 组件名 - PascalCase with Cfe prefix
export const CfeButton = withInstall(Button)
export const CfeDataTable = withInstall(DataTable)

// ✅ 文件名 - kebab-case
// user-config.ts
// data-table.vue
// api-client.ts
```

### 注释规范
```typescript
/**
 * 计算两个数的和
 * @param a 第一个数字
 * @param b 第二个数字
 * @returns 两数之和
 * @example
 * ```typescript
 * const result = add(1, 2) // 3
 * ```
 */
export function add(a: number, b: number): number {
  return a + b
}

// 单行注释用于解释复杂逻辑
const complexCalculation = (x * y) + z // 计算复合值

/**
 * TODO: 待实现的功能
 * FIXME: 需要修复的问题
 * NOTE: 重要说明
 */
```

## 🧩 Vue 组件规范

### 组件结构
```vue
<template>
  <!-- 1. 使用语义化的 HTML 标签 -->
  <article class="cfe-card">
    <header class="cfe-card__header">
      <h2 class="cfe-card__title">{{ title }}</h2>
    </header>
    
    <main class="cfe-card__content">
      <slot />
    </main>
    
    <footer class="cfe-card__footer" v-if="$slots.footer">
      <slot name="footer" />
    </footer>
  </article>
</template>

<script setup lang="ts">
// 2. 导入顺序：Vue API -> 第三方库 -> 项目内部
import { computed, ref } from 'vue'
import { useVModel } from '@vueuse/core'
import type { CardProps } from './types'

// 3. 定义组件元信息
defineOptions({
  name: 'CfeCard',
  inheritAttrs: false
})

// 4. Props 定义（使用 withDefaults）
const props = withDefaults(defineProps<CardProps>(), {
  title: '',
  bordered: true,
  shadow: 'hover'
})

// 5. Events 定义
const emit = defineEmits<{
  click: [event: MouseEvent]
  close: []
}>()

// 6. 响应式数据
const isVisible = ref(true)

// 7. 计算属性
const classes = computed(() => ({
  'cfe-card': true,
  'cfe-card--bordered': props.bordered,
  'cfe-card--shadow-hover': props.shadow === 'hover'
}))

// 8. 方法定义
const handleClick = (event: MouseEvent) => {
  emit('click', event)
}

// 9. 生命周期钩子
onMounted(() => {
  console.log('Card mounted')
})
</script>

<style lang="scss" scoped>
// 10. 样式使用 SCSS 和 BEM 命名
.cfe-card {
  border-radius: 8px;
  background: white;
  
  &--bordered {
    border: 1px solid var(--border-color);
  }
  
  &__header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
  }
  
  &__title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  &__content {
    padding: 16px;
  }
  
  &__footer {
    padding: 12px 16px;
    background: var(--bg-color-secondary);
  }
}
</style>
```

### Props 和 Events 规范
```typescript
// types.ts
export interface CardProps {
  /** 卡片标题 */
  title?: string
  /** 是否显示边框 */
  bordered?: boolean
  /** 阴影类型 */
  shadow?: 'always' | 'hover' | 'never'
  /** 卡片尺寸 */
  size?: 'small' | 'medium' | 'large'
}

export interface CardEmits {
  /** 卡片点击事件 */
  (e: 'click', event: MouseEvent): void
  /** 卡片关闭事件 */  
  (e: 'close'): void
}

// ✅ 使用具体的事件类型
emit('click', event)  // event 是 MouseEvent 类型

// ❌ 避免使用 any
emit('click', event as any)
```

### Composables 规范
```typescript
// ✅ 推荐的 composable 结构
export function useCounter(initialValue = 0) {
  const count = ref(initialValue)
  
  const increment = () => count.value++
  const decrement = () => count.value--
  const reset = () => count.value = initialValue
  
  // 返回对象解构，方便使用
  return {
    count: readonly(count),  // 只读状态
    increment,
    decrement, 
    reset
  }
}

// ✅ 带参数和选项的 composable
export function useApi<T>(
  url: string,
  options: {
    immediate?: boolean
    transform?: (data: any) => T
  } = {}
) {
  const { immediate = true, transform } = options
  
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)
  
  const execute = async () => {
    try {
      loading.value = true
      error.value = null
      
      const response = await fetch(url)
      const result = await response.json()
      
      data.value = transform ? transform(result) : result
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
    } finally {
      loading.value = false
    }
  }
  
  if (immediate) {
    execute()
  }
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    execute
  }
}
```

## 📦 包结构规范

### 标准包结构
```
packages/package-name/
├── src/
│   ├── index.ts          # 包主入口
│   ├── types.ts          # 类型定义
│   ├── utils/            # 工具函数
│   └── components/       # 组件(如适用)
├── __tests__/
│   └── index.test.ts     # 测试文件
├── build.config.ts       # 构建配置
├── package.json          # 包配置
├── tsconfig.json         # TS配置
└── README.md             # 包文档
```

### package.json 规范
```json
{
  "name": "@cfe-components/package-name",
  "version": "0.0.1",
  "description": "Package description",
  "type": "module",
  "main": "./dist/index.cjs",
  "module": "./dist/index.mjs", 
  "types": "./dist/index.d.ts",
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.mjs",
      "require": "./dist/index.cjs"
    },
    "./package.json": "./package.json"
  },
  "files": ["dist"],
  "scripts": {
    "build": "unbuild",
    "dev": "unbuild --stub",
    "test": "vitest"
  },
  "peerDependencies": {
    "vue": "^2.7.0 || ^3.0.0"
  },
  "devDependencies": {
    "@cfe-components/utils": "workspace:*"
  }
}
```

## 🧪 测试规范

### 测试文件组织
```typescript
// button.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { CfeButton } from '../index'
import type { ButtonProps } from '../types'

describe('CfeButton', () => {
  let wrapper: VueWrapper<any>
  
  // 通用 props
  const defaultProps: ButtonProps = {
    type: 'primary',
    size: 'medium'
  }
  
  beforeEach(() => {
    wrapper = mount(CfeButton, {
      props: defaultProps
    })
  })
  
  afterEach(() => {
    wrapper.unmount()
  })
  
  describe('渲染测试', () => {
    it('应该正确渲染基础按钮', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.classes()).toContain('cfe-button')
      expect(wrapper.classes()).toContain('cfe-button--primary')
    })
    
    it('应该正确应用尺寸类名', () => {
      expect(wrapper.classes()).toContain('cfe-button--medium')
    })
  })
  
  describe('交互测试', () => {
    it('应该触发点击事件', async () => {
      await wrapper.trigger('click')
      
      expect(wrapper.emitted('click')).toBeTruthy()
      expect(wrapper.emitted('click')![0]).toHaveLength(1)
    })
    
    it('禁用状态下不应该触发点击事件', async () => {
      await wrapper.setProps({ disabled: true })
      await wrapper.trigger('click')
      
      expect(wrapper.emitted('click')).toBeFalsy()
    })
  })
  
  describe('属性测试', () => {
    it('应该响应属性变化', async () => {
      await wrapper.setProps({ type: 'danger' })
      
      expect(wrapper.classes()).toContain('cfe-button--danger')
      expect(wrapper.classes()).not.toContain('cfe-button--primary')
    })
  })
})
```

### 测试覆盖率要求
- **语句覆盖率** ≥ 80%
- **分支覆盖率** ≥ 75%
- **函数覆盖率** ≥ 90%
- **行覆盖率** ≥ 80%

## 📚 相关文档
- 项目概览: [01-project-overview.mdc](mdc:01-project-overview.mdc)
- 组件指南: [02-components-guide.mdc](mdc:02-components-guide.mdc)
- Vue兼容性: [05-vue-compatibility.mdc](mdc:05-vue-compatibility.mdc)
- Hooks开发: [08-hooks-and-composables.mdc](mdc:08-hooks-and-composables.mdc)
