---
description: 
globs: 
alwaysApply: false
---
# CFE Components Plus - 项目概览

## 🏗️ 项目简介
CFE Components Plus 是一个基于Vue的现代化组件库，采用Monorepo架构组织代码，通过pnpm workspace管理多个包。

**核心特性**:
- ✅ 同时支持 Vue 2.7+ 和 Vue 3.x
- 🏗️ Monorepo 架构，模块化开发
- 🔧 TypeScript 全面支持
- 🎨 UnoCSS + SCSS 样式系统
- 📦 多种打包格式 (ESM/CJS/IIFE)
- 🧪 完整的测试覆盖

## 📁 核心目录结构

### 主要包目录 `/packages`
```
packages/
├── components/     # 🧩 主要UI组件库
├── core/          # 🛠️ 核心功能和API
├── utils/         # 🔧 通用工具函数
├── hooks/         # 🎣 Vue Composition API钩子
├── shared/        # 🤝 共享资源和类型
├── global/        # 🌍 全局服务和插件
├── constants/     # 📋 常量定义
├── cli/          # ⚡ 命令行工具
└── __template/   # 📝 新包开发模板
```

### 开发和示例目录
```
examples/          # 📚 使用示例和演示
playgrounds/       # 🎮 开发调试环境
├── vue2.7/       # Vue 2.7 测试环境
└── vue3/         # Vue 3 测试环境
configs/          # ⚙️ 各种构建和工具配置
```

## 📄 核心配置文件

### 项目管理
- **[package.json](mdc:package.json)** - 项目依赖、脚本和元信息
- **[pnpm-workspace.yaml](mdc:pnpm-workspace.yaml)** - pnpm工作区配置
- **[tsconfig.json](mdc:tsconfig.json)** - TypeScript全局配置

### 构建工具
- **[vite.config.ts](mdc:vite.config.ts)** - Vite构建配置
- **[unocss.config.ts](mdc:unocss.config.ts)** - UnoCSS样式配置
- **[eslint.config.mjs](mdc:eslint.config.mjs)** - ESLint代码规范配置

### 开发工具
- **[vitest.config.ts](mdc:vitest.config.ts)** - 测试框架配置
- **[commitlint.config.js](mdc:commitlint.config.js)** - Git提交规范

## 🔧 构建系统

### 构建工具栈
- **Vite** - 快速构建和开发服务器
- **unbuild** - 库打包工具
- **TypeScript** - 类型检查和编译
- **Rollup** - 底层打包引擎

### 支持的输出格式
- **ESM** (`.mjs`) - 现代ES模块格式
- **CJS** (`.cjs`) - Node.js CommonJS格式
- **IIFE** (`.iife.js`) - 浏览器全局变量格式
- **Types** (`.d.ts`) - TypeScript类型声明

## 🌟 Vue版本兼容性

### 支持版本
- **Vue 2.7.x** - 支持Composition API的Vue2最新版本
- **Vue 3.x** - 完整的Vue3生态支持

### 兼容性实现
- 🔄 条件编译处理版本差异
- 🎯 使用通用的Composition API
- 🛠️ 双构建目标支持
- 🧪 分版本测试覆盖

## 🚀 快速开始

### 开发环境设置
```bash
# 安装依赖
pnpm install

# 启动Vue3开发环境
pnpm play:vue3

# 启动Vue2.7开发环境  
pnpm play:vue2
```

### 常用命令
```bash
# 构建所有包
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 版本发布
pnpm release
```

## 📚 相关文档
- 详细开发指南: [03-development-guide.mdc](mdc:03-development-guide.mdc)
- 组件开发: [02-components-guide.mdc](mdc:02-components-guide.mdc)
- Monorepo管理: [07-monorepo-structure.mdc](mdc:07-monorepo-structure.mdc)
- 构建系统: [06-build-system.mdc](mdc:06-build-system.mdc)
