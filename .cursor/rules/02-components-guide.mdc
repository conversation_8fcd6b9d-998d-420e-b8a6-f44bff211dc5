---
description: 
globs: 
alwaysApply: false
---
# CFE Components Plus - 组件库开发指南

## 🧩 组件库结构

### 核心目录
- **[packages/components/](mdc:packages/components)** - 组件库主目录
- **[packages/components/src/index.ts](mdc:packages/components/src/index.ts)** - 组件库主入口
- **[packages/components/src/components/index.ts](mdc:packages/components/src/components/index.ts)** - 组件注册入口

### 组件目录结构
```
packages/components/src/components/
├── button/                    # 组件目录
│   ├── src/                  # 组件源码
│   │   ├── button.vue       # 主组件
│   │   ├── button-group.vue # 子组件
│   │   └── types.ts         # 类型定义
│   ├── index.ts             # 组件导出
│   └── __tests__/           # 测试文件
├── template.vue             # 🎯 组件开发模板
└── index.ts                 # 组件总入口
```

## 🎯 组件开发模板

### 基础模板
[packages/components/src/components/template.vue](mdc:packages/components/src/components/template.vue) 提供了完整的组件开发模板，包含：

- ✅ Props 和 Events 定义
- ✅ 生命周期钩子
- ✅ TypeScript 类型支持
- ✅ Vue 2/3 兼容性
- ✅ 测试友好的结构

### 组件命名规范
```typescript
// ✅ 组件命名 - 使用 PascalCase，Cfe 前缀
export const CfeButton = withInstall(Button)
export const CfeButtonGroup = withInstall(ButtonGroup)

// ❌ 错误命名
export const cfeButton = withInstall(Button)     // 小写开头
export const Button = withInstall(Button)        // 缺少前缀
```

## 🔧 组件开发规范

### 1. 组件结构
```vue
<template>
  <div class="cfe-component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
// 1. 导入依赖
import { computed, ref } from 'vue'
import type { ComponentProps, ComponentEmits } from './types'

// 2. 定义组件名称
defineOptions({
  name: 'CfeComponentName'
})

// 3. 定义 Props
const props = withDefaults(defineProps<ComponentProps>(), {
  size: 'medium',
  disabled: false,
  // 其他默认值
})

// 4. 定义 Events
const emit = defineEmits<ComponentEmits>()

// 5. 组件逻辑
const isActive = ref(false)
const classes = computed(() => ({
  'cfe-component-name': true,
  'cfe-component-name--active': isActive.value,
  'cfe-component-name--disabled': props.disabled,
}))
</script>
```

### 2. 类型定义 (types.ts)
```typescript
import type { ExtractPropTypes } from 'vue'

// Props 类型定义
export interface ComponentProps {
  /** 组件大小 */
  size?: 'small' | 'medium' | 'large'
  /** 是否禁用 */
  disabled?: boolean
  /** 组件内容 */
  content?: string
}

// Events 类型定义
export interface ComponentEmits {
  /** 点击事件 */
  (e: 'click', event: MouseEvent): void
  /** 值变化事件 */
  (e: 'change', value: string): void
}

// 导出组件实例类型
export type ComponentInstance = InstanceType<typeof CfeComponentName>
```

### 3. 组件注册
```typescript
// packages/components/src/components/component-name/index.ts
import { withInstall } from '@cfe-components/utils'
import ComponentName from './src/component-name.vue'

export const CfeComponentName = withInstall(ComponentName)
export default CfeComponentName

// 导出类型
export * from './src/types'
```

## 🎨 样式开发规范

### CSS 类命名
```scss
// 使用 BEM 命名约定
.cfe-button {
  // 基础样式
  
  &--primary {
    // 修饰符样式
  }
  
  &--large {
    // 尺寸修饰符
  }
  
  &__icon {
    // 子元素样式
  }
  
  &:disabled {
    // 伪类状态
  }
}
```

### UnoCSS 集成
```vue
<template>
  <!-- 优先使用 UnoCSS 原子类 -->
  <button 
    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
    :class="customClasses"
  >
    <slot />
  </button>
</template>
```

## 🔄 Vue 2/3 兼容性

### 推荐的 API 使用
```typescript
// ✅ 推荐 - 兼容 Vue 2/3
import { ref, computed, watch, onMounted } from 'vue'

// ✅ Props 定义
const props = withDefaults(defineProps<PropsType>(), {
  defaultValue: 'default'
})

// ✅ Events 定义
const emit = defineEmits<EmitsType>()

// ✅ 响应式数据
const count = ref(0)
const doubleCount = computed(() => count.value * 2)

// ✅ 生命周期
onMounted(() => {
  console.log('组件已挂载')
})
```

### 避免使用的特性
```typescript
// ❌ 避免使用 - Vue 版本差异
// Vue 3 特有的 API
import { defineCustomElement } from 'vue'

// Vue 2 特有的 API
this.$scopedSlots
this.$listeners
```

## 🧪 组件测试

### 测试文件结构
```typescript
// packages/components/src/components/button/__tests__/button.test.ts
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { CfeButton } from '../index'

describe('CfeButton', () => {
  it('应该正确渲染基础按钮', () => {
    const wrapper = mount(CfeButton, {
      props: {
        type: 'primary'
      },
      slots: {
        default: 'Click me'
      }
    })
    
    expect(wrapper.text()).toBe('Click me')
    expect(wrapper.classes()).toContain('cfe-button--primary')
  })
  
  it('应该正确触发点击事件', async () => {
    const wrapper = mount(CfeButton)
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

### 测试工具
- 使用 [packages/components/tests/utils.ts](mdc:packages/components/tests/utils.ts) 中的辅助函数
- 支持 Vue 2/3 的统一测试环境

## 📦 组件发布流程

### 1. 开发新组件
```bash
# 1. 创建组件目录
mkdir packages/components/src/components/new-component

# 2. 复制模板文件
cp packages/components/src/components/template.vue \
   packages/components/src/components/new-component/src/new-component.vue

# 3. 创建必要文件
touch packages/components/src/components/new-component/src/types.ts
touch packages/components/src/components/new-component/index.ts
```

### 2. 注册组件
```typescript
// packages/components/src/components/index.ts
export * from './new-component'
```

### 3. 添加到主入口
```typescript
// packages/components/src/index.ts
export { CfeNewComponent } from './components'
```

## 🎯 最佳实践

### 组件设计原则
1. **单一职责** - 每个组件只负责一个功能
2. **可复用性** - 设计通用的 API 接口
3. **可访问性** - 支持键盘导航和屏幕阅读器
4. **性能优化** - 使用合适的更新策略

### API 设计规范
```typescript
// ✅ 良好的 API 设计
interface ButtonProps {
  /** 按钮类型 */
  type?: 'primary' | 'secondary' | 'danger'
  /** 按钮尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示加载状态 */
  loading?: boolean
}

// ❌ 不好的 API 设计
interface ButtonProps {
  config?: any              // 过于宽泛
  buttonType?: string       // 不明确的类型
  isDisabled?: boolean      // 不一致的命名
}
```

## 📚 相关文档
- 代码规范: [04-code-standards.mdc](mdc:04-code-standards.mdc)
- Vue兼容性: [05-vue-compatibility.mdc](mdc:05-vue-compatibility.mdc)
- Hooks开发: [08-hooks-and-composables.mdc](mdc:08-hooks-and-composables.mdc)
