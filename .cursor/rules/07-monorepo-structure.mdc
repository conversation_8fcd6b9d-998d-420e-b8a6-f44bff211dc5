---
description: 
globs: 
alwaysApply: false
---
# CFE Components Plus - Monorepo架构指南

## 🏗️ Monorepo概述

### 架构优势
- 🔄 **统一依赖管理** - 所有包共享依赖版本
- 🚀 **原子化提交** - 跨包修改可以在单个提交中完成
- 🔧 **统一工具链** - 共享构建、测试、发布流程
- 📦 **包间引用** - 便于包之间的相互依赖和调试

### 工作区管理
项目使用 **pnpm workspace** 管理多包结构，配置位于 [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml)。

## 📁 包组织架构

### 核心包层级
```
packages/
├── 🛠️ 基础设施层
│   ├── core/          # 核心API和基础功能
│   ├── utils/         # 通用工具函数
│   ├── shared/        # 共享类型和常量
│   └── constants/     # 全局常量定义
├── 🧩 功能层
│   ├── components/    # UI组件库
│   ├── hooks/         # Composition API钩子
│   └── global/        # 全局服务和插件
├── 🔧 工具层
│   ├── cli/          # 命令行工具
│   └── __template/   # 新包开发模板
└── 📚 示例层
    ├── examples/     # 使用示例
    └── playgrounds/  # 开发调试环境
```

### 包依赖关系图
```mermaid
graph TD
    A[components] --> B[core]
    A --> C[utils]
    A --> D[hooks]
    A --> E[shared]
    
    D --> B
    D --> C
    D --> E
    
    F[global] --> B
    F --> C
    
    G[cli] --> B
    G --> C
    
    B --> E
    C --> E
    
    H[constants] --> E
```

## 📦 包详细说明

### 基础设施包

#### @cfe-components/core
```typescript
// 核心功能和API
export {
  // 组件安装器
  withInstall,
  // 全局配置
  GlobalConfig,
  // 事件系统
  EventBus
} from './src'
```

#### @cfe-components/utils
```typescript
// 通用工具函数
export {
  // 类型工具
  isString, isObject, isArray,
  // DOM工具
  addClass, removeClass, hasClass,
  // 数据处理
  deepClone, merge, debounce, throttle
} from './src'
```

#### @cfe-components/shared
```typescript
// 共享类型和接口
export type {
  ComponentSize,
  ComponentType,
  GlobalConfigType
} from './src/types'

export {
  // 共享常量
  COMPONENT_SIZES,
  COMPONENT_TYPES
} from './src/constants'
```

### 功能包

#### @cfe-components/components
```typescript
// UI组件库主包
export {
  // 基础组件
  CfeButton, CfeInput, CfeSelect,
  // 布局组件
  CfeRow, CfeCol, CfeContainer,
  // 数据展示
  CfeTable, CfeCard, CfeTabs
} from './src/components'

// 组件类型
export type * from './src/types'
```

#### @cfe-components/hooks
```typescript
// Vue Composition API钩子
export {
  // 状态管理
  useToggle, useCounter, useLocalStorage,
  // DOM操作
  useClickOutside, useElementSize,
  // 数据获取
  useRequest, usePagination
} from './src'
```

### 工具包

#### @cfe-components/cli
```typescript
// 命令行工具
export {
  // 项目初始化
  initProject,
  // 组件生成
  generateComponent,
  // 构建工具
  buildProject
} from './src/commands'
```

## 🔗 包间依赖管理

### Workspace协议
使用 `workspace:*` 协议管理包间依赖：

```json
// packages/components/package.json
{
  "dependencies": {
    "@cfe-components/core": "workspace:*",
    "@cfe-components/utils": "workspace:*",
    "@cfe-components/hooks": "workspace:*",
    "@cfe-components/shared": "workspace:*"
  }
}
```

### 依赖层级规则
1. **基础设施包** 不能依赖功能包
2. **功能包** 可以依赖基础设施包
3. **工具包** 可以依赖基础设施包，但不应依赖功能包
4. **避免循环依赖** - 使用依赖图检查

### 依赖检查命令
```bash
# 查看包依赖关系
pnpm list --depth=0

# 检查特定包的依赖
pnpm why @cfe-components/utils

# 查看依赖图
pnpm -r list --depth=1
```

## 🛠️ 包管理操作

### 安装依赖
```bash
# 为根项目安装依赖
pnpm add -w typescript

# 为特定包安装依赖
pnpm add lodash --filter @cfe-components/utils

# 为所有包安装开发依赖
pnpm add -D vitest --filter "./packages/*"

# 安装workspace内部依赖
pnpm add @cfe-components/utils --filter @cfe-components/components
```

### 脚本执行
```bash
# 在所有包中执行脚本
pnpm -r run build

# 在特定包中执行脚本
pnpm --filter @cfe-components/components build

# 并行执行（提高效率）
pnpm -r --parallel run test

# 按依赖顺序执行
pnpm -r --workspace-concurrency=1 run build
```

### 包过滤器
```bash
# 按包名过滤
pnpm --filter @cfe-components/components build

# 按路径过滤
pnpm --filter "./packages/components" build

# 按模式过滤
pnpm --filter "@cfe-components/*" build

# 排除特定包
pnpm --filter "!@cfe-components/cli" build

# 依赖关系过滤
pnpm --filter "@cfe-components/components..." build  # 包含依赖
pnpm --filter "...@cfe-components/components" build  # 包含依赖者
```

## 🔄 开发工作流

### 新包创建流程
```bash
# 1. 复制模板
cp -r packages/__template packages/new-package

# 2. 更新包信息
cd packages/new-package
vim package.json  # 修改name、description等

# 3. 安装依赖
pnpm install

# 4. 开发功能
# 编写代码...

# 5. 添加测试
# 编写测试用例...

# 6. 构建验证
pnpm build

# 7. 发布
pnpm publish
```

### 跨包开发调试
```bash
# 1. 启用stub模式（开发模式）
pnpm --filter @cfe-components/utils build --stub

# 2. 在另一个包中使用
pnpm --filter @cfe-components/components dev

# 3. 实时调试
# 修改utils包代码，components包会自动更新
```

### 版本同步管理
```bash
# 同步更新所有包版本
pnpm -r exec npm version patch

# 使用changeset管理版本
pnpm changeset

# 发布所有更改的包
pnpm changeset publish
```

## 📋 包开发规范

### 包结构标准
```
packages/package-name/
├── src/
│   ├── index.ts          # 主入口文件
│   ├── types.ts          # 类型定义
│   ├── utils/            # 内部工具
│   └── components/       # 组件(如适用)
├── __tests__/
│   ├── index.test.ts     # 主测试文件
│   └── utils.test.ts     # 工具测试
├── docs/
│   └── README.md         # 包文档
├── build.config.ts       # 构建配置
├── package.json          # 包配置
├── tsconfig.json         # TypeScript配置
└── vitest.config.ts      # 测试配置
```

### package.json规范
```json
{
  "name": "@cfe-components/package-name",
  "version": "0.0.1",
  "description": "Package description",
  "type": "module",
  "main": "./dist/index.cjs",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.mjs",
      "require": "./dist/index.cjs"
    }
  },
  "files": ["dist"],
  "scripts": {
    "build": "unbuild",
    "dev": "unbuild --stub",
    "test": "vitest",
    "test:coverage": "vitest --coverage"
  },
  "peerDependencies": {
    "vue": "^2.7.0 || ^3.0.0"
  },
  "devDependencies": {
    "@cfe-components/shared": "workspace:*"
  },
  "publishConfig": {
    "access": "public"
  }
}
```

### 导出规范
```typescript
// src/index.ts - 统一导出入口
// 1. 导出主要功能
export { default as MainFunction } from './main'

// 2. 导出工具函数
export * from './utils'

// 3. 导出类型定义
export type * from './types'

// 4. 导出常量
export { CONSTANTS } from './constants'

// 5. 默认导出（如果需要）
export { default } from './main'
```

## 🔍 依赖分析与优化

### 依赖可视化
```bash
# 生成依赖图
npx madge --image deps.png packages/

# 检查循环依赖
npx madge --circular packages/

# 分析包大小
npx bundlephobia @cfe-components/components
```

### 依赖优化策略
1. **共享依赖提升** - 将公共依赖提升到根目录
2. **peer依赖使用** - 对于Vue等框架使用peerDependencies
3. **按需导入** - 支持tree-shaking的导出方式
4. **依赖去重** - 定期清理重复依赖

### 依赖管理最佳实践
```json
// 根目录 package.json
{
  "devDependencies": {
    // 所有包共享的开发依赖
    "typescript": "^5.0.0",
    "vitest": "^0.34.0",
    "unbuild": "^2.0.0"
  }
}

// 包级 package.json
{
  "peerDependencies": {
    // 框架依赖使用peer
    "vue": "^2.7.0 || ^3.0.0"
  },
  "dependencies": {
    // 只包含必要的运行时依赖
    "lodash-es": "^4.17.21"
  },
  "devDependencies": {
    // 包特有的开发依赖
    "@cfe-components/shared": "workspace:*"
  }
}
```

## 🚀 发布策略

### 独立发布
```bash
# 发布单个包
pnpm --filter @cfe-components/utils publish

# 发布多个包
pnpm --filter "@cfe-components/{utils,core}" publish
```

### 统一发布
```bash
# 发布所有更改的包
pnpm -r publish

# 使用changeset进行版本管理
pnpm changeset version
pnpm changeset publish
```

### 发布前检查
```bash
# 构建检查
pnpm -r run build

# 测试检查
pnpm -r run test

# 类型检查
pnpm -r run type-check

# 包内容检查
npm pack --dry-run
```

## 🛠️ 开发工具集成

### VS Code工作区配置
```json
// .vscode/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.workspaceSymbols.scope": "allOpenProjects"
}
```

### 调试配置
```json
// .vscode/launch.json
{
  "configurations": [
    {
      "name": "Debug Package",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/packages/cli/dist/index.js",
      "args": ["build"],
      "console": "integratedTerminal"
    }
  ]
}
```

## 📚 相关文档
- 项目概览: [01-project-overview.mdc](mdc:01-project-overview.mdc)
- 开发指南: [03-development-guide.mdc](mdc:03-development-guide.mdc)
- 构建系统: [06-build-system.mdc](mdc:06-build-system.mdc)
- 代码规范: [04-code-standards.mdc](mdc:04-code-standards.mdc)
