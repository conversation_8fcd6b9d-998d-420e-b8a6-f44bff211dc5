---
description: 
globs: 
alwaysApply: false
---
# CFE Components Plus - Hooks和Composables开发指南

## 🎣 Hooks概述

### 什么是Hooks/Composables
Hooks（在Vue中称为Composables）是基于Vue Composition API的可复用逻辑函数，用于封装和共享组件间的状态逻辑。

### 核心优势
- 🔄 **逻辑复用** - 在多个组件间共享状态逻辑
- 🧩 **模块化** - 将复杂逻辑拆分为独立的功能模块
- 🧪 **易测试** - 独立的函数便于单元测试
- 📦 **类型安全** - 完整的TypeScript支持

## 📁 Hooks组织结构

### 包结构
```
packages/hooks/
├── src/
│   ├── index.ts              # 主入口文件
│   ├── state/                # 状态管理类
│   │   ├── useToggle.ts
│   │   ├── useCounter.ts
│   │   └── useLocalStorage.ts
│   ├── dom/                  # DOM操作类
│   │   ├── useClickOutside.ts
│   │   ├── useElementSize.ts
│   │   └── useEventListener.ts
│   ├── async/                # 异步处理类
│   │   ├── useRequest.ts
│   │   ├── usePagination.ts
│   │   └── useDebounce.ts
│   ├── utils/                # 工具类
│   │   ├── useClipboard.ts
│   │   └── usePermission.ts
│   └── types.ts              # 类型定义
├── __tests__/                # 测试文件
└── docs/                     # 文档
```

### 组件内Composables
```
packages/components/src/composables/
├── useFormValidation.ts      # 表单验证
├── useTableSort.ts           # 表格排序
├── useModalState.ts          # 模态框状态
└── useTheme.ts               # 主题切换
```

## 🏗️ Hooks开发规范

### 命名约定
```typescript
// ✅ 推荐命名 - 使用 use 前缀
export function useCounter() { }
export function useToggle() { }
export function useLocalStorage() { }
export function useClickOutside() { }

// ❌ 错误命名
export function counter() { }        // 缺少use前缀
export function toggleHook() { }     // 不要使用Hook后缀
export function UseToggle() { }      // 首字母不应大写
```

### 基础Hook模板
```typescript
import { ref, computed, readonly } from 'vue'
import type { Ref } from 'vue'

/**
 * 计数器Hook
 * @param initialValue 初始值
 * @param options 配置选项
 */
export function useCounter(
  initialValue: number = 0,
  options: {
    min?: number
    max?: number
    step?: number
  } = {}
) {
  const { min, max, step = 1 } = options
  
  // 内部状态
  const count = ref(initialValue)
  
  // 计算属性
  const isAtMin = computed(() => min !== undefined && count.value <= min)
  const isAtMax = computed(() => max !== undefined && count.value >= max)
  
  // 方法
  const increment = () => {
    if (max !== undefined && count.value >= max) return
    count.value += step
  }
  
  const decrement = () => {
    if (min !== undefined && count.value <= min) return
    count.value -= step
  }
  
  const reset = () => {
    count.value = initialValue
  }
  
  const set = (value: number) => {
    if (min !== undefined && value < min) return
    if (max !== undefined && value > max) return
    count.value = value
  }
  
  // 返回API
  return {
    // 只读状态
    count: readonly(count),
    isAtMin,
    isAtMax,
    
    // 操作方法
    increment,
    decrement,
    reset,
    set
  }
}

// 类型导出
export type UseCounterReturn = ReturnType<typeof useCounter>
```

### 复杂Hook示例
```typescript
import { ref, watch, onUnmounted } from 'vue'
import type { Ref } from 'vue'

export interface UseRequestOptions<T> {
  /** 是否立即执行 */
  immediate?: boolean
  /** 数据转换函数 */
  transform?: (data: any) => T
  /** 错误处理函数 */
  onError?: (error: Error) => void
  /** 成功回调 */
  onSuccess?: (data: T) => void
}

/**
 * 异步请求Hook
 */
export function useRequest<T = any>(
  requestFn: () => Promise<any>,
  options: UseRequestOptions<T> = {}
) {
  const {
    immediate = false,
    transform,
    onError,
    onSuccess
  } = options
  
  // 状态管理
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)
  
  // 执行请求
  const execute = async () => {
    try {
      loading.value = true
      error.value = null
      
      const result = await requestFn()
      const transformedData = transform ? transform(result) : result
      
      data.value = transformedData
      onSuccess?.(transformedData)
      
      return transformedData
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err))
      error.value = errorObj
      onError?.(errorObj)
      throw errorObj
    } finally {
      loading.value = false
    }
  }
  
  // 取消请求
  const cancel = () => {
    loading.value = false
  }
  
  // 重置状态
  const reset = () => {
    data.value = null
    error.value = null
    loading.value = false
  }
  
  // 立即执行
  if (immediate) {
    execute()
  }
  
  // 清理
  onUnmounted(() => {
    cancel()
  })
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    execute,
    cancel,
    reset
  }
}
```

## 🎯 常用Hooks分类

### 状态管理类
```typescript
// 切换状态
export function useToggle(initialValue = false) {
  const state = ref(initialValue)
  
  const toggle = () => state.value = !state.value
  const setTrue = () => state.value = true
  const setFalse = () => state.value = false
  
  return {
    state: readonly(state),
    toggle,
    setTrue,
    setFalse
  }
}

// 本地存储
export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  options: {
    serializer?: {
      read: (value: string) => T
      write: (value: T) => string
    }
  } = {}
) {
  const { serializer = JSON } = options
  
  const storedValue = ref<T>(defaultValue)
  
  // 读取存储值
  const read = () => {
    try {
      const item = localStorage.getItem(key)
      if (item !== null) {
        storedValue.value = serializer.read(item)
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
    }
  }
  
  // 写入存储值
  const write = (value: T) => {
    try {
      storedValue.value = value
      localStorage.setItem(key, serializer.write(value))
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error)
    }
  }
  
  // 删除存储值
  const remove = () => {
    try {
      localStorage.removeItem(key)
      storedValue.value = defaultValue
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error)
    }
  }
  
  // 初始化
  read()
  
  return {
    value: storedValue,
    setValue: write,
    remove
  }
}
```

### DOM操作类
```typescript
// 点击外部区域
export function useClickOutside(
  target: Ref<HTMLElement | null>,
  callback: (event: MouseEvent) => void
) {
  const listener = (event: MouseEvent) => {
    if (!target.value || target.value.contains(event.target as Node)) {
      return
    }
    callback(event)
  }
  
  onMounted(() => {
    document.addEventListener('click', listener)
  })
  
  onUnmounted(() => {
    document.removeEventListener('click', listener)
  })
}

// 元素尺寸监听
export function useElementSize(target: Ref<HTMLElement | null>) {
  const width = ref(0)
  const height = ref(0)
  
  const update = () => {
    if (target.value) {
      width.value = target.value.offsetWidth
      height.value = target.value.offsetHeight
    }
  }
  
  let resizeObserver: ResizeObserver | null = null
  
  onMounted(() => {
    if (target.value) {
      resizeObserver = new ResizeObserver(update)
      resizeObserver.observe(target.value)
      update()
    }
  })
  
  onUnmounted(() => {
    resizeObserver?.disconnect()
  })
  
  return {
    width: readonly(width),
    height: readonly(height)
  }
}
```

### 异步处理类
```typescript
// 防抖Hook
export function useDebounce<T>(
  value: Ref<T>,
  delay: number = 300
) {
  const debouncedValue = ref<T>(value.value)
  
  watch(value, (newValue) => {
    const timer = setTimeout(() => {
      debouncedValue.value = newValue
    }, delay)
    
    return () => clearTimeout(timer)
  })
  
  return readonly(debouncedValue)
}

// 分页Hook
export function usePagination(
  total: Ref<number>,
  options: {
    pageSize?: number
    currentPage?: number
  } = {}
) {
  const { pageSize = 10, currentPage = 1 } = options
  
  const current = ref(currentPage)
  const size = ref(pageSize)
  
  const totalPages = computed(() => Math.ceil(total.value / size.value))
  const offset = computed(() => (current.value - 1) * size.value)
  
  const next = () => {
    if (current.value < totalPages.value) {
      current.value++
    }
  }
  
  const prev = () => {
    if (current.value > 1) {
      current.value--
    }
  }
  
  const goto = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      current.value = page
    }
  }
  
  return {
    current,
    size,
    total,
    totalPages,
    offset,
    next,
    prev,
    goto
  }
}
```

## 🧪 Hooks测试

### 测试环境配置
```typescript
// __tests__/setup.ts
import { createApp } from 'vue'
import { mount } from '@vue/test-utils'

// 测试组件包装器
export function renderHook<T>(hook: () => T) {
  let result: T
  
  const TestComponent = {
    setup() {
      result = hook()
      return () => null
    }
  }
  
  const wrapper = mount(TestComponent)
  
  return {
    result: result!,
    wrapper,
    unmount: () => wrapper.unmount()
  }
}
```

### Hook测试示例
```typescript
// __tests__/useCounter.test.ts
import { describe, it, expect } from 'vitest'
import { renderHook } from './setup'
import { useCounter } from '../src/useCounter'

describe('useCounter', () => {
  it('应该初始化为默认值', () => {
    const { result } = renderHook(() => useCounter())
    
    expect(result.count.value).toBe(0)
    expect(result.isAtMin.value).toBe(false)
    expect(result.isAtMax.value).toBe(false)
  })
  
  it('应该正确递增', () => {
    const { result } = renderHook(() => useCounter(0, { max: 10 }))
    
    result.increment()
    expect(result.count.value).toBe(1)
    
    // 测试边界条件
    for (let i = 0; i < 20; i++) {
      result.increment()
    }
    expect(result.count.value).toBe(10)
    expect(result.isAtMax.value).toBe(true)
  })
  
  it('应该正确递减', () => {
    const { result } = renderHook(() => useCounter(5, { min: 0 }))
    
    result.decrement()
    expect(result.count.value).toBe(4)
    
    // 测试边界条件
    for (let i = 0; i < 10; i++) {
      result.decrement()
    }
    expect(result.count.value).toBe(0)
    expect(result.isAtMin.value).toBe(true)
  })
  
  it('应该正确重置', () => {
    const { result } = renderHook(() => useCounter(5))
    
    result.increment()
    result.increment()
    expect(result.count.value).toBe(7)
    
    result.reset()
    expect(result.count.value).toBe(5)
  })
})
```

## 🔧 外部库集成

### VueUse集成
```typescript
// 使用VueUse提供的基础功能
import { 
  useVModel, 
  useElementSize, 
  useLocalStorage,
  useEventListener,
  useClipboard
} from '@vueuse/core'

// 扩展VueUse功能
export function useEnhancedClipboard() {
  const { copy, copied, isSupported } = useClipboard()
  
  const copyWithNotification = async (text: string) => {
    try {
      await copy(text)
      // 添加通知逻辑
      console.log('复制成功')
    } catch (error) {
      console.error('复制失败:', error)
    }
  }
  
  return {
    copy: copyWithNotification,
    copied,
    isSupported
  }
}
```

### 状态管理集成
```typescript
// Pinia集成示例
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isLoggedIn = computed(() => !!user.value)
  
  const login = async (credentials: LoginCredentials) => {
    // 登录逻辑
  }
  
  const logout = () => {
    user.value = null
  }
  
  return {
    user: readonly(user),
    isLoggedIn,
    login,
    logout
  }
})

// Hook包装器
export function useAuth() {
  const store = useUserStore()
  
  return {
    user: store.user,
    isLoggedIn: store.isLoggedIn,
    login: store.login,
    logout: store.logout
  }
}
```

## 📋 最佳实践

### 设计原则
1. **单一职责** - 每个Hook只负责一个功能
2. **纯函数** - 避免副作用，便于测试
3. **类型安全** - 提供完整的TypeScript类型
4. **可组合** - Hook之间可以相互组合使用

### 性能优化
```typescript
// ✅ 使用readonly防止外部修改
export function useCounter() {
  const count = ref(0)
  
  return {
    count: readonly(count),  // 防止外部直接修改
    increment: () => count.value++
  }
}

// ✅ 合理使用computed缓存计算结果
export function useExpensiveCalculation(input: Ref<number>) {
  const result = computed(() => {
    // 昂贵的计算
    return heavyCalculation(input.value)
  })
  
  return { result }
}

// ✅ 及时清理副作用
export function useTimer() {
  const count = ref(0)
  let timer: number | null = null
  
  const start = () => {
    timer = setInterval(() => {
      count.value++
    }, 1000)
  }
  
  const stop = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }
  
  onUnmounted(() => {
    stop()  // 组件卸载时清理
  })
  
  return { count, start, stop }
}
```

### 错误处理
```typescript
export function useAsyncOperation<T>(
  operation: () => Promise<T>
) {
  const data = ref<T | null>(null)
  const error = ref<Error | null>(null)
  const loading = ref(false)
  
  const execute = async () => {
    try {
      loading.value = true
      error.value = null
      
      data.value = await operation()
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      console.error('Operation failed:', err)
    } finally {
      loading.value = false
    }
  }
  
  return {
    data: readonly(data),
    error: readonly(error),
    loading: readonly(loading),
    execute
  }
}
```

## 📚 相关文档
- 组件开发: [02-components-guide.mdc](mdc:02-components-guide.mdc)
- Vue兼容性: [05-vue-compatibility.mdc](mdc:05-vue-compatibility.mdc)
- 代码规范: [04-code-standards.mdc](mdc:04-code-standards.mdc)
- 开发指南: [03-development-guide.mdc](mdc:03-development-guide.mdc)
