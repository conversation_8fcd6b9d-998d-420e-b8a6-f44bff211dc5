---
description: 
globs: 
alwaysApply: false
---
# CFE Components Plus - 构建系统指南

## 🛠️ 构建工具栈

### 核心工具
- **[Vite](mdc:https:/vitejs.dev)** - 快速构建工具和开发服务器
- **[unbuild](mdc:https:/github.com/unjs/unbuild)** - 统一的库构建工具
- **[TypeScript](mdc:https:/www.typescriptlang.org)** - 类型检查和编译
- **[Rollup](mdc:https:/rollupjs.org)** - 底层打包引擎

### 构建架构
```
构建系统架构
├── Vite (开发环境)
│   ├── HMR 热更新
│   ├── 开发服务器
│   └── Vue SFC 处理
├── unbuild (生产构建)
│   ├── 多格式输出
│   ├── TypeScript 编译
│   └── 类型声明生成
└── 配置管理
    ├── 基础配置
    ├── 包级配置
    └── 环境配置
```

## 📁 构建配置文件

### 全局配置
- **[vite.config.ts](mdc:vite.config.ts)** - 全局Vite配置文件
- **[configs/vite/base.ts](mdc:configs/vite/base.ts)** - 基础Vite配置模块
- **[configs/alias.ts](mdc:configs/alias.ts)** - 路径别名统一配置
- **[configs/utils.ts](mdc:configs/utils.ts)** - 构建工具函数

### 包级配置
每个包都有独立的构建配置：
```typescript
// packages/*/build.config.ts
import { defineBuildConfig } from 'unbuild'

export default defineBuildConfig({
  entries: [
    'src/index'  // 入口文件
  ],
  declaration: true,    // 生成类型声明
  clean: true,         // 构建前清理
  rollup: {
    emitCJS: true,     // 生成 CommonJS 格式
    inlineDependencies: true
  },
  externals: [
    'vue',             // 外部依赖
    '@vue/composition-api'
  ]
})
```

## 📦 输出格式详解

### 支持的模块格式
```json
{
  "main": "./dist/index.cjs",        // CommonJS (Node.js)
  "module": "./dist/index.mjs",      // ES Module (现代打包工具)
  "types": "./dist/index.d.ts",      // TypeScript 类型声明
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.mjs",   // ESM 导入
      "require": "./dist/index.cjs"   // CJS 导入
    }
  }
}
```

### 格式说明
- **ESM (`.mjs`)** - 现代ES模块格式，支持tree-shaking
- **CJS (`.cjs`)** - Node.js兼容格式，用于服务器端
- **IIFE (`.iife.js`)** - 浏览器全局变量格式（组件库专用）
- **Types (`.d.ts`)** - TypeScript类型声明文件

### 输出目录结构
```
dist/
├── index.cjs          # CommonJS 格式
├── index.mjs          # ES Module 格式
├── index.d.ts         # 类型声明
├── index.iife.js      # IIFE 格式 (仅组件库)
└── components/        # 子目录结构 (如适用)
    ├── button.d.ts
    └── ...
```

## 🔧 构建命令详解

### 基础构建命令
```bash
# 构建所有包
pnpm build

# 构建特定包
pnpm --filter @cfe-components/components build

# 构建并监听变化
pnpm --filter @cfe-components/components build --stub

# 清理所有构建产物
pnpm clean
```

### 高级构建命令
```bash
# 构建框架相关包
pnpm build

# 构建并执行lint检查
pnpm build:lib

# 仅生成类型声明文件
pnpm build:types

# 生产环境构建 (优化)
pnpm build:prod
```

### 构建脚本组织
```json
// package.json
{
  "scripts": {
    "build": "pnpm -r --filter=./packages/* run build",
    "build": "pnpm -r --filter=@cfe-components/{core,utils,hooks} run build",
    "build:components": "pnpm --filter=@cfe-components/components build",
    "build:lib": "pnpm build && pnpm lint",
    "build:types": "pnpm -r run build:types",
    "clean": "pnpm -r run clean"
  }
}
```

## ⚙️ 构建优化

### Rollup配置优化
```typescript
// build.config.ts 优化示例
import { defineBuildConfig } from 'unbuild'

export default defineBuildConfig({
  entries: ['src/index'],
  declaration: true,
  clean: true,
  rollup: {
    emitCJS: true,
    inlineDependencies: true,
    // 外部依赖优化
    external: [
      'vue',
      /^@vue\//,
      /^@cfe-components\//
    ],
    // 输出优化
    output: {
      globals: {
        vue: 'Vue'
      }
    }
  },
  // 替换环境变量
  replace: {
    __VERSION__: JSON.stringify(process.env.npm_package_version)
  }
})
```

### TypeScript编译优化
```json
// tsconfig.json 构建优化
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    "preserveWatchOutput": true,
    "skipLibCheck": true
  },
  "include": ["src/**/*"],
  "exclude": ["**/*.test.ts", "**/__tests__/**/*"]
}
```

## 🏗️ 构建流程

### 开发构建流程
```mermaid
graph TD
    A[源代码修改] --> B[Vite HMR]
    B --> C[实时编译]
    C --> D[浏览器更新]
    D --> E[开发调试]
    E --> A
```

### 生产构建流程
```mermaid
graph TD
    A[源代码] --> B[TypeScript编译]
    B --> C[Rollup打包]
    C --> D[格式转换]
    D --> E[代码压缩]
    E --> F[类型生成]
    F --> G[构建产物]
```

### 构建钩子
```typescript
// 自定义构建钩子示例
export default defineBuildConfig({
  entries: ['src/index'],
  hooks: {
    'build:before': async () => {
      console.log('🔧 开始构建...')
    },
    'build:done': async () => {
      console.log('✅ 构建完成！')
    }
  }
})
```

## 🎯 Vue版本适配

### 双版本构建配置
```typescript
// configs/vite/vue-config.ts
import vue from '@vitejs/plugin-vue'
import vue2 from '@vitejs/plugin-vue2'

export function createVuePlugin() {
  const isVue2 = process.env.VUE_VERSION === '2'
  
  if (isVue2) {
    return vue2({
      jsx: true,
      jsxOptions: {
        compositionAPI: true
      }
    })
  }
  
  return vue({
    jsx: true,
    script: {
      defineModel: true,
      propsDestructure: true
    }
  })
}
```

### 条件构建
```bash
# Vue 2 构建
VUE_VERSION=2 pnpm build

# Vue 3 构建  
VUE_VERSION=3 pnpm build

# 同时构建两个版本
pnpm build:dual
```

## 📋 版本管理

### 版本更新工具
项目使用 [bumpp](mdc:https:/github.com/antfu/bumpp) 进行版本管理：

```bash
# 交互式版本更新
pnpm release

# 自动升级patch版本
pnpm release patch

# 自动升级minor版本
pnpm release minor

# 自动升级major版本
pnpm release major
```

### 版本配置
```json
// package.json
{
  "scripts": {
    "release": "bumpp",
    "release:patch": "bumpp patch",
    "release:minor": "bumpp minor", 
    "release:major": "bumpp major"
  }
}
```

## 🚀 发布流程

### 自动化发布
```bash
# 1. 确保代码已提交
git status

# 2. 运行测试
pnpm test

# 3. 构建项目
pnpm build

# 4. 更新版本
pnpm release

# 5. 发布到NPM
pnpm release:publish
```

### 发布配置
```json
// .github/workflows/release.yml
name: Release
on:
  push:
    tags:
      - 'v*'
jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install pnpm
        uses: pnpm/action-setup@v2
      - name: Install dependencies
        run: pnpm install
      - name: Build
        run: pnpm build
      - name: Publish
        run: pnpm release:publish
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
```

### 发布脚本
```json
// package.json
{
  "scripts": {
    "release:beta": "pnpm publish --tag beta",
    "release:latest": "pnpm publish --tag latest",
    "release:publish": "pnpm -r publish --access public"
  }
}
```

## 🔍 构建分析

### 包大小分析
```bash
# 分析构建产物大小
npx vite-bundle-analyzer dist

# 分析依赖关系
pnpm why package-name

# 查看包内容
npm pack --dry-run
```

### 性能监控
```typescript
// 构建性能监控
export default defineBuildConfig({
  entries: ['src/index'],
  hooks: {
    'build:before': () => {
      console.time('Build Time')
    },
    'build:done': () => {
      console.timeEnd('Build Time')
    }
  }
})
```

## 🛠️ 开发工具集成

### VS Code配置
```json
// .vscode/tasks.json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Build All",
      "type": "shell", 
      "command": "pnpm build",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always"
      }
    }
  ]
}
```

### 构建缓存
```json
// .gitignore
dist/
*.tsbuildinfo
node_modules/.cache/
```

## 🚨 常见问题解决

### 构建失败排查
```bash
# 1. 清理缓存
pnpm clean
rm -rf node_modules/.cache

# 2. 重新安装依赖
rm pnpm-lock.yaml
pnpm install

# 3. 检查TypeScript配置
pnpm tsc --noEmit

# 4. 逐个包构建排查
pnpm --filter @cfe-components/utils build
```

### 类型声明问题
```bash
# 重新生成类型文件
pnpm build:types

# 检查类型导出
pnpm tsc --listFiles
```

## 📚 相关文档
- 项目概览: [01-project-overview.mdc](mdc:01-project-overview.mdc)
- 开发指南: [03-development-guide.mdc](mdc:03-development-guide.mdc)
- Vue兼容性: [05-vue-compatibility.mdc](mdc:05-vue-compatibility.mdc)
- Monorepo结构: [07-monorepo-structure.mdc](mdc:07-monorepo-structure.mdc)
