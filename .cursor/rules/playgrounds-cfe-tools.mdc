---
description: 
globs: playgrounds/cfe-tools/**/*.vue,playgrounds/cfe-tools/**/*.ts
alwaysApply: false
---
# 🛠️ CFE Tools - Tauri 桌面应用开发规则

## 📋 项目概述

**CFE Tools** 是基于 `Tauri + Vue 3 + TypeScript` 技术栈开发的跨平台桌面应用，旨在打造一个高效、易用的 CFE 开发工具集合平台。

### 🎯 项目定位
- **产品定位**: 统一的 CFE 开发工具集合平台
- **技术定位**: 现代化跨平台桌面应用
- **团队定位**: 中小型开发团队的效率工具

### 📁 项目路径
```
playgrounds/cfe-tools/
```

## 🏗️ 技术架构

### 核心技术栈
```typescript
// 前端技术栈
Vue 3           // 前端框架 (Composition API)
TypeScript      // 开发语言
Pinia          // 状态管理
Vue Router 4    // 路由管理
Naive UI       // 主要 UI 组件库
Element Plus   // 备选 UI 组件库
VueUse         // 组合式函数工具库

// 桌面应用框架
Tauri          // 跨平台桌面应用框架 (Rust 后端)

// 样式和构建
UnoCSS         // 原子化 CSS 框架
SCSS           // CSS 预处理器
Vite           // 构建工具

// 开发工具
Vitest         // 测试框架
ESLint         // 代码检查
```

### 架构分层
```
┌─────────────────────────────────────────────┐
│             表现层 (Presentation)            │
│     Components | Views | Layouts           │
├─────────────────────────────────────────────┤
│              业务层 (Business)               │
│     Services | Stores | Router             │
├─────────────────────────────────────────────┤
│              数据层 (Data)                   │
│     APIs | Storage | Cache                 │
├─────────────────────────────────────────────┤
│             基础层 (Infrastructure)          │
│     Utils | Config | Logger | Tauri        │
└─────────────────────────────────────────────┘
```

## 📂 目录结构规范

### 标准目录结构
```
src/
├── components/           # 组件目录
│   ├── common/          # 通用组件
│   ├── layout/          # 布局组件
│   └── business/        # 业务组件
├── pages/               # 页面组件
│   ├── home/           # 首页
│   ├── common/         # 通用页面
│   └── sample/         # 示例页面
├── stores/              # 状态管理
├── router/              # 路由配置
├── composables/         # 组合式函数
├── services/            # 业务服务
├── utils/               # 工具函数
├── types/               # 类型定义
├── assets/              # 静态资源
├── styles/              # 样式文件
├── tauri/               # Tauri 相关
├── storage/             # 存储相关
└── base/                # 基础应用层
```

### 文件命名规范
```typescript
// 组件文件 - PascalCase
MyComponent.vue
UserProfile.vue

// 页面文件 - PascalCase
HomePage.vue
ProjectDetail.vue

// 工具函数 - camelCase
formatUtils.ts
validationHelpers.ts

// 类型定义 - PascalCase
UserTypes.ts
ApiModels.ts

// 常量文件 - camelCase
appConstants.ts
apiEndpoints.ts
```

## 🎨 组件开发规范

### Vue 3 Composition API 标准
```vue
<template>
  <!-- 模板内容 -->
  <div class="component-container">
    <h2>{{ title }}</h2>
    <button @click="handleClick">{{ buttonText }}</button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useStore } from '@/stores'

// 🔸 Props 定义
interface Props {
  title?: string
  buttonText?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Default Title',
  buttonText: 'Click Me',
  disabled: false
})

// 🔸 Emits 定义
interface Emits {
  (e: 'click', payload: { id: string }): void
  (e: 'change', value: string): void
}

const emit = defineEmits<Emits>()

// 🔸 Reactive 数据
const loading = ref(false)
const data = ref<any[]>([])

// 🔸 Store 使用
const store = useStore()

// 🔸 Computed 属性
const processedData = computed(() => {
  return data.value.filter(item => item.active)
})

// 🔸 Methods
const handleClick = () => {
  emit('click', { id: 'example' })
}

// 🔸 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped lang="scss">
.component-container {
  @apply p-4 bg-white rounded-lg shadow-md;
  
  h2 {
    @apply text-lg font-semibold mb-4;
  }
  
  button {
    @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600;
  }
}
</style>
```

### 组件规范要点
- ✅ 使用 `<script setup>` 语法
- ✅ Props 和 Emits 必须有完整的 TypeScript 类型定义
- ✅ 使用 `withDefaults` 为 Props 提供默认值
- ✅ 组合式函数按功能分组：数据、计算属性、方法、生命周期
- ✅ 样式使用 `scoped` 作用域和 UnoCSS 类名

## 🗃️ 状态管理规范

### Pinia Store 标准模式
```typescript
// stores/projectStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Project } from '@/types/models'
import { projectApi } from '@/services/api'

export const useProjectStore = defineStore('project', () => {
  // 🔸 State
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 🔸 Getters
  const activeProjects = computed(() => 
    projects.value.filter(p => p.status === 'active')
  )

  const projectCount = computed(() => projects.value.length)

  // 🔸 Actions
  const loadProjects = async () => {
    loading.value = true
    error.value = null
    try {
      const data = await projectApi.getProjects()
      projects.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
    } finally {
      loading.value = false
    }
  }

  const addProject = (project: Project) => {
    projects.value.push(project)
  }

  const updateProject = (id: string, updates: Partial<Project>) => {
    const index = projects.value.findIndex(p => p.id === id)
    if (index !== -1) {
      projects.value[index] = { ...projects.value[index], ...updates }
    }
  }

  const removeProject = (id: string) => {
    const index = projects.value.findIndex(p => p.id === id)
    if (index !== -1) {
      projects.value.splice(index, 1)
    }
  }

  // 🔸 Reset
  const $reset = () => {
    projects.value = []
    currentProject.value = null
    loading.value = false
    error.value = null
  }

  return {
    // State (readonly)
    projects: readonly(projects),
    currentProject: readonly(currentProject),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    activeProjects,
    projectCount,
    
    // Actions
    loadProjects,
    addProject,
    updateProject,
    removeProject,
    $reset
  }
})
```

### Store 使用规范
- ✅ 使用 Composition API 风格 (`setup()` 函数)
- ✅ State 使用 `readonly()` 包装对外暴露
- ✅ 异步操作必须包含错误处理
- ✅ 提供 `$reset` 方法重置状态
- ✅ Actions 使用清晰的命名约定：`load*`, `add*`, `update*`, `remove*`

## 🌐 路由配置规范

### 路由结构
```typescript
// router/routes.ts
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/layout/AppLayout.vue'),
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/pages/home/<USER>'),
        meta: {
          title: '首页',
          icon: 'home',
          requiresAuth: false
        }
      },
      {
        path: '/projects',
        name: 'Projects',
        component: () => import('@/pages/project/ProjectList.vue'),
        meta: {
          title: '项目管理',
          icon: 'folder',
          requiresAuth: true
        }
      }
    ]
  }
]

export default routes
```

## 🎛️ Tauri 集成规范

### 前端调用后端
```typescript
// utils/tauri/commands.ts
import { invoke } from '@tauri-apps/api/tauri'

export interface TauriCommand {
  createProject: (params: CreateProjectParams) => Promise<Project>
  getProjects: () => Promise<Project[]>
  runTool: (toolId: string, args: string[]) => Promise<string>
  getSystemInfo: () => Promise<SystemInfo>
}

export const tauriCommands: TauriCommand = {
  createProject: (params) => invoke('create_project', { params }),
  getProjects: () => invoke('get_projects'),
  runTool: (toolId, args) => invoke('run_tool', { toolId, args }),
  getSystemInfo: () => invoke('get_system_info')
}
```

### 事件监听
```typescript
// utils/tauri/events.ts
import { listen } from '@tauri-apps/api/event'

export const setupTauriEvents = () => {
  // 项目状态变化事件
  listen('project-status-changed', (event) => {
    const { projectId, status } = event.payload
    // 处理项目状态变化
  })

  // 工具执行结果事件
  listen('tool-execution-result', (event) => {
    const { toolId, result, error } = event.payload
    // 处理工具执行结果
  })
}
```

## 🔧 Tauri API 使用规范

### 核心原则

**⚠️ 优先使用现有封装** - 在使用 Tauri API 或实现桌面端特有功能时，必须遵循以下查找优先级：

1. **首先查找 `tauri.md` 文档** - 项目根目录的 `playgrounds/cfe-tools/tauri.md` 包含完整的已封装方法清单
2. **检查现有封装** - 在 `src/tauri/` 目录下查找是否有现有的封装实现
3. **复用现有模式** - 如果需要新功能，参考现有封装的设计模式
4. **避免重复造轮子** - 不要直接使用原生 Tauri API，优先扩展现有封装

### 开发流程

#### 步骤 1: 查找现有功能
```bash
# 1. 查看完整功能清单
cat playgrounds/cfe-tools/tauri.md

# 2. 搜索相关功能
grep -r "你需要的功能关键词" src/tauri/

# 3. 检查相关模块
ls src/tauri/*/
```

#### 步骤 2: 使用现有封装
```typescript
// ✅ 推荐：使用现有封装
import { TauriBrowserService } from '@/tauri/browser'
import { TerminalService } from '@/tauri/utils/terminal'
import { LocalProjectScanner } from '@/tauri/services/localProjectScanner'

// 使用封装好的方法
const result = await TauriBrowserService.openUrl({ url: 'https://example.com' })
const gitResult = await TerminalService.git.getCurrentBranch(projectPath)
```

#### 步骤 3: 扩展现有模块（如需要）
```typescript
// ✅ 如果需要新功能，扩展现有模块
// 在 src/tauri/browser.ts 中添加新方法
export class TauriBrowserService implements IBrowserService {
  // ... 现有方法
  
  /**
   * 新增的桌面功能
   */
  async newDesktopFeature(options: NewFeatureOptions): Promise<ResultStatus> {
    // 实现新功能，遵循现有模式
  }
}
```

#### 步骤 4: 更新文档
当添加新的 Tauri 功能或修改现有模块时，**必须同步更新** `tauri.md` 文档：

```markdown
// 在 tauri.md 中添加新功能说明
### 新增功能
```typescript
// 新方法描述
newDesktopFeature(options: NewFeatureOptions): Promise<ResultStatus>
```
```

### 禁止的做法

❌ **直接使用原生 Tauri API**：
```typescript
// ❌ 错误：直接导入原生 API
import { open } from '@tauri-apps/plugin-dialog'
import { writeFile } from '@tauri-apps/plugin-fs'

// 应该使用封装好的方法
```

❌ **重复实现已有功能**：
```typescript
// ❌ 错误：重新实现已存在的功能
const openExternal = async (url: string) => {
  const { openUrl } = await import('@tauri-apps/plugin-opener')
  await openUrl(url)
}

// ✅ 正确：使用现有封装
import { TauriBrowserService } from '@/tauri/browser'
const result = await TauriBrowserService.openUrl({ url })
```

### 架构层级规范

遵循项目的 Tauri 模块架构层级：

```typescript
// 🎯 接口层 - 定义抽象接口
src/tauri/interface/
├── iCommon.ts         // 通用接口
└── iBrowser.ts        // 浏览器服务接口

// 🔧 服务层 - 业务逻辑实现
src/tauri/services/
├── localProjectScanner.ts  // 项目扫描
└── shellService.ts         // Shell 服务

// 💾 数据层 - 持久化存储
src/tauri/stores/
└── tauriStore.ts      // 类型安全存储

// 🛠️ 工具层 - 工具函数
src/tauri/utils/
├── terminal.ts        // 终端命令服务
└── index.ts          // 工具导出

// 🌐 实现层 - 具体功能实现
src/tauri/
├── browser.ts         // 浏览器服务
├── environment.ts     // 环境检测
└── index.ts          // 模块入口
```

### 错误处理模式

所有 Tauri 相关功能必须遵循统一的错误处理模式：

```typescript
// ✅ 统一的返回格式
interface ResultStatus<T = any> {
  status: boolean
  message?: string
  data?: T
}

// ✅ 标准错误处理
export async function tauriMethod(): Promise<ResultStatus> {
  try {
    // Tauri API 调用
    const result = await someOperation()
    return { status: true, data: result }
  } catch (error) {
    console.error('[ModuleName] 操作失败:', error)
    return { 
      status: false, 
      message: error instanceof Error ? error.message : '未知错误' 
    }
  }
}
```

### 环境兼容性

所有桌面端功能必须考虑环境兼容性：

```typescript
// ✅ 环境检测
import { isTauriEnvironment } from '@/tauri/environment'

export async function desktopFeature(): Promise<ResultStatus> {
  if (!isTauriEnvironment()) {
    return { 
      status: false, 
      message: '此功能仅在桌面环境下可用' 
    }
  }
  
  // Tauri 功能实现
}
```

### 文档维护要求

#### 强制要求
1. **功能添加**：新增任何 Tauri 相关功能时，必须同步更新 `tauri.md` 文档
2. **方法修改**：修改现有封装方法的签名或行为时，必须更新对应文档
3. **模块重构**：调整 Tauri 模块结构时，必须更新架构说明
4. **插件升级**：升级 Tauri 插件版本时，必须验证兼容性并更新文档

#### 文档更新格式
```markdown
### [模块名] ([文件路径])

#### 核心功能方法
```typescript
// 方法签名和说明
methodName(params: ParamType): Promise<ReturnType>
```

#### 使用的 Tauri 插件
- `@tauri-apps/plugin-name` - 功能描述
```

### 最佳实践

1. **模块化设计**：按功能领域组织 Tauri 封装代码
2. **类型安全**：所有方法必须有完整的 TypeScript 类型定义
3. **错误处理**：统一使用 `ResultStatus` 格式返回结果
4. **环境检测**：桌面端功能必须检测 Tauri 环境可用性
5. **文档同步**：代码变更必须同步更新 `tauri.md` 文档

### 常见问题

#### Q: 如何快速找到需要的 Tauri 功能？
A: 
1. 首先查看 `tauri.md` 文档的功能清单
2. 使用关键词搜索相关模块
3. 查看类似功能的实现模式

#### Q: 什么时候可以直接使用原生 Tauri API？
A: 
- 仅在现有封装完全无法满足需求时
- 必须先讨论是否需要扩展现有封装
- 新功能实现后必须更新 `tauri.md` 文档

#### Q: 如何确保 Tauri 功能的兼容性？
A: 
- 使用 `isTauriEnvironment()` 检测环境
- 提供降级处理或友好的错误提示
- 遵循统一的错误处理模式

## 🎨 组件样式开发规范

1、尽量少的修改组件库（element-plus）的样式
2、可以使用unocss原子css，快速布局和设置字体颜色边距，但基础的dom结构class还是需要有的

### UnoCSS 使用规范
```vue
<template>
  <!-- 使用 UnoCSS 原子类 -->
  <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
    <h2 class="text-lg font-semibold text-gray-800">标题</h2>
    <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
      按钮
    </button>
  </div>
</template>

<style scoped lang="scss">
// 复杂样式使用 SCSS
.custom-component {
  @apply relative overflow-hidden;
  
  &::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 opacity-10;
  }
  
  .nested-element {
    @apply transform transition-transform hover:scale-105;
  }
}
</style>
```

### 主题变量
```scss
// playgrounds/cfe-tools/src/styles/dark.scss
:root {
  --cfe-c-primary: #1890ff;
  --cfe-c-success: #52c41a;
  --cfe-c-warning: #faad14;
  --cfe-c-danger: #f5222d;
  --cfe-c-info: #909399;
  --cfe-c-success-hover: #85ce61;
  --cfe-c-warning-hover: #ebb563;
  --cfe-c-danger-hover: #f78989;
  --cfe-c-info-hover: #a6a9ad;
  --cfe-c-bg: #ffffff;
  --cfe-c-text: #303133;
  --cfe-c-card: #fafbfc;
  --cfe-c-card2: #f7f9ff;
  --cfe-c-card3: #ffffff;
  --cfe-c-card4: #ffffff;
  --cfe-c-border: #dcdfe6;
  --cfe-c-hover: #fafbfc;
  --cfe-c-active: #f7f9ff;
  --cfe-text-one: #323b4b;
  --cfe-text-two: #4f596a;
  --cfe-text-three: #b0b7c3;
  --cfe-c-text-one: #323b4b;
  --cfe-c-text-two: #4f596a;
  --cfe-c-text-three: #b0b7c3;
  --cfe-c-text-primary: #323b4b;
  --cfe-c-text-regular: #4f596a;
  --cfe-c-text-secondary: #b0b7c3;
  --cfe-c-text-placeholder: #a8abb2;
  --cfe-c-text-disabled: #c0c4cc;
  --cfe-space-1: 2px;
  --cfe-space-2: 4px;
  --cfe-space-3: 6px;
  --cfe-space-4: 8px;
  --cfe-space-5: 10px;
  --cfe-space-6: 12px;
  --cfe-space-8: 16px;
  --cfe-space-10: 20px;
  --cfe-font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
  --cfe-font-size: 14px;
  --cfe-line-height: 1.5;
  --cfe-radius-sm: 4px;
  --cfe-radius-md: 6px;
  --cfe-radius-lg: 8px;
  --cfe-radius-full: 9999px;
  --cfe-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --cfe-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --cfe-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --cfe-transition: all 0.2s ease-in-out;
  --cfe-z-negative: -1;
  --cfe-z-normal: 1;
  --cfe-z-sticky: 100;
  --cfe-z-drawer: 900;
  --cfe-z-modal: 1000;
  --cfe-z-popup: 1100;
  --cfe-z-toast: 1200;
  --cfe-z-tooltip: 1300;
  --cfe-font-size-xs: 12px;
  --cfe-font-size-sm: 14px;
  --cfe-font-size-md: 16px;
  --cfe-font-size-lg: 18px;
  --cfe-font-size-xl: 20px;
  --cfe-font-size-2xl: 24px;
  --cfe-line-height-tight: 1.25;
  --cfe-line-height-normal: 1.5;
  --cfe-line-height-relaxed: 1.75;
  --cfe-breakpoint-sm: 640px;
  --cfe-breakpoint-md: 768px;
  --cfe-breakpoint-lg: 1024px;
  --cfe-breakpoint-xl: 1280px;
  --cfe-scrollbar-width: 6px;
  --cfe-scrollbar-radius: 3px;
  --cfe-scrollbar-track: #dcdfe6;
  --cfe-scrollbar-thumb: #f7f9ff;
  --cfe-scrollbar-thumb-hover: #fafbfc;
}

.dark {
  --cfe-c-primary: #1890ff;
  --cfe-c-primary-hover: #1481e7;
  --cfe-c-text: #ffffffde;
  --cfe-c-bg: #313949;
  --cfe-c-bg2: #353e4e;
  --cfe-c-card: #2a3242;
  --cfe-c-card2: #353e4e;
  --cfe-c-card3: #3b465c;
  --cfe-c-card4: #353e4e;
  --cfe-c-border: #4c4d4f;
  --cfe-c-hover: var(--cfe-c-card);
  --cfe-c-active: var(--cfe-c-card2);
  --cfe-text-one: #ffffff;
  --cfe-text-two: #cecece;
  --cfe-text-three: #727272;
  --cfe-c-text-one: #ffffff;
  --cfe-c-text-two: #cecece;
  --cfe-c-text-three: #727272;
  --cfe-c-text-primary: #e5eaf3;
  --cfe-c-text-regular: #cfd3dc;
  --cfe-c-text-secondary: #a3a6ad;
  --cfe-c-text-placeholder: #8d9095;
  --cfe-c-text-disabled: #6c6e72;
  --cfe-scrollbar-width: 6px;
  --cfe-scrollbar-radius: 3px;
  --cfe-scrollbar-track: #4c4d4f;
  --cfe-scrollbar-thumb: #2d2d2d;
  --cfe-scrollbar-thumb-hover: #141414;
}

```

## 🧪 测试规范

### 组件测试
```typescript
// tests/components/MyComponent.test.ts
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import MyComponent from '@/components/MyComponent.vue'

describe('MyComponent', () => {
  it('should render correctly with props', () => {
    const wrapper = mount(MyComponent, {
      props: {
        title: 'Test Title',
        disabled: false
      }
    })
    
    expect(wrapper.find('h2').text()).toBe('Test Title')
    expect(wrapper.find('button').attributes('disabled')).toBeUndefined()
  })

  it('should emit events correctly', async () => {
    const wrapper = mount(MyComponent)
    
    await wrapper.find('button').trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
    expect(wrapper.emitted('click')?.[0]).toEqual([{ id: 'example' }])
  })
})
```

## 🔧 开发工具配置

### package.json 脚本
```json
{
  "scripts": {
    "dev:web": "vite --host --port 3000 --open",
    "dev": "vite --host",
    "build": "vite build",
    "preview": "vite preview",
    "tauri:dev": "tauri dev",
    "tauri:build": "tauri build",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "type-check": "vue-tsc --noEmit"
  }
}
```

### 开发环境要求
```bash
# 环境要求
Node.js >= 18.0.0
pnpm >= 8.0.0
Rust >= 1.70.0 (for Tauri)

# 开发工具
VS Code + Volar + Tauri + rust-analyzer
```

## 📋 编码规范

### TypeScript 规范
```typescript
// ✅ 推荐写法
interface User {
  id: string
  name: string
  email?: string
  createdAt: Date
}

const user: User = {
  id: '1',
  name: 'John Doe',
  createdAt: new Date()
}

// ✅ 函数类型注解
const processUser = (user: User): Promise<void> => {
  // 处理逻辑
}

// ✅ 泛型使用
const createApiClient = <T>(): ApiClient<T> => {
  // 实现
}
```

### Vue 组件规范
```typescript
// ✅ 推荐的组件结构顺序
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入
import { ref, computed } from 'vue'

// 2. 类型定义
interface Props { }
interface Emits { }

// 3. Props & Emits
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 4. 响应式数据
const data = ref()

// 5. 计算属性
const computed = computed(() => {})

// 6. 方法
const method = () => {}

// 7. 生命周期
onMounted(() => {})
</script>

<style scoped lang="scss">
/* 样式 */
</style>
```

## 🚀 部署与构建

### 构建命令
```bash
# 开发环境
pnpm tauri dev

# 生产构建
pnpm tauri build

# 仅前端开发
pnpm dev:web

# 代码检查
pnpm lint
pnpm type-check

# 测试
pnpm test
```

### 构建配置
```typescript
// vite.config.ts - 关键配置
export default defineConfig({
  base: VITE_PUBLIC_PATH,
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '#': resolve(__dirname, 'types'),
    },
  },
  server: {
    port: 1420,
    strictPort: true,
    host: host || false,
  },
  envPrefix: ['VITE_', 'TAURI_ENV_*'],
})
```

## 📚 关键文档

### 项目文档结构
```
design/
├── 需求文档.md           # 产品需求和功能定义
├── 技术栈.md             # 技术选型和架构设计
├── 开发规范与流程.md     # 开发流程和规范
├── 数据架构设计.md       # 数据模型和存储设计
├── 用户体验设计.md       # UI/UX 设计规范
├── 测试策略.md           # 测试策略和规范
├── 风险分析.md           # 风险识别和应对
├── 后续规划.md           # 产品发展规划
└── 开发计划.md           # 开发里程碑和计划
```

## ⚠️ 开发注意事项

### 性能优化
- ✅ 使用 `defineAsyncComponent` 懒加载大型组件
- ✅ 路由组件使用动态导入 `() => import()`
- ✅ 避免在模板中使用复杂计算，使用 computed
- ✅ 大列表使用虚拟滚动
- ✅ 图片使用适当格式和尺寸

### 安全考虑
- ✅ Tauri 命令必须验证输入参数
- ✅ 敏感操作需要用户确认
- ✅ 文件操作限制在允许的目录范围内
- ✅ 网络请求使用 HTTPS

### 用户体验
- ✅ 长时间操作显示加载状态
- ✅ 错误信息友好且可操作
- ✅ 关键操作提供撤销功能
- ✅ 支持键盘快捷键
- ✅ 响应式设计适配不同屏幕尺寸

## 🔗 相关资源

- [Vue 3 官方文档](mdc:https:/cn.vuejs.org)
- [Tauri 官方文档](mdc:https:/tauri.app)
- [Naive UI 组件库](mdc:https:/www.naiveui.com)
- [UnoCSS 文档](mdc:https:/unocss.dev)
- [Pinia 状态管理](mdc:https:/pinia.vuejs.org)
- [VueUse 工具库](mdc:https:/vueuse.org)

---

> 💡 **使用指导**: 本规则文件定义了 CFE Tools 项目的完整开发规范，请在开发过程中严格遵循这些规范，确保代码质量和项目的可维护性。
