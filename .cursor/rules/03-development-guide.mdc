---
description: 
globs: 
alwaysApply: false
---
# CFE Components Plus - 开发指南

## 🚀 环境配置

### 系统要求
- **Node.js** ≥ 16.14.0
- **pnpm** ≥ 7.0.0 (推荐使用最新版本)
- **Git** ≥ 2.20.0

### 快速开始
```bash
# 1. 克隆仓库
git clone <repository-url>
cd cfe-components-plus

# 2. 安装依赖
pnpm install

# 3. 启动开发环境 (选择一个)
pnpm play:vue3    # Vue 3 开发环境
pnpm play:vue2    # Vue 2.7 开发环境
```

## 🎮 开发环境

### Playground 环境
```bash
# Vue 3 开发环境
pnpm play:vue3
# → 启动地址: http://localhost:5173

# Vue 2.7 开发环境
pnpm play:vue2
# → 启动地址: http://localhost:5174
```

### 示例项目
```bash
# Vue 3 示例
cd examples/dev3
pnpm dev

# Vue 2 示例
cd examples/dev2
pnpm dev
```

## 📦 包开发流程

### 1. 创建新包
```bash
# 使用包模板
cp -r packages/__template packages/new-package-name

# 进入新包目录
cd packages/new-package-name

# 更新 package.json
vim package.json  # 修改包名、描述等信息
```

### 2. 包配置模板
```json
{
  "name": "@cfe-components/new-package",
  "version": "0.0.1",
  "description": "Package description",
  "type": "module",
  "main": "./dist/index.cjs",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.mjs",
      "require": "./dist/index.cjs"
    }
  },
  "files": ["dist"],
  "scripts": {
    "build": "unbuild",
    "dev": "unbuild --stub"
  }
}
```

### 3. 添加到工作区
```yaml
# pnpm-workspace.yaml
packages:
  - 'packages/*'  # 新包会自动包含
```

## 🧩 组件开发流程

### 1. 创建组件结构
```bash
# 在组件库中创建新组件
mkdir -p packages/components/src/components/new-component/src
mkdir -p packages/components/src/components/new-component/__tests__

# 创建组件文件
touch packages/components/src/components/new-component/src/new-component.vue
touch packages/components/src/components/new-component/src/types.ts
touch packages/components/src/components/new-component/index.ts
touch packages/components/src/components/new-component/__tests__/new-component.test.ts
```

### 2. 参考模板文件
- 基础结构: [packages/components/src/components/template.vue](mdc:packages/components/src/components/template.vue)
- 类型定义: 参考其他组件的 `types.ts`
- 测试用例: 参考其他组件的测试文件

### 3. 注册新组件
```typescript
// packages/components/src/components/index.ts
export * from './new-component'

// packages/components/src/index.ts
export { CfeNewComponent } from './components'
```

## 🧪 测试开发

### 测试命令
```bash
# 运行所有测试
pnpm test

# 运行特定包的测试
pnpm --filter @cfe-components/components test

# 运行测试并生成覆盖率报告
pnpm coverage

# 监听模式运行测试
pnpm test:watch
```

### 测试文件结构
```typescript
// packages/components/src/components/button/__tests__/button.test.ts
import { describe, it, expect, beforeEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { CfeButton } from '../index'

describe('CfeButton', () => {
  let wrapper: VueWrapper<any>
  
  beforeEach(() => {
    wrapper = mount(CfeButton, {
      props: {
        type: 'primary'
      }
    })
  })
  
  it('应该正确渲染', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.classes()).toContain('cfe-button')
  })
  
  it('应该响应属性变化', async () => {
    await wrapper.setProps({ type: 'danger' })
    expect(wrapper.classes()).toContain('cfe-button--danger')
  })
})
```

### 测试最佳实践
1. **完整覆盖** - 测试组件的所有公共 API
2. **边界测试** - 测试极端情况和错误处理
3. **快照测试** - 对于复杂组件使用快照测试
4. **交互测试** - 测试用户交互和事件触发

## 🔧 构建与打包

### 构建命令
```bash
# 构建所有包
pnpm build

# 构建特定包
pnpm --filter @cfe-components/components build

# 构建并检查
pnpm build:lib

# 只构建类型文件
pnpm build:types
```

### 构建配置
每个包的 `build.config.ts`:
```typescript
import { defineBuildConfig } from 'unbuild'

export default defineBuildConfig({
  entries: [
    'src/index'
  ],
  declaration: true,
  clean: true,
  rollup: {
    emitCJS: true,
    inlineDependencies: true
  }
})
```

## 📝 版本管理与发布

### 版本更新流程
```bash
# 1. 提交所有代码
git add .
git commit -m "feat: add new feature"

# 2. 更新版本号 (交互式)
pnpm release

# 3. 推送代码和标签
git push
git push --tags
```

### 发布流程
```bash
# 发布 Beta 版本
pnpm release:beta

# 发布正式版本
pnpm release:latest

# 发布到私有 registry (如果有)
pnpm publish --registry https://your-registry.com
```

### 版本管理规范
- 使用 [Semantic Versioning](mdc:https:/semver.org)
- 主版本号: 破坏性变更
- 次版本号: 新功能添加  
- 修订版本号: Bug 修复

## 🛠️ 开发工具

### VS Code 配置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "vue.codeActions.enabled": true,
  "vue.complete.casing.props": "kebab",
  "vue.complete.casing.tags": "pascal"
}
```

### 推荐插件
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- UnoCSS

### Git Hooks
项目配置了以下 Git Hooks:
- **pre-commit**: 运行 lint 检查
- **commit-msg**: 验证提交信息格式
- **pre-push**: 运行测试

## 🐛 调试技巧

### 组件调试
```vue
<template>
  <div>
    <!-- 使用 Vue Devtools 调试 -->
    <pre>{{ $data }}</pre>
  </div>
</template>

<script setup lang="ts">
// 使用 console.log 调试
console.log('组件加载', props)

// 使用 debugger 断点
const handleClick = () => {
  debugger
  // 处理逻辑
}
</script>
```

### 测试调试
```typescript
import { describe, it, expect } from 'vitest'

describe('Debug Test', () => {
  it('should debug', () => {
    // 设置断点调试
    debugger
    
    // 或使用 console.log
    console.log('Debug info:', someVariable)
    
    expect(true).toBe(true)
  })
})
```

## 🚨 常见问题解决

### 依赖安装问题
```bash
# 清理依赖缓存
pnpm store prune

# 重新安装依赖
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

### 构建问题
```bash
# 清理构建缓存
pnpm clean

# 重新构建
pnpm build
```

### 类型检查问题
```bash
# 重新生成类型文件
pnpm build:types

# 检查 TypeScript 配置
pnpm tsc --noEmit
```

## 📚 相关文档
- 项目概览: [01-project-overview.mdc](mdc:01-project-overview.mdc)
- 组件开发: [02-components-guide.mdc](mdc:02-components-guide.mdc)
- 代码规范: [04-code-standards.mdc](mdc:04-code-standards.mdc)
- 构建系统: [06-build-system.mdc](mdc:06-build-system.mdc)
