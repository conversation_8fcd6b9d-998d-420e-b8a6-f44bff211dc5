---
description: 
globs: 
alwaysApply: false
---
# CFE Components Plus - Vue兼容性指南

## 🎯 兼容性目标

### 支持的Vue版本
- **Vue 2.7.x** - 支持Composition API的Vue2最新版本
- **Vue 3.x** - 完整的Vue3生态支持

### 兼容性策略
本项目通过以下方式实现Vue2/3双版本兼容：
- 🔄 **统一API**: 使用Composition API作为主要开发模式
- 🏗️ **构建分离**: 针对不同版本的独立构建配置
- 🧪 **分环境测试**: 在两个版本下分别进行测试验证
- 📦 **智能打包**: 自动检测目标环境并应用对应的构建策略

## 🏗️ 构建系统兼容性

### 构建配置
项目使用不同的构建插件处理Vue版本差异：

```typescript
// configs/vite/base.ts
import vue from '@vitejs/plugin-vue'          // Vue 3
import vue2 from '@vitejs/plugin-vue2'        // Vue 2.7

export function createVuePlugin(isVue2 = false) {
  if (isVue2) {
    return vue2({
      // Vue 2.7 特定配置
      jsx: true,
      jsxOptions: {
        compositionAPI: true
      }
    })
  }
  
  return vue({
    // Vue 3 配置
    jsx: true,
    script: {
      defineModel: true,
      propsDestructure: true
    }
  })
}
```

### 环境变量控制
```bash
# Vue 3 环境
VUE_VERSION=3 pnpm build

# Vue 2.7 环境  
VUE_VERSION=2 pnpm build
```

## 🧩 组件API兼容性

### 推荐的组件写法
```vue
<template>
  <div class="cfe-component">
    <!-- 使用通用的模板语法 -->
    <slot name="header" />
    <div class="cfe-component__content">
      <slot />
    </div>
    <slot name="footer" />
  </div>
</template>

<script setup lang="ts">
// ✅ 推荐 - Vue 2/3 通用API
import { computed, ref, watch, onMounted } from 'vue'
import type { ComponentProps } from './types'

// 组件选项 (Vue 3 语法，Vue 2.7 兼容)
defineOptions({
  name: 'CfeComponent',
  inheritAttrs: false
})

// Props 定义 (两个版本通用)
const props = withDefaults(defineProps<ComponentProps>(), {
  title: '',
  disabled: false
})

// Events 定义 (两个版本通用) 
const emit = defineEmits<{
  change: [value: string]
  click: [event: MouseEvent]
}>()

// 响应式数据 (两个版本通用)
const isActive = ref(false)
const computedValue = computed(() => {
  return props.title.toUpperCase()
})

// 监听器 (两个版本通用)
watch(() => props.disabled, (newVal) => {
  if (newVal) {
    isActive.value = false
  }
})

// 生命周期 (两个版本通用)
onMounted(() => {
  console.log('Component mounted')
})

// 方法定义
const handleClick = (event: MouseEvent) => {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>
```

### Props和Events兼容性
```typescript
// ✅ 推荐 - 使用interface定义Props
export interface ComponentProps {
  title?: string
  disabled?: boolean
  size?: 'small' | 'medium' | 'large'
  modelValue?: string  // v-model支持
}

// ✅ 推荐 - Events类型定义
export interface ComponentEmits {
  /** v-model更新事件 */
  (e: 'update:modelValue', value: string): void
  /** 点击事件 */
  (e: 'click', event: MouseEvent): void
}

// 组件中的使用
const props = withDefaults(defineProps<ComponentProps>(), {
  title: '',
  disabled: false,
  size: 'medium',
  modelValue: ''
})

const emit = defineEmits<ComponentEmits>()
```

### v-model兼容性处理
```vue
<script setup lang="ts">
// ✅ Vue 2/3 兼容的v-model实现
const props = defineProps<{
  modelValue?: string  // Vue 3 标准
  value?: string       // Vue 2 兼容
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]  // Vue 3
  'input': [value: string]             // Vue 2 兼容
}>()

// 统一的内部值处理
const internalValue = computed({
  get: () => props.modelValue ?? props.value ?? '',
  set: (value: string) => {
    // 同时触发两种事件，确保兼容性
    emit('update:modelValue', value)
    emit('input', value)
  }
})
</script>
```

## 🎣 Composition API兼容性

### 生命周期钩子映射
```typescript
// ✅ 推荐 - 使用Composition API生命周期
import { 
  onMounted,        // mounted
  onUnmounted,      // unmounted/beforeDestroy
  onUpdated,        // updated
  onBeforeMount,    // beforeMount  
  onBeforeUnmount,  // beforeUnmount/beforeDestroy
  onBeforeUpdate    // beforeUpdate
} from 'vue'

// Vue 2/3 通用的生命周期使用
export function useLifecycle() {
  onMounted(() => {
    console.log('组件已挂载')
  })
  
  onBeforeUnmount(() => {
    console.log('组件即将卸载')
  })
}
```

### 响应式API最佳实践
```typescript
// ✅ 推荐的响应式API使用
import { ref, reactive, computed, watch, watchEffect } from 'vue'

export function useReactiveData() {
  // 基础类型使用ref
  const count = ref(0)
  const isLoading = ref(false)
  
  // 对象类型可以使用reactive
  const user = reactive({
    name: '',
    age: 0
  })
  
  // 计算属性
  const doubleCount = computed(() => count.value * 2)
  
  // 监听器
  watch(count, (newVal, oldVal) => {
    console.log(`Count changed from ${oldVal} to ${newVal}`)
  })
  
  // 立即执行的监听器
  watchEffect(() => {
    if (count.value > 10) {
      isLoading.value = true
    }
  })
  
  return {
    count,
    isLoading,
    user,
    doubleCount
  }
}
```

### 组合函数兼容性
```typescript
// ✅ Vue 2/3 兼容的组合函数
export function useToggle(initialValue = false) {
  const state = ref(initialValue)
  
  const toggle = () => {
    state.value = !state.value
  }
  
  const setTrue = () => {
    state.value = true
  }
  
  const setFalse = () => {
    state.value = false
  }
  
  return {
    state: readonly(state),  // 只读状态
    toggle,
    setTrue,
    setFalse
  }
}
```

## 🚫 避免使用的特性

### Vue版本特有API
```typescript
// ❌ Vue 3 特有 - 避免在组件中使用
import { defineCustomElement, Teleport } from 'vue'

// ❌ Vue 2 特有 - 已废弃的API
// this.$scopedSlots
// this.$listeners
// 过滤器 (filters)
// .sync修饰符
```

### 模板语法差异
```vue
<template>
  <!-- ❌ Vue 2 特有语法 - 避免使用 -->
  <div :class="{ active: isActive }" v-on="$listeners">
    {{ message | capitalize }}  <!-- 过滤器 -->
  </div>
  
  <!-- ❌ Vue 3 特有语法 - 在Vue 2中不兼容 -->
  <Teleport to="body">
    <div>传送内容</div>
  </Teleport>
  
  <!-- ✅ 通用语法 - 推荐使用 -->
  <div :class="{ active: isActive }" @click="handleClick">
    {{ capitalizedMessage }}  <!-- 使用计算属性 -->
  </div>
</template>
```

### 事件处理兼容性
```vue
<script setup lang="ts">
// ✅ 推荐 - 统一的事件处理
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
}>()

// 统一的事件触发方式
const handleChange = (value: string) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// ❌ 避免 - 依赖特定版本的事件处理
// Vue 2: this.$emit('input', value)
// Vue 3: emit('update:modelValue', value)
</script>
```

## 🧪 测试兼容性

### 测试环境配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import vue2 from '@vitejs/plugin-vue2'

const isVue2 = process.env.VUE_VERSION === '2'

export default defineConfig({
  plugins: [
    isVue2 ? vue2() : vue()
  ],
  test: {
    environment: 'jsdom',
    globals: true
  }
})
```

### 测试工具兼容性
```typescript
// tests/utils.ts
import { mount } from '@vue/test-utils'
import type { ComponentPublicInstance } from 'vue'

// 通用的测试挂载函数
export function createWrapper<T extends ComponentPublicInstance>(
  component: any,
  options: any = {}
) {
  return mount(component, {
    ...options,
    global: {
      // Vue 2/3 通用配置
      stubs: {
        transition: false,
        'router-link': true
      },
      ...options.global
    }
  })
}

// 兼容性测试辅助函数
export function isVue2() {
  return process.env.VUE_VERSION === '2'
}

export function isVue3() {
  return !isVue2()
}
```

### 跨版本测试示例
```typescript
// component.test.ts
import { describe, it, expect } from 'vitest'
import { createWrapper, isVue2, isVue3 } from '../utils'
import { CfeButton } from '../button'

describe('CfeButton - Vue兼容性', () => {
  it('应该在Vue 2和Vue 3中正确渲染', () => {
    const wrapper = createWrapper(CfeButton, {
      props: {
        type: 'primary'
      }
    })
    
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.classes()).toContain('cfe-button--primary')
  })
  
  it('应该正确处理v-model', async () => {
    const wrapper = createWrapper(CfeButton, {
      props: {
        modelValue: 'test'
      }
    })
    
    // 在Vue 2中也会触发update:modelValue事件
    await wrapper.vm.$emit('update:modelValue', 'new-value')
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })
  
  // 版本特定测试
  if (isVue3()) {
    it('Vue 3 特定功能测试', () => {
      // Vue 3 特有功能的测试
    })
  }
  
  if (isVue2()) {
    it('Vue 2 特定功能测试', () => {
      // Vue 2 特有功能的测试
    })
  }
})
```

## 🛠️ 开发调试

### 调试环境设置
```bash
# Vue 3 调试环境
pnpm play:vue3

# Vue 2.7 调试环境
pnpm play:vue2

# 构建测试
VUE_VERSION=2 pnpm build  # Vue 2构建
VUE_VERSION=3 pnpm build  # Vue 3构建
```

### 兼容性检查清单
在开发组件时，请确保：

- [ ] 使用Composition API语法
- [ ] 避免使用版本特有API
- [ ] Props和Events类型明确定义
- [ ] v-model兼容性处理
- [ ] 在两个版本环境中测试
- [ ] 生命周期钩子使用组合式API
- [ ] 模板语法保持通用性

## 📚 相关文档
- 组件开发: [02-components-guide.mdc](mdc:02-components-guide.mdc)
- 代码规范: [04-code-standards.mdc](mdc:04-code-standards.mdc)
- Hooks指南: [08-hooks-and-composables.mdc](mdc:08-hooks-and-composables.mdc)
- 开发指南: [03-development-guide.mdc](mdc:03-development-guide.mdc)
