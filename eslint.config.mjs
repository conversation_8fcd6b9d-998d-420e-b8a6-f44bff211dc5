/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-07-11 08:19:53
 * @LastEditTime: 2025-07-11 09:16:18
 * @LastEditors: shaojun
 * @Description:
 */

import antfu from '@antfu/eslint-config'

const ignoresFiles = ['unocss.config.ts', 'vite.config.ts', '**/*-base64.scss', '*.html', '**/*.md', '*.md', '*.yml', '*.yaml', '*.txt']
const ignoresDirs = ['examples', 'playgrounds', 'node_modules', '.pnpm', '.pnpm-store', '.node-modules-inspector', '.vscode', '.idea', 'dist', 'public', 'docs', '.husky', '.local', '**/src/api/**']

// [antfu:为什么我不使用 Prettier](https://antfu.me/posts/why-not-prettier-zh)
// [Eslint configuration-objects]https://eslint.org/docs/latest/use/configure/configuration-files-new#configuration-objects

export default antfu(
  {
    ignores: [...ignoresFiles, ...ignoresDirs],
    formatters: true,
    unocss: true,
    vue: true,
    typescript: true,
    // [eslint stylistic](https://eslint.style/)
    stylistic: {
      indent: 2, // 缩进
      quotes: 'single', // 单引号
      semi: false, // 尾分号
      // arrowParens: true // 无效
      // commaDangle: 'never', // 尾逗号无效
      // printWidth: 150, // 无效
    },
  },
  {
    files: ['**/*.ts', '**/*.mts', '**/*.mjs', '**/*.cjs', '**/*.js', '**/*.jsx', '**/*.tsx'],
    rules: {

    },
  },
  {
    files: ['**/*.vue'], // vue文件
    rules: {
      'vue/no-unused-vars': 'off',
      'vue/no-unused-refs': 'off',
      'vue/custom-event-name-casing': 'off', // 自定义事件名大小写 不限制
    },
  },
  {
    files: ['packages/global/**/*.vue', 'packages/ui/**/*.vue'], // 全局组件和ui(vue2)组件
    rules: {
      'vue/custom-event-name-casing': 'off', // 自定义事件名大小写 不限制
      'vue/no-deprecated-v-bind-sync': 'off', // v-bind.sync vue3已经废弃
      'vue/require-v-for-key': 'off', // v-for 需要key vue2 vue3 规则有冲突https://v3-migration.vuejs.org/breaking-changes/key-attribute.html#with-template-v-for
      'vue/no-v-for-template-key-on-child': 'off', // v-for 模板中的 key 不需要
    },
  },
  {
    rules: {
      'curly': ['error', 'all'], // if else while for do while 代码块必须使用大括号
      'no-alert': 'warn',
      'no-console': 'off',
      'no-undef': 'off',
      'no-unused-vars': 'off',
      'no-restricted-imports': 'off',
      'import/first': 'off',
      'unused-imports/no-unused-vars': 'off',
      'ts/no-unused-vars': 'off',
      'ts/no-redeclare': 'off',
      'ts/no-use-before-define': 'off',
      'jsdoc/check-param-names': 'off',
      'jsdoc/require-returns-description': 'off',
      'antfu/top-level-function': 'off', // 顶级函数使用function声明https://github.com/antfu/eslint-plugin-antfu/tree/main/src/rules
      // 'style/comma-dangle': ['error', 'never'],
      'style/max-len': ['warn', { code: 150, ignoreUrls: true, ignoreStrings: true, ignoreTemplateLiterals: true, ignoreRegExpLiterals: true, ignoreComments: true }],
      'style/template-curly-spacing': ['error', 'never'], // 模板字符串空格 never: `hello, ${name}`, always: `hello, ${ name }`
      'style/object-curly-spacing': ['error', 'always'], // 对象解构空格 never: {a: 1}, always: { a: 1 }
      'style/arrow-parens': ['error', 'always'], // 箭头函数参数括号 always: (x) => x, avoid: x => x
      'style/brace-style': ['error', '1tbs', { allowSingleLine: false }], // 大括号风格 1tbs 必须使用{}并且换行
    },
  },
)
