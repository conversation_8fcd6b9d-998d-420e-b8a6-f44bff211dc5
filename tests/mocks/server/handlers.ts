// https://mswjs.io/
import { http, HttpResponse } from 'msw'

// 创建一个快速返回响应结果的函数
export const commonResponse = (code?: number, message?: string, data?: any) => {
  return {
    resultCode: code,
    resultMessage: message,
    data,
  }
}
// mock data
export const posts = [
  {
    userId: 1,
    id: 1,
    title: 'first post title',
    body: 'first post body',
  },
  {
    userId: 2,
    id: 5,
    title: 'second post title',
    body: 'second post body',
  },
]

export const comments = [
  {
    userId: 1,
    id: 1,
    content: 'first content',
  },
  {
    userId: 2,
    id: 2,
    content: 'second content',
  },
  {
    userId: 3,
    id: 3,
    content: 'third content',
  },
]

export const handlers = [
  http.get('http://localhost:4430/get301', () => {
    return new HttpResponse(null, { status: 301 })
  }),
  http.get('http://localhost:4430/get401', () => {
    return new HttpResponse(null, { status: 401 })
  }),
  http.get('http://localhost:4430/get403', () => {
    return new HttpResponse(null, { status: 403 })
  }),
  http.post('http://localhost:4430/postUsers', () => {
    return HttpResponse.json(commonResponse(200, '', posts))
  }),
  http.post('http://localhost:4430/postUsersCodeUndefined', () => {
    return HttpResponse.json(commonResponse(undefined, '', posts))
  }),
  http.post('http://localhost:4430/postUsersCode201', () => {
    return HttpResponse.json(commonResponse(201, '', posts))
  }),
  http.get('http://localhost:4430/getComments', () => {
    return HttpResponse.json(commonResponse(200, '', comments))
  }),
  http.get('/getComments', () => {
    return HttpResponse.json(commonResponse(200, '', comments))
  }),
  http.get('https://mdemo.cjiatest.com/getComments', () => {
    return HttpResponse.json(commonResponse(200, '', comments))
  }),
  http.options('https://mdemo.cjiatest.com/getComments', () => {
    return new HttpResponse(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': '*',
      },
    })
  }),
  http.post('http://localhost:4430/getComments', () => {
    return new HttpResponse(JSON.stringify({
      errorCode: 999,
      errorMessage: 'Method not allowed',
      resultCode: 999,
    }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    })
  }),
  http.get('http://localhost:4430/postUsers', () => {
    return new HttpResponse(JSON.stringify({
      errorCode: 999,
      errorMessage: 'Method not allowed',
      resultCode: 999,
    }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    })
  }),
  http.get('https://m.cjia.com/123', () => {
    return new HttpResponse(JSON.stringify({
      errorCode: 999,
      errorMessage: 'Not found',
      resultCode: 999,
    }), {
      status: 404,
      headers: { 'Content-Type': 'application/json' },
    })
  }),
  http.options('https://m.cjia.com/123', () => {
    return new HttpResponse(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': '*',
      },
    })
  }),
  // add more...
]
