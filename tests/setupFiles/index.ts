import process from 'node:process'
import { afterAll, afterEach, beforeAll } from 'vitest'
import { server } from '../mocks/server'
import 'cross-fetch/polyfill'
import 'reflect-metadata'
import '../mocks/global'

// 设置服务器 - 条件性启用MSW
const setupServer = () => {
  // 配置服务 onUnhandleRequest: 'error' 只要产生了没有相应类型的请求处理，就会发生错误。
  // 在所有测试之前启动服务器
  beforeAll(() => server.listen({ onUnhandledRequest: 'warn' }))

  // 所有测试后关闭服务器
  afterAll(() => server.close())

  // 每次测试后重置处理程序"对测试隔离很重要"
  afterEach(() => server.resetHandlers())
}

// 检查是否应该启用MSW
const shouldEnableMSW = () => {
  // 优先检查测试文件级别的环境变量设置
  if (process.env.DISABLE_MSW === 'true') {
    console.log('📌 MSW disabled by DISABLE_MSW environment variable')
    return false
  }

  // 检查是否有启用MSW的明确设置
  if (process.env.ENABLE_MSW === 'true') {
    console.log('📌 MSW enabled by ENABLE_MSW environment variable')
    return true
  }

  // 检查测试文件路径，对于特定测试禁用MSW
  if (process.env.VITEST_POOL_ID) {
    const testPath = globalThis.__vitest_worker__?.filepath || ''

    // 如果是koa-swagger-decorator的集成测试，默认禁用MSW
    if (testPath.includes('koa-swagger-decorator') && testPath.includes('integration')) {
      console.log('🔄 Auto-disabling MSW for koa-swagger-decorator integration tests')
      return false
    }

    // 如果是其他需要真实HTTP请求的测试
    if (testPath.includes('e2e') || testPath.includes('integration')) {
      console.log('🔄 Auto-disabling MSW for e2e/integration tests')
      return false
    }
  }

  // 默认启用MSW
  return true
}

// 条件性启用MSW
if (shouldEnableMSW()) {
  console.log('🚀 Enabling MSW for tests')
  setupServer()
} else {
  console.log('⏭️  Skipping MSW setup - real HTTP requests allowed')
}

// 导出MSW控制工具，供测试文件使用
export const mswControl = {
  /**
   * 在测试文件中禁用MSW
   * @example
   * ```typescript
   * import { mswControl } from '../../../tests/setupFiles';
   * mswControl.disable();
   * ```
   */
  disable: () => {
    process.env.DISABLE_MSW = 'true'
    console.log('🔴 MSW disabled for current test file')
  },

  /**
   * 在测试文件中启用MSW
   * @example
   * ```typescript
   * import { mswControl } from '../../../tests/setupFiles';
   * mswControl.enable();
   * ```
   */
  enable: () => {
    process.env.ENABLE_MSW = 'true'
    console.log('🟢 MSW enabled for current test file')
  },

  /**
   * 获取MSW状态
   */
  getStatus: () => {
    return {
      disabled: process.env.DISABLE_MSW === 'true',
      enabled: process.env.ENABLE_MSW === 'true',
      auto: !process.env.DISABLE_MSW && !process.env.ENABLE_MSW,
    }
  },
}
