import { defineConfig } from 'vitepress'

export default defineConfig({
  title: 'CFE Node Server',
  description: '基于 Node.js + Koa + TypeScript + TypeORM + MySQL 的后端服务框架',
  
  themeConfig: {
    nav: [
      { text: '指南', link: '/guide/' },
      { text: 'API', link: '/api/' },
      { text: '配置', link: '/config/' },
      { text: '部署', link: '/deployment/' }
    ],

    sidebar: {
      '/guide/': [
        {
          text: '介绍',
          items: [
            { text: '快速开始', link: '/guide/getting-started' },
            { text: '项目结构', link: '/guide/project-structure' }
          ]
        },
        {
          text: '基础',
          items: [
            { text: '路由', link: '/guide/routing' },
            { text: '控制器', link: '/guide/controllers' },
            { text: '中间件', link: '/guide/middleware' }
          ]
        },
        {
          text: '进阶',
          items: [
            { text: '数据库', link: '/guide/database' },
            { text: 'Redis 缓存', link: '/guide/redis' },
            { text: '认证授权', link: '/guide/auth' },
            { text: '日志', link: '/guide/logging' },
            { text: '测试', link: '/guide/testing' }
          ]
        }
      ],
      '/api/': [
        {
          text: 'API 参考',
          items: [
            { text: '响应格式', link: '/api/response' },
            { text: '错误处理', link: '/api/errors' },
            { text: '认证', link: '/api/auth' }
          ]
        }
      ]
    },

    socialLinks: [
      { icon: 'github', link: 'https://github.com/your-repo/cfe-node' }
    ]
  }
}) 
