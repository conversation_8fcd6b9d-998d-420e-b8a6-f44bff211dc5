---
layout: home

hero:
  name: CFE Node Server
  text: 基于 Node.js + Koa + TypeScript 的后端服务框架
  tagline: 简单、高效、可扩展的后端开发框架
  actions:
    - theme: brand
      text: 快速开始
      link: /guide/getting-started
    - theme: alt
      text: 在 GitHub 上查看
      link: https://github.com/your-repo/cfe-node

features:
  - icon: 🚀
    title: 高性能
    details: 基于 Node.js 和 Koa2，提供高性能的请求处理能力
  - icon: 🛠️
    title: TypeScript
    details: 使用 TypeScript 开发，提供完整的类型支持
  - icon: 📦
    title: 开箱即用
    details: 集成常用功能，包括数据库、缓存、认证等
  - icon: 🔧
    title: 易扩展
    details: 模块化设计，易于扩展和维护
--- 
