import { SwaggerRouter, z } from '@cfe-node/koa-swagger-decorator'
import { ServerPublicRoot } from '@cfe-node/utils'
import { globalSchemas } from '@/controllers/utils/global'
import { GlobalSwaggerController } from '../controllers/global.controller'
import { HealthController } from '../controllers/health.controller'
import { UserController } from '../controllers/user.controller'

// 测试 Zod 扩展是否正常工作
console.log('🔍 [DEBUG] 测试 Zod 扩展:')
const testSchema = z.string()
console.log('🔍 [DEBUG] testSchema 有 openapi 方法:', typeof testSchema.openapi === 'function')
console.log('🔍 [DEBUG] globalSchemas.AppInfo 有 openapi 方法:', typeof globalSchemas.AppInfo?.openapi === 'function')
if (typeof testSchema.openapi === 'function') {
  try {
    const result = testSchema.openapi({ description: 'test' })
    console.log('🔍 [DEBUG] testSchema.openapi() 调用成功:', !!result)
  } catch (error) {
    console.log('🔍 [DEBUG] testSchema.openapi() 调用失败:', error instanceof Error ? error.message : String(error))
  }
}

// 创建 SwaggerRouter 实例，用于自动生成 API 文档
const router = new SwaggerRouter(
  {
    spec: {
      info: {
        title: 'CFE Node Server API',
        description: '基于 Node.js + Koa + TypeScript 的后端服务框架',
        version: '1.0.0',
      },
      // 配置 Bearer Token 认证
      securityDefinitions: {
        Bearer: {
          type: 'apiKey',
          in: 'header',
          name: 'Authorization',
        },
      },
    },
    // 配置 Swagger 文档访问路径
    swaggerJsonEndpoint: '/swagger.json',
    swaggerHtmlEndpoint: '/swagger',
    swaggerUIConfig: {
      routePrefix: '/swagger',
      title: 'CFE Node Server API',
      hideTopbar: true,
      swaggerOptions: {
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        docExpansion: 'none',
        persistAuthorization: true,
        defaultModelRendering: 'schema',
      },
    },
    globalSchemas,
  },
  {
    // API 路由前缀
    prefix: '/api',
  },
)

// 注册控制器，自动扫描装饰器生成路由
router.applyRoute(GlobalSwaggerController)
router.applyRoute(UserController)
router.applyRoute(HealthController)

// 生成 Swagger 文档规范
router.swagger()

// 将 swagger.json 导出到 public 目录，供 UI 使用
router.exportSwaggerJson({ dir: ServerPublicRoot })

export { router }
