/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-12-19 09:30:17
 * @LastEditTime: 2025-07-15 16:14:13
 * @LastEditors: shaojun
 * @Description:
 */

import type { IEnv } from './env'
import { resolve } from 'node:path'

import process from 'node:process'
import { projRoot } from '@cfe-node/utils'
import { env } from './env'

export * from './database'
export * from './env'
export * from './redis'

export const boolConfig = (str: string): boolean => {
  return str === 'true'
}
export interface ConfigScheme {
  isDebug: boolean
  env: IEnv
  baseUrl: string
  serverUrl: string
  // 服务端配置
  server: {
    port: string
    apiPrefix: string
    uploadPrefix: string
    keyApiToken: string
    rootDir: string
    fileDir: string
    logDir: string
    secret: string
  }
  // 数据库配置
  db: {
    host: string
    port: string
    user: string
    password: string
    synchronize: boolean
    logging: boolean
  }
  jwt: {
    secret: string
    expiresIn: string
  }
  redis: {
    host: string
    port: string
    password: string
    db: number
  }
  email: {
    emailService: string
    emailUser: string
    emailPass: string
    emailPort: string
  }
  ldap: {
    openLdap: boolean
    ldapServer: string
    ldapUserDn: string
    ldapBindCredentials: string
    ldapBase: string
  }
  allowRegister: boolean
}
console.log('env project root xxxxxx', projRoot)
const staticRootDir = resolve(projRoot, 'static')
const localFileDir = resolve(staticRootDir, 'uploads')
const localLogDir = resolve(staticRootDir, 'logs')

const baseUrl = process.env.BASE_URL || `http://localhost:${env.CLIENT_PORT}`
const serverUrl = process.env.SERVER_URL || `${env.HOST}:${env.PORT}`

const config: ConfigScheme = {
  env,
  isDebug: env.NODE_ENV === 'development',
  baseUrl,
  serverUrl,
  server: {
    port: env.PORT.toString(),
    apiPrefix: env.API_PREFIX,
    uploadPrefix: '/upload',
    keyApiToken: 'apiToken',
    rootDir: `${projRoot}/`,
    fileDir: process.env.FABU_UPLOAD_DIR || localFileDir,
    logDir: process.env.FABU_LOG_DIR || localLogDir,
    secret: process.env.FABU_SECRET || 'xxxabc123',
  },
  db: {
    host: env.DB_HOST,
    port: env.DB_PORT.toString(),
    user: env.DB_USER,
    password: env.DB_PASSWORD,
    synchronize: env.DB_SYNC,
    logging: env.DB_LOGGING,
  },
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
  },
  redis: {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT.toString(),
    password: env.REDIS_PASSWORD,
    db: env.REDIS_DB,
  },
  email: {
    emailService: process.env.FABU_EMAIL_SERVICE || 'imap.exmail.qq.com', // 邮件相关配置 用于找回密码和邀请团队成员发送邮件
    emailUser: process.env.FABU_EMAIL_USER || '<EMAIL>',
    emailPass: process.env.FABU_EMAIL_PASS || 'Qq201600',
    emailPort: process.env.FABU_EMAIL_PORT || '465',
  },
  ldap: {
    openLdap: boolConfig(process.env.FABU_ALLOW_LDAP || 'false'), // 是否开启ldap 默认是false 如果公司没有ldap服务可以不用理会
    ldapServer: process.env.FABU_LDAP_URL || '', // ldap server url
    ldapUserDn: process.env.FABU_LDAP_USERDN || '', // ldap管理员dn 也就是管理员用户名
    ldapBindCredentials: process.env.FABU_LDAP_CREDENTIALS || '', // ldap管理员密码
    ldapBase: process.env.FABU_LDAP_BASE || '', // ldap base
  },
  allowRegister: true,
}

export default config
