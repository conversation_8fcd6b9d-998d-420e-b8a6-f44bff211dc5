import { dirname, join } from 'node:path'
import { fileURLToPath } from 'node:url'
import { DataSource } from 'typeorm'
import { entities } from '../typeorm/entity'
import { env } from './env'

const __dirname = dirname(fileURLToPath(import.meta.url))

// 开发环境和生产环境的不同配置
const isDevelopment = env.NODE_ENV === 'development'
const isProduction = env.NODE_ENV === 'production'

export const AppDataSource = new DataSource({
  type: 'mysql',
  host: env.DB_HOST,
  port: env.DB_PORT,
  username: env.DB_USER,
  password: env.DB_PASSWORD,
  database: env.DB_NAME,

  // 开发环境配置
  synchronize: isDevelopment ? (env.DB_SYNC !== false) : env.DB_SYNC, // 开发环境默认同步
  logging: isDevelopment ? ['error', 'warn'] : (env.DB_LOGGING ? ['error', 'warn'] : false),

  // 直接使用实体数组而不是文件路径
  entities,

  // 迁移配置 - 生产环境使用
  migrations: [join(__dirname, '../typeorm/migration/**/*.{ts,js,mjs}')],
  migrationsRun: isProduction, // 生产环境自动运行迁移

  // TypeORM 配置
  entitySkipConstructor: true,

  // 连接池配置
  extra: {
    connectionLimit: isDevelopment ? 5 : 10,
    acquireTimeout: 10000,
    timeout: 10000,
    reconnect: true,
  },

  // MySQL 特定配置
  charset: 'utf8mb4',
  timezone: '+08:00', // 东八区时间

  // 性能优化
  cache: env.REDIS_ENABLED ? {
    duration: 30000, // 缓存 30 秒
    type: 'redis' as const,
    options: {
      host: env.REDIS_HOST,
      port: env.REDIS_PORT,
      password: env.REDIS_PASSWORD,
      db: env.REDIS_DB,
    },
  } : false,

  // 连接配置
  connectTimeout: 10000,
  acquireTimeout: 10000,

  // 开发环境启用更多调试信息
  ...(isDevelopment && {
    dropSchema: false, // 开发环境不要删除现有架构
    maxQueryExecutionTime: 2000, // 慢查询警告
  }),
})

const MAX_RETRIES = 5
const RETRY_DELAY = 5000

export const setupConnection = async (retryCount = 0): Promise<void> => {
  try {
    if (AppDataSource.isInitialized) {
      console.log('✅ Database already connected')
      return
    }

    console.log('🔄 Database connection initializing...')

    await AppDataSource.initialize()

    console.log('✅ Database connection established successfully!')
    console.log(`📋 Connected to ${env.DB_NAME} with ${entities.length} entities`)

    // 开发环境下的表格验证已简化，如需详细信息请查看数据库管理工具
  } catch (error) {
    if (error instanceof Error && error.name === 'CannotConnectAlreadyConnectedError') {
      console.log('✅ Database already connected')
      return
    }

    console.error(`❌ Database connection failed (attempt ${retryCount + 1}/${MAX_RETRIES})`)

    if (retryCount < MAX_RETRIES) {
      console.log(`🔄 Retrying in ${RETRY_DELAY / 1000}s...`)
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY))
      return setupConnection(retryCount + 1)
    }

    throw error
  }
}

export const closeConnection = async () => {
  if (AppDataSource.isInitialized) {
    await AppDataSource.destroy()
    console.log('✅ Database connection closed')
  }
}

/**
 * 检查数据库连接健康状态
 */
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    if (!AppDataSource.isInitialized) {
      return false
    }

    await AppDataSource.query('SELECT 1')
    return true
  } catch (error) {
    // 只在非生产环境输出健康检查错误
    if (!isProduction) {
      console.error('❌ Database health check failed:', error)
    }
    return false
  }
}

/**
 * 获取数据库统计信息
 */
export const getDatabaseStats = async () => {
  if (!AppDataSource.isInitialized) {
    throw new Error('Database not initialized')
  }

  const queryRunner = AppDataSource.createQueryRunner()
  try {
    const tables = await queryRunner.getTables()
    const stats = {
      connected: true,
      tableCount: tables.length,
      tables: tables.map((t) => ({
        name: t.name,
        columnCount: t.columns.length,
        indexCount: t.indices.length,
      })),
      entityCount: entities.length,
      entities: entities.map((e) => e.name),
    }

    return stats
  } finally {
    await queryRunner.release()
  }
}
