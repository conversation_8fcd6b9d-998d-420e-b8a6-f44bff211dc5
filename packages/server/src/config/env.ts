import process from 'node:process'

export interface IEnv {
  NODE_ENV: string // 环境
  PORT: number // 端口
  HOST: string // 主机
  API_PREFIX: string // 接口前缀
  CLIENT_PORT: number // 客户端端口
  DB_HOST: string // 数据库主机
  DB_PORT: number // 数据库端口
  DB_USER: string // 数据库用户
  DB_PASSWORD: string // 数据库密码
  DB_NAME: string // 数据库名称
  DB_SYNC: boolean // 数据库同步
  DB_LOGGING: boolean // 数据库日志
  JWT_SECRET: string // JWT密钥
  JWT_EXPIRES_IN: string // JWT过期时间
  REDIS_ENABLED: boolean // Redis是否启用
  REDIS_HOST: string // Redis主机
  REDIS_PORT: number // Redis端口
  REDIS_PASSWORD: string // Redis密码
  REDIS_DB: number // Redis数据库
  RATE_LIMIT_WINDOW: number // 限流窗口
  RATE_LIMIT_MAX: number // 限流最大值
}

// 检测当前环境
const NODE_ENV = process.env.NODE_ENV || 'development'
const isDevelopment = NODE_ENV === 'development'
const isStaging = NODE_ENV === 'staging'
const isProduction = NODE_ENV === 'production'

// 环境特定的默认值
const getEnvDefaults = () => {
  if (isDevelopment) {
    return {
      dbName: 'cfe_node_server_dev',
      dbSync: true,
      dbLogging: true,
      jwtSecret: 'dev-secret-key',
      redisEnabled: false,
      rateLimit: 1000,
    }
  }

  if (isStaging) {
    return {
      dbName: 'cfe_node_server_staging',
      dbSync: false,
      dbLogging: true,
      jwtSecret: 'staging-secret-key',
      redisEnabled: true,
      rateLimit: 200,
    }
  }

  // Production
  return {
    dbName: 'cfe_node_server_prod',
    dbSync: false,
    dbLogging: false,
    jwtSecret: 'your-secure-jwt-secret',
    redisEnabled: true,
    rateLimit: 100,
  }
}

const envDefaults = getEnvDefaults()

export const env: IEnv = {
  NODE_ENV,
  PORT: Number(process.env.PORT) || 3000, // 服务端口
  HOST: process.env.HOST || 'localhost', // 服务主机
  API_PREFIX: process.env.API_PREFIX || '/api', // 接口前缀
  CLIENT_PORT: Number(process.env.CLIENT_PORT) || 8000, // 客户端端口
  // 数据库配置
  DB_HOST: process.env.DB_HOST || 'localhost',
  DB_PORT: Number(process.env.DB_PORT) || 3306,
  DB_USER: process.env.DB_USER || 'root',
  DB_PASSWORD: process.env.DB_PASSWORD || 'root',
  DB_NAME: process.env.DB_NAME || envDefaults.dbName,

  // 环境默认配置
  DB_SYNC: process.env.DB_SYNC !== undefined
    ? process.env.DB_SYNC === 'true'
    : envDefaults.dbSync,

  // 日志配置
  DB_LOGGING: process.env.DB_LOGGING !== undefined
    ? process.env.DB_LOGGING === 'true'
    : envDefaults.dbLogging,

  // JWT配置
  JWT_SECRET: process.env.JWT_SECRET || envDefaults.jwtSecret,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',

  // Redis配置
  REDIS_ENABLED: process.env.REDIS_ENABLED !== undefined
    ? process.env.REDIS_ENABLED === 'true'
    : envDefaults.redisEnabled,
  REDIS_HOST: process.env.REDIS_HOST || 'localhost',
  REDIS_PORT: Number(process.env.REDIS_PORT) || 6379,
  REDIS_PASSWORD: process.env.REDIS_PASSWORD || '',
  REDIS_DB: Number(process.env.REDIS_DB) || 0,
  // 限流配置
  RATE_LIMIT_WINDOW: Number(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000,
  RATE_LIMIT_MAX: Number(process.env.RATE_LIMIT_MAX) || envDefaults.rateLimit,
}

// 环境配置验证和提示
if (isDevelopment) {
  console.log('🔧 Development Environment Configuration:')
  console.log('  - Database Sync:', env.DB_SYNC ? '✅ Enabled' : '❌ Disabled')
  console.log('  - Database Logging:', env.DB_LOGGING ? '✅ Enabled' : '❌ Disabled')
  console.log('  - Database Name:', env.DB_NAME)
  console.log('  - Redis:', env.REDIS_ENABLED ? '✅ Enabled' : '❌ Disabled')

  if (!env.DB_SYNC) {
    console.warn('⚠️  Database sync is disabled in development. Tables may not be created automatically.')
    console.warn('   Set DB_SYNC=true in your environment variables to enable auto table creation.')
  }
}

if (isStaging) {
  console.log('🧪 Staging Environment Configuration:')
  console.log('  - Database Sync:', env.DB_SYNC ? '✅ Enabled' : '❌ Disabled')
  console.log('  - Database Logging:', env.DB_LOGGING ? '✅ Enabled' : '❌ Disabled')
  console.log('  - Database Name:', env.DB_NAME)
  console.log('  - Redis:', env.REDIS_ENABLED ? '✅ Enabled' : '❌ Disabled')
  console.log('  - Rate Limit Max:', env.RATE_LIMIT_MAX)
}

// 生产环境和 Staging 环境安全检查
if (isStaging || isProduction) {
  const envType = isProduction ? 'Production' : 'Staging'
  const envIcon = isProduction ? '🚀' : '🧪'

  console.log(`${envIcon} ${envType} Environment Configuration`)

  if (env.DB_SYNC) {
    console.warn(`⚠️  WARNING: Database sync is enabled in ${envType.toLowerCase()}!`)
    console.warn('   This can cause data loss. Consider using migrations instead.')
  }

  const defaultSecrets = ['your-secure-jwt-secret', 'staging-secret-key', 'your-secret-key']
  if (defaultSecrets.includes(env.JWT_SECRET)) {
    const message = `❌ SECURITY WARNING: Using default JWT secret in ${envType.toLowerCase()}!`
    console.error(message)
    if (isProduction) {
      throw new Error(`Please set a secure JWT_SECRET in ${envType.toLowerCase()} environment`)
    } else {
      console.warn('   Please change JWT_SECRET for staging environment security.')
    }
  }
}
