import Redis from 'ioredis'
import { env } from './env'

export const useRedis = () => {
  let redis: Redis | null = null
  if (env.REDIS_ENABLED) {
    redis = new Redis({
      host: env.REDIS_HOST,
      port: env.REDIS_PORT,
      password: env.REDIS_PASSWORD,
      db: env.REDIS_DB,

      // 重连配置
      retryStrategy(times) {
        const delay = Math.min(times * 50, 2000)
        return delay
      },

      // 错误处理
      maxRetriesPerRequest: 3,
      enableReadyCheck: true,

      // 连接超时设置
      connectTimeout: 10000,

      // 保持连接
      keepAlive: 10000,
    })

    // 错误处理
    redis.on('error', (err) => {
      console.error('Redis connection error:', err)
    })

    // 连接成功处理
    redis.on('connect', () => {
      console.log('Redis connected successfully')
    })
  }
  return {
    redis,
    isConnected: !!redis,
  }
}
