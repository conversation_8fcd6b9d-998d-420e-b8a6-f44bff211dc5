import { afterAll, afterEach, beforeAll } from 'vitest'
import { AppDataSource, closeConnection, setupConnection } from '../config/database'

// 在所有测试之前设置数据库连接
beforeAll(async () => {
  await setupConnection()
})

// 每个测试后清理数据
afterEach(async () => {
  if (AppDataSource.isInitialized) {
    const entities = AppDataSource.entityMetadatas
    for (const entity of entities) {
      const repository = AppDataSource.getRepository(entity.name)
      await repository.clear()
    }
  }
})

// 所有测试完成后关闭连接
afterAll(async () => {
  await closeConnection()
})
