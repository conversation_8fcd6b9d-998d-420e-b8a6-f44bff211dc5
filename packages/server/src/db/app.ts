/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-11 11:17:30
 * @LastEditTime: 2025-07-11 11:17:32
 * @LastEditors: shaojun
 * @Description: 应用数据操作 - MySQL版本
 */

import { AppDataSource } from '@/config/database'
import { App, AppDailyStat, Team } from '@/typeorm'
import { RoleEnum } from '@/typeorm/enums'
import { formatDate, getStartOfDay } from '@/utils/date'

/**
 * 判断当前user是否有权限操作app
 * 判断当前用户是否是owner或者manager
 */
export const appInTeamAndUserIsManager = async (appId: number, teamId: number, userId: number): Promise<App | null> => {
  const appRepository = AppDataSource.getRepository(App)
  const teamRepository = AppDataSource.getRepository(Team)

  // 查找team，并检查用户是否为该团队成员且有管理权限
  const team = await teamRepository.findOne({
    where: { id: teamId },
    relations: ['userTeams'],
  })

  if (!team) {
    throw new Error('应用不存在或您没有权限执行该操作--team')
  }

  // 通过 userTeams 关系检查用户权限
  const userTeams = await team.userTeams
  const userTeam = userTeams.find((ut) =>
    ut.userId === userId && (ut.role === RoleEnum.owner || ut.role === RoleEnum.manager),
  )

  if (!userTeam) {
    throw new Error('应用不存在或您没有权限执行该操作--user')
  }

  // 查找app，检查是否属于该团队
  const app = await appRepository.findOne({
    where: {
      id: appId,
      ownerId: teamId,
    },
  })

  if (!app) {
    throw new Error('应用不存在或您没有权限执行该操作--app')
  }

  return app
}

/**
 * 修改应用下载数量
 * 使用 AppDailyStat 实体来管理每日下载统计
 */
export const changeAppDownloadCount = async (app: App, shouldIncrement: boolean): Promise<App> => {
  const appRepository = AppDataSource.getRepository(App)
  const dailyStatRepository = AppDataSource.getRepository(AppDailyStat)

  // 获取今天的日期
  const today = getStartOfDay()
  const todayStr = formatDate(today)

  if (shouldIncrement) {
    // 查找或创建今日统计记录
    let todayStat = await dailyStatRepository.findOne({
      where: {
        appId: app.id,
        statDate: todayStr as any, // TypeORM 会自动处理日期类型转换
      },
    })

    if (!todayStat) {
      // 创建新的今日统计记录
      todayStat = new AppDailyStat(app.id, today)
      todayStat.downloadCount = 1
      await dailyStatRepository.save(todayStat)
    } else {
      // 更新现有的今日统计记录
      todayStat.downloadCount += 1
      await dailyStatRepository.save(todayStat)
    }

    // 更新应用的总下载次数
    app.totalDownloadCount += 1
    await appRepository.save(app)
  }

  return app
}

/**
 * 获取应用的今日下载次数
 */
export const getAppTodayDownloadCount = async (appId: number): Promise<number> => {
  const dailyStatRepository = AppDataSource.getRepository(AppDailyStat)
  const today = getStartOfDay()
  const todayStr = formatDate(today)

  const todayStat = await dailyStatRepository.findOne({
    where: {
      appId,
      statDate: todayStr as any,
    },
  })

  return todayStat ? todayStat.downloadCount : 0
}

/**
 * 获取应用的下载统计信息
 */
export const getAppDownloadStats = async (appId: number): Promise<{
  totalDownloadCount: number
  todayDownloadCount: number
  weeklyDownloadCount: number
}> => {
  const appRepository = AppDataSource.getRepository(App)
  const dailyStatRepository = AppDataSource.getRepository(AppDailyStat)

  // 获取应用基本信息
  const app = await appRepository.findOne({ where: { id: appId } })
  if (!app) {
    throw new Error('应用不存在')
  }

  // 获取今日下载次数
  const todayDownloadCount = await getAppTodayDownloadCount(appId)

  // 获取最近7天的下载次数
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

  const weeklyStats = await dailyStatRepository
    .createQueryBuilder('stat')
    .where('stat.appId = :appId', { appId })
    .andWhere('stat.statDate >= :startDate', { startDate: formatDate(sevenDaysAgo) })
    .getMany()

  const weeklyDownloadCount = weeklyStats.reduce((sum, stat) => sum + stat.downloadCount, 0)

  return {
    totalDownloadCount: app.totalDownloadCount,
    todayDownloadCount,
    weeklyDownloadCount,
  }
}
