/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-05-27 14:07:06
 * @LastEditTime: 2024-06-04 15:20:23
 * @LastEditors: shaojun
 * @Description: 团队数据操作 - MySQL版本
 */

import { AppDataSource } from '@/config/database'
import { Team, User, UserTeam, TeamMember } from '@/typeorm'
import { RoleEnum } from '@/typeorm/enums'

/**
 * 获取team下所有成员的用户对象
 * @param team
 * @returns
 */
export const selectTeamMemberUsers = async (team: Team): Promise<User[]> => {
  const userRepository = AppDataSource.getRepository(User)
  const teamMemberRepository = AppDataSource.getRepository(TeamMember)
  
  // 通过团队成员表获取用户ID列表
  const teamMembers = await teamMemberRepository.find({
    where: { teamId: team.id }
  })
  
  if (teamMembers.length === 0) {
    return []
  }
  
  const memberIds = teamMembers.map(member => member.userId)
  return userRepository.findByIds(memberIds)
}

/**
 * 通过团队ID获取团队成员
 * @param teamId 团队ID
 * @returns
 */
export const getTeamMembersByTeamId = async (teamId: number): Promise<TeamMember[]> => {
  const teamMemberRepository = AppDataSource.getRepository(TeamMember)
  return teamMemberRepository.find({
    where: { teamId },
    order: { joinedAt: 'ASC' }
  })
}

// 权限weight
const roleWeightMap = new Map<RoleEnum, number>([
  [RoleEnum.owner, 100],
  [RoleEnum.manager, 10],
  [RoleEnum.guest, 1],
])

export const getRoleWeight = (role?: RoleEnum) => role && roleWeightMap.get(role) || 0

/**
 * 是否有权限操作team
 * @param teamId
 * @param userId
 * @param props 权限配置
 * @returns
 */
export const permissionTeam = async (
  teamId: number, 
  userId: number, 
  props?: { 
    minRole?: RoleEnum
    permissionMessage?: string 
  }
): Promise<Team> => {
  const teamRepository = AppDataSource.getRepository(Team)
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  
  // 查找团队
  const team = await teamRepository.findOne({ where: { id: teamId } })
  if (!team) {
    throw new Error('团队不存在')
  }
  
  // 查找用户在团队中的关系
  const userTeam = await userTeamRepository.findOne({
    where: { 
      teamId, 
      userId 
    }
  })
  
  if (!userTeam) {
    throw new Error('您不是该团队成员')
  }

  // 当前成员的权限小于最小要求的权限
  if (props && props.minRole && getRoleWeight(userTeam.role) < getRoleWeight(props.minRole)) {
    throw new Error(props.permissionMessage || '您没有权限操作该团队')
  }
  
  return team
}

/**
 * 检查用户是否有团队权限
 * @param teamId 团队ID
 * @param userId 用户ID
 * @param requiredRole 需要的最低角色
 * @returns
 */
export const checkTeamPermission = async (
  teamId: number, 
  userId: number, 
  requiredRole: RoleEnum = RoleEnum.guest
): Promise<boolean> => {
  try {
    await permissionTeam(teamId, userId, { minRole: requiredRole })
    return true
  } catch {
    return false
  }
}

/**
 * 获取用户在团队中的角色
 * @param teamId 团队ID
 * @param userId 用户ID
 * @returns
 */
export const getUserTeamRole = async (teamId: number, userId: number): Promise<RoleEnum | null> => {
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  
  const userTeam = await userTeamRepository.findOne({
    where: { teamId, userId }
  })
  
  return userTeam ? userTeam.role : null
}

/**
 * 添加用户到团队
 * @param teamId 团队ID
 * @param userId 用户ID
 * @param role 角色
 * @param invitedBy 邀请者名称
 * @returns
 */
export const addUserToTeam = async (
  teamId: number, 
  userId: number, 
  role: RoleEnum = RoleEnum.guest,
  invitedBy?: string
): Promise<void> => {
  const teamRepository = AppDataSource.getRepository(Team)
  const userRepository = AppDataSource.getRepository(User)
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  const teamMemberRepository = AppDataSource.getRepository(TeamMember)
  
  // 使用事务确保数据一致性
  await AppDataSource.transaction(async (manager) => {
    // 检查团队是否存在
    const team = await manager.findOne(Team, { where: { id: teamId } })
    if (!team) {
      throw new Error('团队不存在')
    }
    
    // 检查用户是否存在
    const user = await manager.findOne(User, { where: { id: userId } })
    if (!user) {
      throw new Error('用户不存在')
    }
    
    // 检查用户是否已经在团队中
    const existingUserTeam = await manager.findOne(UserTeam, {
      where: { teamId, userId }
    })
    if (existingUserTeam) {
      throw new Error('用户已经是团队成员')
    }
    
    // 创建用户团队关系
    const userTeam = new UserTeam(userId, teamId, role)
    userTeam.teamName = team.name
    userTeam.teamIcon = team.icon
    await manager.save(UserTeam, userTeam)
    
    // 创建团队成员记录
    const teamMember = new TeamMember(teamId, userId, role)
    teamMember.username = user.username
    teamMember.email = user.email
    teamMember.userAvatar = user.userAvatar
    teamMember.joinedAt = new Date()
    teamMember.invitedBy = invitedBy
    await manager.save(TeamMember, teamMember)
    
    // 更新团队成员数量
    team.memberCount += 1
    await manager.save(Team, team)
  })
}

/**
 * 从团队移除用户
 * @param teamId 团队ID
 * @param userId 用户ID
 * @returns
 */
export const removeUserFromTeam = async (teamId: number, userId: number): Promise<void> => {
  const teamRepository = AppDataSource.getRepository(Team)
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  const teamMemberRepository = AppDataSource.getRepository(TeamMember)
  
  // 使用事务确保数据一致性
  await AppDataSource.transaction(async (manager) => {
    // 检查用户团队关系是否存在
    const userTeam = await manager.findOne(UserTeam, {
      where: { teamId, userId }
    })
    if (!userTeam) {
      throw new Error('用户不是团队成员')
    }
    
    // 检查是否为团队所有者（不能移除所有者）
    if (userTeam.role === RoleEnum.owner) {
      throw new Error('不能移除团队所有者')
    }
    
    // 删除用户团队关系
    await manager.delete(UserTeam, { teamId, userId })
    
    // 删除团队成员记录
    await manager.delete(TeamMember, { teamId, userId })
    
    // 更新团队成员数量
    const team = await manager.findOne(Team, { where: { id: teamId } })
    if (team && team.memberCount > 0) {
      team.memberCount -= 1
      await manager.save(Team, team)
    }
  })
}

/**
 * 更新用户在团队中的角色
 * @param teamId 团队ID
 * @param userId 用户ID
 * @param newRole 新角色
 * @returns
 */
export const updateUserTeamRole = async (
  teamId: number, 
  userId: number, 
  newRole: RoleEnum
): Promise<void> => {
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  const teamMemberRepository = AppDataSource.getRepository(TeamMember)
  
  // 使用事务确保数据一致性
  await AppDataSource.transaction(async (manager) => {
    // 更新用户团队关系中的角色
    const updateResult = await manager.update(UserTeam, 
      { teamId, userId }, 
      { role: newRole }
    )
    
    if (updateResult.affected === 0) {
      throw new Error('用户不是团队成员')
    }
    
    // 更新团队成员记录中的角色
    await manager.update(TeamMember, 
      { teamId, userId }, 
      { role: newRole }
    )
  })
}
