/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-04-19 14:42:10
 * @LastEditTime: 2025-07-11 11:26:04
 * @LastEditors: shaojun
 * @Description: 用户数据操作 - MySQL版本
 */

import { AppDataSource } from '@/config/database'
import { Team, TeamMember, User, UserTeam } from '@/typeorm'
import { RoleEnum } from '@/typeorm/enums'

/**
 * 创建用户
 * @param username
 * @param password
 * @param email
 * @returns User
 */
export const createUser = async (username: string, password: string, email: string): Promise<User> => {
  const userRepository = AppDataSource.getRepository(User)
  const teamRepository = AppDataSource.getRepository(Team)
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  const teamMemberRepository = AppDataSource.getRepository(TeamMember)

  // 使用事务确保数据一致性
  return await AppDataSource.transaction(async (manager) => {
    try {
      // 创建用户
      const newUser = new User(username, password, email)
      const savedUser = await manager.save(User, newUser)

      // 创建个人团队
      const newTeam = new Team(`${username}的团队`, '', true)
      newTeam.creatorId = savedUser.id
      newTeam.creatorName = savedUser.username
      newTeam.memberCount = 1
      const savedTeam = await manager.save(Team, newTeam)

      // 创建用户团队关系
      const userTeam = new UserTeam(savedUser.id, savedTeam.id, RoleEnum.owner)
      userTeam.teamName = savedTeam.name
      await manager.save(UserTeam, userTeam)

      // 创建团队成员记录
      const teamMember = new TeamMember(savedTeam.id, savedUser.id, RoleEnum.owner)
      teamMember.username = savedUser.username
      teamMember.email = savedUser.email
      teamMember.userAvatar = savedUser.userAvatar
      teamMember.joinedAt = new Date()
      await manager.save(TeamMember, teamMember)

      return savedUser
    } catch (error) {
      throw new Error(`创建用户失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  })
}

/**
 * 检查团队是否存在
 */
export const checkTeamExist = async (teamId?: number): Promise<boolean> => {
  if (!teamId) {
    throw new Error('团队id不能为空')
  }

  const teamRepository = AppDataSource.getRepository(Team)
  const team = await teamRepository.findOne({ where: { id: teamId } })

  if (!team) {
    throw new Error('团队不存在')
  }

  return !!team
}

/**
 * 查询所有users
 */
export const getUsers = async (): Promise<User[]> => {
  const userRepository = AppDataSource.getRepository(User)
  return userRepository.find({
    select: {
      id: true,
      username: true,
      email: true,
      userAvatar: true,
      mobile: true,
      company: true,
      career: true,
      createdAt: true,
      updatedAt: true,
    },
  })
}

/**
 * 根据ID查询用户
 */
export const getUserById = async (userId: number): Promise<User | null> => {
  const userRepository = AppDataSource.getRepository(User)
  return userRepository.findOne({
    where: { id: userId },
    select: {
      id: true,
      username: true,
      email: true,
      userAvatar: true,
      mobile: true,
      company: true,
      career: true,
      createdAt: true,
      updatedAt: true,
    },
  })
}

/**
 * 根据邮箱查询用户
 */
export const getUserByEmail = async (email: string): Promise<User | null> => {
  const userRepository = AppDataSource.getRepository(User)
  return userRepository.findOne({
    where: { email },
    select: {
      id: true,
      username: true,
      email: true,
      password: true, // 登录验证时需要密码
      userAvatar: true,
      token: true,
      apiToken: true,
      mobile: true,
      company: true,
      career: true,
      createdAt: true,
      updatedAt: true,
    },
  })
}

/**
 * 根据用户名查询用户
 */
export const getUserByUsername = async (username: string): Promise<User | null> => {
  const userRepository = AppDataSource.getRepository(User)
  return userRepository.findOne({
    where: { username },
    select: {
      id: true,
      username: true,
      email: true,
      password: true, // 登录验证时需要密码
      userAvatar: true,
      token: true,
      apiToken: true,
      mobile: true,
      company: true,
      career: true,
      createdAt: true,
      updatedAt: true,
    },
  })
}

/**
 * 更新用户信息
 */
export const updateUser = async (userId: number, updateData: Partial<User>): Promise<void> => {
  const userRepository = AppDataSource.getRepository(User)

  // 移除不允许更新的字段
  const { id, createdAt, updatedAt, ...allowedUpdateData } = updateData

  await userRepository.update({ id: userId }, allowedUpdateData)
}

/**
 * 获取用户的团队信息
 */
export const getUserTeams = async (userId: number): Promise<UserTeam[]> => {
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  return userTeamRepository.find({
    where: { userId },
    order: { createdAt: 'DESC' },
  })
}

/**
 * 检查用户是否为团队成员
 */
export const isUserTeamMember = async (userId: number, teamId: number): Promise<boolean> => {
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  const userTeam = await userTeamRepository.findOne({
    where: { userId, teamId },
  })
  return !!userTeam
}

/**
 * 获取用户在团队中的角色
 */
export const getUserRoleInTeam = async (userId: number, teamId: number): Promise<RoleEnum | null> => {
  const userTeamRepository = AppDataSource.getRepository(UserTeam)
  const userTeam = await userTeamRepository.findOne({
    where: { userId, teamId },
  })
  return userTeam ? userTeam.role : null
}

/**
 * 过滤用户信息的敏感字段
 */
export const filterSensitiveUser = (user: User): Omit<User, 'password'> => {
  const { password, ...safeUser } = user
  return safeUser
}
