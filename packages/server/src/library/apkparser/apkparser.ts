import type { ExecFileException } from 'node:child_process'
import { <PERSON><PERSON><PERSON> } from 'node:buffer'
import { execFile } from 'node:child_process'
import os from 'node:os'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import logger from '@/middlewares/logger'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// https://developer.android.google.cn/studio/command-line/aapt2?hl=zh-cn
// aapt2 dump sub-command filename.apk [options]
// badging输出从 APK 的清单中提取的信息。
const parseApk = (
  filename: string,
  cb: (
    error: ExecFileException | null,
    stdout?: string | Buffer,
    stderr?: string | Buffer
  ) => void,
) => {
  let exeName = ''
  if (os.type() === 'Darwin') {
    logger().info('Darwin===>aapt2')
    exeName = 'aapt2'
  } else if (os.type() === 'Linux') {
    logger().info('Linux===>aapt-linux')
    exeName = 'aapt-linux'
  } else {
    throw new Error('Unknown OS!')
  }

  // 执行aapt命令输出从 APK 的清单中提取的信息。 https://developer.android.google.cn/studio/command-line/aapt2?hl=zh-cn
  const process = execFile(
    path.join(__dirname, exeName),
    ['dump', 'badging', filename],
    {
      maxBuffer: 1024 * 1024 * 1024,
    },
    (err, out) => {
      if (err) {
        return cb(err)
      }

      return parseOutput(out, cb)
    },
  )
  process.on('error', (err) => {
    logger().error(`Caught exception: ${err}`)
  })
}

const parseOutput = (
  text: string,
  cb: (
    error: ExecFileException | null,
    stdout?: string | Buffer,
    stderr?: string | Buffer
  ) => void,
) => {
  if (!text) {
    return cb(new Error('No input!'))
  }
  const lines = text.split('\n')
  const result: Buffer = Buffer.from('')
  for (let i = 0; i < lines.length; i++) {
    const kvs = lines[i].split(':')
    logger().info(`parseOutput ====> ${kvs[0]}----${kvs[1]}`)
    if (kvs.length === 2) {
      result[kvs[0]] = kvs[1]
    }
  }
  return cb(null, result)
}

parseApk.parseOutput = parseOutput

export default parseApk
