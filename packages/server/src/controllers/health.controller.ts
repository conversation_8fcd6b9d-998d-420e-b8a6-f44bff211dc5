import type { ControllerConfig } from '@cfe-node/koa-swagger-decorator'
import type { Context } from 'koa'
import { Controller, Responses, RouteConfig, z } from '@cfe-node/koa-swagger-decorator'

// 健康检查响应 Schema
const HealthCheckResponseSchema = z.object({
  status: z.string().openapi({ example: 'ok', description: '服务状态' }),
  timestamp: z.string().openapi({ example: new Date().toISOString(), description: '时间戳' }),
  db: z.union([z.string(), z.null()]).optional().openapi({ example: 'ok', description: '数据库状态' }),
})

const controllerConfig: ControllerConfig = {
  tags: ['系统健康检查'],
}

@Controller(controllerConfig)
export class HealthController {
  @RouteConfig({
    method: 'get',
    path: '/health',
    summary: '健康检查',
    description: '服务健康检查接口',
  })
  @Responses(HealthCheckResponseSchema)
  async getHealth(ctx: Context) {
    // 预留数据库健康检查扩展点
    // const dbStatus = await checkDbHealth() // 可扩展
    ctx.body = {
      code: 0,
      data: {
        status: 'ok',
        timestamp: new Date().toISOString(),
        // db: dbStatus,
      },
      message: '健康检查成功',
    }
  }
}
