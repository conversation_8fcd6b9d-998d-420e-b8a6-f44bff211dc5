/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-07-14 08:49:46
 * @LastEditTime: 2025-07-14 08:49:48
 * @LastEditors: shaojun
 * @Description:
 */
import { Controller } from '@cfe-node/koa-swagger-decorator'

@Controller({
  tags: ['_Global'],
  components: {
    securitySchemes: {
      BearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
  },
})
export class GlobalSwaggerController { }
