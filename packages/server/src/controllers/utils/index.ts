/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-07-15 15:05:57
 * @LastEditTime: 2025-07-16 18:56:38
 * @LastEditors: shaojun
 * @Description:
 */
import type { Context } from 'koa'
import type { User } from '../../typeorm'
import { AppException } from '../../middlewares'

export * from './tools'

export const getJwtAuthUser = (ctx: Context): User => {
  try {
    const user = ctx.state.user.data
    if (!user) {
      throw new AppException(401, '用户未登录')
    }
    return user
  } catch (error) {
    throw new AppException(401, '用户未登录')
  }
}
