/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-15 15:05:29
 * @LastEditTime: 2025-07-16 18:56:47
 * @LastEditors: s<PERSON><PERSON>
 * @Description:
 */
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-12 16:31:39
 * @LastEditTime: 2024-04-29 09:19:47
 * @LastEditors: shaojun
 * @Description:
 */

import type { ZodRawShape, ZodType } from 'zod'
import type { DefaultObject } from '../../typeorm'
import { z } from '@cfe-node/koa-swagger-decorator'
import Constants from '../../base/constants'
// 泛型函数R
export function R<T extends z.ZodTypeAny>(dataSchema: T) {
  return z.object({
    resultCode: z.number().openapi({ example: 200 }),
    resultMessage: z.string().openapi({ example: 'success' }),
    data: dataSchema,
  })
}
// 泛型函数LIST
export function LIST<T extends z.ZodTypeAny>(dataSchema: T) {
  return z.array(dataSchema)
}

// 泛型函数RList
export function RList<T extends z.ZodTypeAny>(dataSchema: T) {
  return z.object({
    resultCode: z.number().openapi({ example: 200 }),
    resultMessage: z.string().openapi({ example: 'success' }),
    data: LIST(dataSchema),
  })
}
// 定义泛型类型
export type RType<T extends z.ZodTypeAny> = z.infer<ReturnType<typeof R<T>>>
export type ListType<T extends z.ZodTypeAny> = z.infer<ReturnType<typeof LIST<T>>>
export type RListType<T extends z.ZodTypeAny> = z.infer<ReturnType<typeof RList<T>>>
// zod.infer类型
export type ZType<T extends z.ZodTypeAny> = z.infer<T>

// 验证并处理
export function validateAndProcess<T extends ZodRawShape>(
  input: T,
  schema: ZodType<T>,
  callback: (data: T) => void,
) {
  const result = schema.safeParse(input)
  if (result.success) {
    callback(result.data)
  } else {
    throw new Error(`Invalid input: ${result.error.message}`)
  }
}

/**
 * response封装
 * @param params (多参数)
 */
export const responseWrapper = <T>(res: Partial<DefaultObject<T>>): DefaultObject<T> => {
  const { resultCode = Constants.SUCCESS_CODE, resultMessage = 'success', data } = res
  return {
    resultCode,
    resultMessage,
    data,
  } as DefaultObject<T>
}
