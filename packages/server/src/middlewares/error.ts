/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-07-15 15:09:27
 * @LastEditTime: 2025-07-16 18:56:26
 * @LastEditors: shaojun
 * @Description:
 */
import type { Context, Next } from 'koa'

import Constants from '../base/constants'
import { responseWrapper } from '../controllers/utils'
import logger from './logger'

export interface IAppException {
  resultCode?: number
  resultMessage?: string
}
/**
 * 异常内封装
 */
export class AppException extends Error implements IAppException {
  resultCode?: number
  resultMessage?: string
  constructor(resultCode = Constants.ERROR_CODE_BIZ, resultMessage = '服务器异常') {
    super()
    this.resultCode = resultCode
    this.resultMessage = resultMessage
    this.message = resultMessage
  }
}

/**
 * 异常处理中间件
 * @param ctx
 * @param next
 */
export const errorHandler = async (ctx: Context, next: Next): Promise<void> => {
  try {
    await next()
  } catch (err: any) {
    const resultMessage = err.resultMessage || (err.originalError && err.originalError.message) || err.message || ''

    if (err.status === Constants.ERROR_CODE_AUTHENTICATION) {
      // ctx.status = Constants.ERROR_CODE_AUTHENTICATION;
      err.resultCode = Constants.ERROR_CODE_AUTHENTICATION
      ctx.status = Constants.ERROR_CODE_AUTHENTICATION
      ctx.body = responseWrapper<string>({ resultCode: Constants.ERROR_CODE_AUTHENTICATION, resultMessage })
    } else {
      if (err.resultCode) {
        logger().error(`\n[${err.resultCode}-${err.resultMessage}]\n[${err.stack}]`)
      } else {
        logger().error(`报错？是不是你又写bug了？如果只是正常的业务异常，请使用AppException() \n${err.stack}`)
      }

      const resultCode = err.resultCode || Constants.ERROR_CODE_BIZ
      // (is biz err) ? 200 : 500
      ctx.status = resultCode ? Constants.SUCCESS_CODE : Constants.ERROR_CODE_INTERNAL
      // {resultCode:999,resultMessage:"err msg"}
      ctx.body = responseWrapper<string>({ resultCode, resultMessage })
    }
  }
}
