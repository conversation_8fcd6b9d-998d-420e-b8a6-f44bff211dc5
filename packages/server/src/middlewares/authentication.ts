/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-15 15:10:23
 * @LastEditTime: 2025-07-15 15:22:07
 * @LastEditors: shaojun
 * @Description:
 */
import type { Context, Next } from 'koa'
import type { DefaultObject } from '../typeorm/model'
import KoaJwt from 'koa-jwt'
import config from '../config'

import MiddlewareHelper from '../helpler/middle'
import { User } from '../typeorm/entity/user'
import logger from './logger'

class Authentication {
  // apiToken
  KEY_API_TOKEN = config.server.keyApiToken.toLocaleLowerCase()
  authMiddleware = KoaJwt({ secret: config.server.secret }).unless({
    // 设置login、jiarLogin、register接口，可以不需要认证访问
    path: [
      '/api/user/register',
      '/api/user/login',
      '/api/user/jiarLogin',
      '/api/user/resetPassword',
      '/api/swagger',
      '/api/swagger.json',
      '/api/swagger.yaml',
      // '/api/miniapps/addDownloadCode',
      // /\/api\/miniappDetail\/.+/,
      // /\/api\/miniappDetailForAutobuild\/.+/,
      // /\/api\/plist\/.+/,
      // /\/api\/count\/.+/,
      /^((?!\/api).)*$/, // 设置除了私有接口外的其它资源，可以不需要认证访问
    ],
  })

  needAuth = new MiddlewareHelper()
    .use(this.authMiddleware)
    .if((ctx: Context) => {
      const key = ctx.request.headers[this.KEY_API_TOKEN]
      // 有apiToken,就跳过jwt校验
      const hasApiToken = key !== undefined
      if (hasApiToken) {
        logger().debug(
          `Authentication-------->${ctx.path}--header中包含ApiToken，不会校验jwt token`,
        )
      }

      // 没有才需要认证
      return !hasApiToken
    })

  apiTokenMiddleware = async (ctx: Context, next: Next): Promise<void> => {
    const key: string = ctx.request.headers[this.KEY_API_TOKEN] as string
    if (key !== undefined) {
      // const userRepository = appDataSource.getMongoRepository(User)
      // const user = await userRepository.findOneBy({ apiToken: key })
      // todo: 这里需要修改，根据apiToken获取用户信息
      const user = null
      if (!user) {
        throw new Error('api token is not exist')
      }

      ctx.state.user = { data: user }
      await next()
    } else {
      await next()
    }
  }

  verifyApiToken = new MiddlewareHelper()
    .use(this.apiTokenMiddleware)
    .if((ctx: Context) => {
      const key = ctx.request.headers[this.KEY_API_TOKEN]
      // 有apiToken，就认证一下ApiToken是否有效
      const hasApiToken = key !== undefined
      if (hasApiToken) {
        logger().debug(
          `Authentication-------->${ctx.path}--包含处理apiToken校验，添加ctx.state.user`,
        )
      }

      return hasApiToken
    })

  error = async (ctx: Context, next: Next): Promise<void> => {
    return next().catch((err) => {
      if (err.status === 401) {
        ctx.status = 401
        const body: DefaultObject<string> = {
          resultCode: 401,
          resultMessage: err.originalError
            ? err.originalError.message
            : err.message,
          data: '',
        }
        ctx.body = body
      } else {
        throw err
      }
    })
  }
}

export const authentication = new Authentication()
/**
 */
// const restify = (): Middleware => {
//   const pathPrefix = config.apiPrefix;
//   return async (ctx: Context, next: Next) => {
//     if (ctx.request.path.startsWith(pathPrefix)) {
//       console.log(`Process API ${ctx.request.method} ${ctx.request.url}...`);
//       ctx.rest = (data) => {
//         ctx.response.type = 'application/json';
//         ctx.response.body = data;
//       };
//       try {
//         await next();
//       } catch (e) {
//         console.log('Process API error...');
//         ctx.response.type = 'application/json';
//         ctx.response.body = {
//           success: false,
//           message: e.message || ''
//         };
//         console.log(e);
//       }
//     } else {
//       await next();
//     }
//   };
// };
