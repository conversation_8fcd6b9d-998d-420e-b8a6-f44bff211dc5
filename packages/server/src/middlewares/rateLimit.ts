import rateLimit from 'koa-ratelimit'
import { env } from '../config/env'
import { useRedis } from '../config/redis'

// 创建内存存储
const memoryDB = new Map()

const { redis } = useRedis()

export const rateLimitMiddleware = rateLimit({
  driver: env.REDIS_ENABLED ? 'redis' : 'memory', // 根据配置选择存储方式
  db: (env.REDIS_ENABLED ? redis : memoryDB) as any, // 根据配置选择存储实例
  duration: env.RATE_LIMIT_WINDOW || 15 * 60 * 1000, // 15分钟
  max: env.RATE_LIMIT_MAX || 100,
  // 可选：针对不同的路由设置不同的限制
  errorMessage: '请求过于频繁，请稍后再试',
  id: (ctx) => ctx.ip, // 使用 IP 作为限制标识
  headers: {
    remaining: 'Rate-Limit-Remaining',
    reset: 'Rate-Limit-Reset',
    total: 'Rate-Limit-Total',
  },
})
