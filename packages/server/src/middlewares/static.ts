import type { Context, Next } from 'koa'
import fs from 'node:fs'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-12-18 17:59:17
 * @LastEditTime: 2025-07-15 18:44:44
 * @LastEditors: shaojun
 * @Description:
 */
import path from 'node:path'
import { resolve } from 'node:path/posix'
import { ServerPublicRoot } from '@cfe-node/utils'
import config from '../config'
import { env } from '../config/env'

class StaticPage {
  rootDir = config.server.rootDir
  publicRoot = ServerPublicRoot
  staticRoot = resolve(config.server.rootDir, 'static')
  // 存放上传文件的路径
  fileDir = config.server.fileDir
  syncToClientHtml = async (ctx: Context, next: Next) => {
    if (ctx.request.path.indexOf(env.API_PREFIX) !== 0) {
      console.log('syncToClientHtml', ctx.request.path)
      return next()
    }

    if (ctx.request.path === '/api/swagger.json') {
      const jsonFilePath = path.join(ServerPublicRoot, 'swagger.json')
      console.log(`swagger.json文件访问==>${jsonFilePath}`)
      ctx.response.type = 'json'
      ctx.response.body = fs.readFileSync(jsonFilePath, { encoding: 'utf8' })
      return
    }

    if (ctx.request.path === '/api/swagger.yaml') {
      const yamlFilePath = path.join(ServerPublicRoot, 'swagger.yaml')
      console.log(`swagger.yaml文件访问==>${yamlFilePath}`)
      ctx.response.type = 'text/yaml'
      ctx.response.body = fs.readFileSync(yamlFilePath, { encoding: 'utf8' })
      return
    }

    return next()
  }
}

const staticPage = new StaticPage()

export { staticPage }
