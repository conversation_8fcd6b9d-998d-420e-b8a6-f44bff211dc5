/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-07-15 15:02:54
 * @LastEditTime: 2025-07-15 16:25:22
 * @LastEditors: shaojun
 * @Description:
 */
import type { Context, Next } from 'koa'
import type { Configuration, Level, Logger } from 'log4js'
import Log4js from 'log4js'
import config from '../config'

interface loggerConfigOptions {
  debugger: boolean
  format?: string
  level?: string
  nolog?: string | RegExp | string[]
  levelMapper?: (statusCode: string) => Level
}

/**
 * 日志分类枚举
 */
export enum LogGategoriyEnum {
  DEBUGGER = 'debugger',
  ACCESS = 'access',
}

/**
 * 日志处理类型枚举
 */
export enum LogAppenderEnum {
  OUT = 'out',
  ACCESS_RECORD = 'accessRecord',
  WRITE_FILE = 'writeFile',
  FILTER_ERROR = 'filterError',
}

/**
 * 日志类封装（log4js-node）
 *
 */
class LoggerX {
  private static instance: LoggerX
  public static getInstance(): LoggerX {
    if (!LoggerX.instance) {
      LoggerX.instance = new LoggerX()
    }

    return LoggerX.instance
  }

  DEFAULT_FORMAT = ':remote-addr - -' + ' ":method :url HTTP/:http-version"' + ' :status :content-length ":referrer"' + ' ":user-agent"'
  options?: loggerConfigOptions
  //   DEFAULT_LEVEL_MAPPER: Levels = (statusCode: number): Levels => {
  //     if (statusCode >= 400) return Levels.ERROR;
  //     if (statusCode >= 300) return Levels.WARN;
  //     return Levels.INFO;
  //   };

  constructor() {
    this.init({
      // 非debugger模式log会写文件
      debugger: config.isDebug,
      // 过滤日志,不输出日志
      nolog: '.png|.jpg',
    })
  }

  init(options?: loggerConfigOptions) {
    this.options = options
    // const level = options.level ? Log4js.levels.getLevel(options.level ) || "" ;
    // const levelMapper = options.levelMapper || this.DEFAULT_LEVEL_MAPPER;
    const logsDir = config.server.logDir
    // log4js配置
    const configuration: Configuration = {
      appenders: {
        [LogAppenderEnum.OUT]: { type: 'console' },
        [LogAppenderEnum.ACCESS_RECORD]: {
          type: 'dateFile',
          filename: `${logsDir}/access.log`,
          pattern: 'yyyy-MM-dd.log',
          alwaysIncludePattern: true,
          mode: 0o644, // Sets rw-r--r--
        },
        [LogAppenderEnum.WRITE_FILE]: { type: 'file', filename: `${logsDir}/error.log` },
        [LogAppenderEnum.FILTER_ERROR]: {
          type: 'logLevelFilter',
          level: 'error',
          maxLevel: 'error',
          appender: [LogAppenderEnum.WRITE_FILE],
        },
      },
      categories: {
        default: { appenders: [LogAppenderEnum.OUT], level: 'all', enableCallStack: true },
        [LogGategoriyEnum.DEBUGGER]: { appenders: [LogAppenderEnum.OUT], level: 'all', enableCallStack: true },
        [LogGategoriyEnum.ACCESS]: {
          appenders: [LogAppenderEnum.OUT, LogAppenderEnum.ACCESS_RECORD, LogAppenderEnum.FILTER_ERROR],
          level: 'all',
          enableCallStack: true,
        },
      },
    }
    Log4js.configure(configuration)
  }

  /**
   * 获取不同类型的logger
   * @param category 类型
   */
  gategoriy(category?: LogGategoriyEnum): Logger {
    let gategoriyLogger: Logger
    switch (category) {
      case LogGategoriyEnum.ACCESS:
        gategoriyLogger = Log4js.getLogger(LogGategoriyEnum.ACCESS)
        break
      case LogGategoriyEnum.DEBUGGER:
        gategoriyLogger = Log4js.getLogger(LogGategoriyEnum.DEBUGGER)
        break
      default:
        //   default
        gategoriyLogger = Log4js.getLogger()
        break
    }
    return gategoriyLogger
  }

  /**
   * Return RegExp Object about nolog
   *
   * @param  {string} nolog
   * @return {RegExp}
   *
   * syntax
   *  1. String
   *   1.1 "\\.gif"
   *   1.2 in "\\.gif|\\.jpg$"
   *   1.3 in "\\.(gif|jpe?g|png)$"
   *  2. RegExp
   *   2.1 in /\.(gif|jpe?g|png)$/
   *        SAME AS 1.3
   *  3. Array
   *   3.1 ["\\.jpg$", "\\.png", "\\.gif"]
   *        SAME AS "\\.jpg|\\.png|\\.gif"
   */
  private createNoLogCondition(nolog: string | RegExp | string[]): RegExp {
    let regexp
    if (nolog) {
      if (nolog instanceof RegExp) {
        regexp = nolog
      }

      if (typeof nolog === 'string') {
        regexp = new RegExp(nolog)
      }

      if (Array.isArray(nolog) && Array.isArray(nolog)) {
        regexp = new RegExp(nolog.join('|'))
      }
    }

    return regexp
  }

  // 判断是否不需要打印日志
  isNolog(originalUrl: string): boolean {
    const isNolog = this.options?.nolog ? this.createNoLogCondition(this.options.nolog) : null
    return !!isNolog && isNolog.test(originalUrl)
  }

  isDebugger(): boolean {
    return (this.options && this.options.debugger) || false
  }
}

/**
 * elasticSeach kibana
 * 信息采集->es
 */
class ElasticSearch {
  private static instance: ElasticSearch
  public static getInstance(): ElasticSearch {
    if (!ElasticSearch.instance) {
      ElasticSearch.instance = new ElasticSearch()
    }

    return ElasticSearch.instance
  }

  constructor() {
    console.log()
  }

  putTrace(ctx: Context) {
    // ctx.append('_datetime', dayjs().format());
    // if (!ctx.header._traceId) {
    //   console.log('添加traceId');
    //   ctx.append('_traceId', Math.random().toString(36).substring(2, 5) + Math.random().toString(36).substring(2, 5));
    //   // ctx.header["_traceId"] =
    // } else {
    //   console.log('已经存在添加traceId');
    // }

    // LoggerX.getInstance()
    //   .gategoriy(LogGategoriyEnum.ACCESS)
    //   .info(`${ctx.originalUrl}----${ctx.header._traceId}--------------${JSON.stringify(ctx)}`);
    // this.client.index()
  }
}

// 导出中间件
export const loggerHander = async (ctx: Context, next: Next): Promise<void> => {
  // 过滤不需要打日志的url
  if (LoggerX.getInstance().isNolog(ctx.originalUrl)) {
    await next()
    return
  }
  const start = Date.now()
  ElasticSearch.getInstance().putTrace(ctx)
  await next()
  const end = Date.now()
  const responseTime = end - start
  const content = `[url:${ctx.originalUrl}] - [状态:${ctx.status}]`
  logger().info(`${content}-响应时长${responseTime / 1000}s`)
  ElasticSearch.getInstance().putTrace(ctx)
}

// 根据类型返回日志
const logger = (): Logger => {
  if (LoggerX.getInstance().isDebugger()) {
    return LoggerX.getInstance().gategoriy(LogGategoriyEnum.DEBUGGER)
  } else {
    return LoggerX.getInstance().gategoriy(LogGategoriyEnum.ACCESS)
  }
}

export default logger
