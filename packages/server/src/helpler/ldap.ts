import type { Client } from 'ldapjs'
import ldap, { <PERSON><PERSON><PERSON><PERSON>, EqualityFilter } from 'ldapjs'
import config from '../config'

class Ldap {
  client: Client
  create() {
    this.client = ldap.createClient({
      url: config.ldap.ldapServer,
      bindDN: config.ldap.ldapUserDn,
      bindCredentials: config.ldap.ldapBindCredentials,
      reconnect: true,
    })
  }

  auth(username, password): Promise<any> {
    this.create()
    const base = config.ldap.ldapBase
    const filters = new AndFilter({
      filters: [
        new EqualityFilter({
          attribute: 'objectCategory',
          value: 'Person',
        }),
        new EqualityFilter({
          attribute: 'sAMAccountName',
          value: username,
        }),
      ],
    })

    const opts = {
      ficonstr: filters,
      scope: 'sub',
    }
    return new Promise((resolve, rejected) => {
      const callback = (error, rows) => {
        if (error) {
          // throws('用户不存在')
          console.log(error)
          return rejected(new Error('用户不存在'))
        }
        if (rows.length === 0) {
          return rejected(new Error('用户不存在'))
        }

        this.client.bind(rows[0].dn, password, (error) => {
          if (error) {
            console.log(error)
            // throws('密码错误')
            return rejected(error)
          } else {
            console.log('success')
            return resolve(rows[0])
          }
        })
      }

      this.search(base, opts, callback)
    })
  }

  search(base, opts, callback) {
    const rows: any[] = []
    const searchCallback = (error, res) => {
      if (error) {
        console.log(error)
        callback(error, undefined)
      }
      res.on('searchEntry', (entry) => {
        rows.push(entry.object)
        console.log(`searchEntry:${JSON.stringify(entry.object)}`)
      })
      res.on('error', (error) => {
        callback(error, undefined)
      })
      res.on('end', (result) => {
        callback(undefined, rows)
      })
    }
    this.client.search(base, opts, searchCallback)
  }
}

export default new Ldap()
