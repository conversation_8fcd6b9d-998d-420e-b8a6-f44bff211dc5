// import nodemailer from "nodemailer";

import fs from 'node:fs'
import type { Transporter } from 'nodemailer'
import nodemailer from 'nodemailer'
import type SMTPTransport from 'nodemailer/lib/smtp-transport'
import ejs from 'ejs'
import config, { serverSrcDir } from '../config'

export const emailTemplatePath = `${serverSrcDir}/helpler/email-template`

export default class Mail {
  // 邮件服务配置
  poolConfig: string = `smtps://${config.email.emailUser}:${config.email.emailPass}@${config.email.emailService}/?pool=true`
  // 邮件发送器
  transporter: Transporter<SMTPTransport.SentMessageInfo>

  // 构造函数
  constructor() {
  // 创建邮件发送器
    this.transporter = nodemailer.createTransport(this.poolConfig)
  }

  async verify() {
    return await this.transporter.verify()
  }

  // 关闭邮件发送器
  close() {
    this.transporter.close()
  }

  /**
   * 发送邮件
   * @param options
   * @returns
   */
  sendEmail<T extends EmailTemplate>(options: {
    emails: string[] | string // 收件人
    subject: string // 邮件主题
    templateOptions: EmailTemplateMapOptions[T] // 模板参数
    template: T // 默认模板
  }): Promise<SMTPTransport.SentMessageInfo> {
    // 读取配置（默认模板）
    const { emails, subject, template, templateOptions } = options
    if (!emails || (Array.isArray(emails) && emails.length === 0)) {
      throw new Error('收件人不能为空')
    }

    // 收件人列表
    const email
      = Array.isArray(emails)
        ? emails.reduce((pv, cv) => {
          return `${pv},${cv}`
        })
        : emails

    // 邮件内容
    const wrapperContent = wrapperContentTemplate<T>(template, templateOptions)
    // 发送邮件
    return this.transporter.sendMail({
      from: `app-publisher<${config.email.emailUser}>`,
      to: email,
      subject,
      html: wrapperContent,
    })
  }
}

export const sendEmail = async <T extends EmailTemplate>(options: {
  emails: string[] | string // 收件人
  subject: string // 邮件主题
  templateOptions: EmailTemplateMapOptions[T] // 模板参数
  template: T // 默认模板
}) => {
  const mailer = new Mail()
  const isVerify = mailer.verify()
  if (!isVerify) {
    throw new Error('邮件服务未连接，请检查配置')
  }
  try {
    await mailer.sendEmail(options)
  } catch (error) {
    throw new Error(`邮件发送失败:${error && error.message}`)
  }
  mailer.close()
}

// 模板类型
export enum EmailTemplate {
  DEFAULT = 'default',
  REST_PASSWORD = 'restPassword',
  INVITE_MEMBER = 'inviteMember',
}

// 模板参数类型
export interface EmailTemplateMapOptions {
  default: { content: string }
  restPassword: { content: string }
  inviteMember: {
    inviteUrl: string
    teamName: string
    // 邀请人
    inviter?: string
    // 被邀请人
    invitee?: string
  }
  // ...其他模板的选项类型
}
/**
 * 包装邮件内容模板<ejs模板引擎>
 * @param templateName
 * @param templateOptions
 * @returns
 */
export const wrapperContentTemplate = <T extends EmailTemplate> (templateName: T, templateOptions: EmailTemplateMapOptions[T]): string => {
  // 读取EJS模板文件
  const template = fs.readFileSync(`${emailTemplatePath}/${templateName}.ejs`, 'utf-8')
  // 编译EJS模板
  const compiledTemplate = ejs.compile(template)
  return compiledTemplate({ ...templateOptions })
}
