import compose from 'koa-compose'
import type { Context, Middleware, Next } from 'koa'

export default class MiddleHelper {
  /**
   * 是否跳过中间件
   * if(condition) 满足条件跳过
   * unless(condition) 除了条件之外跳过
   * @param middle 中间件
   */
  skip(
    ...middle: Middleware[]
  ): Middleware & {
    if: (condition: (ctx: Context) => boolean) => Middleware
    unless: (condition: (ctx: Context) => boolean) => Middleware
  } {
    return Object.create(Object.prototype, {
      if: {
        value: (condition: (ctx: Context) => boolean) => {
          return (ctx: Context, next: Next) => {
            return condition(ctx) ? next() : compose(middle)(ctx, next)
          }
        },
      },
      unless: {
        value: (condition: (ctx: Context) => boolean) => {
          return (ctx: Context, next: Next) => {
            return condition(ctx) ? compose(middle)(ctx, next) : next()
          }
        },
      },
    })
  }

  use(
    ...middle: Middleware[]
  ): Middleware & {
    if: (condition: (ctx: Context) => boolean) => Middleware
    unless: (condition: (ctx: Context) => boolean) => Middleware
  } {
    return Object.create(Object.prototype, {
      if: {
        value: (condition: (ctx: Context) => boolean) => {
          return (ctx: Context, next: Next) => {
            return condition(ctx) ? compose(middle)(ctx, next) : next()
          }
        },
      },
    })
  }
}
