import { join } from 'node:path'
import process from 'node:process'
import { FolderPathsMap } from '@cfe-node/utils'
import cors from '@koa/cors'
import Koa from 'koa'
import { koaBody } from 'koa-body'
import BodyParser from 'koa-bodyparser'
import convert from 'koa-convert'
import helmet from 'koa-helmet'
import Static from 'koa-static'
import config, { env } from './config'
import { closeConnection, setupConnection } from './config/database'
import {
  authentication,
  errorHandler,
  loggerHander,
  rateLimitMiddleware,
  staticPage,
} from './middlewares'
import { router } from './routes'

// import 'reflect-metadata'

const TAG = 'app.ts===>'

// 创建 Koa 应用实例
const app = new Koa()

// 输出环境变量
console.dir(TAG, `env: ${env}`)
console.log(TAG, `设置静态路由：${staticPage.publicRoot}`)
console.log(TAG, `设置静态路由：${staticPage.fileDir}`)
console.dir(FolderPathsMap)

// 初始化数据库连接 如果在生产环境连接失败，则退出进程
setupConnection().catch((err: unknown) => {
  console.error('Failed to connect to database:', err)
  if (process.env.NODE_ENV === 'production') {
    process.exit(1)
  }
})

// 处理 SIGTERM 信号（优雅关闭）
process.on('SIGTERM', async () => {
  console.log('SIGTERM signal received.')
  await closeConnection()
  process.exit(0)
})

// 处理 SIGINT 信号（Ctrl+C）
process.on('SIGINT', async () => {
  console.log('SIGINT signal received.')
  await closeConnection()
  process.exit(0)
})

app
  .use(loggerHander) // 日志
  .use(errorHandler) // 错误处理
  // .use(Authentication.error) // 认证401
  .use(convert(cors())) // 跨域访问
  .use(Static(staticPage.staticRoot)) // 静态文件访问路径
  .use(Static(staticPage.fileDir)) // 上传文件、图片访问路径
  // 静态文件服务中间件
  .use(
    Static(staticPage.publicRoot, {
      index: 'index.html', // 默认索引文件
      defer: false, // 立即处理静态文件请求
      maxage: 0, // 缓存时间
      gzip: true, // 启用 gzip 压缩
    }),
  )
  .use(staticPage.syncToClientHtml) // 访问页面处理
  .use(BodyParser()) // json解析
  .use(authentication.needAuth) // jwt认证
  .use(authentication.verifyApiToken) // 校验apiToken
  .use(router.routes())
  .use(router.allowedMethods())
  .use(router.createSwaggerUI()) // Swagger UI 界面

// 安全相关中间件
app.use(rateLimitMiddleware) // 限制请求频率

// 配置 helmet 的 CSP
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ['\'self\''],
        scriptSrc: ['\'self\'', '\'unsafe-inline\'', 'unpkg.com', 'cdnjs.cloudflare.com'],
        styleSrc: ['\'self\'', '\'unsafe-inline\'', 'unpkg.com', 'cdnjs.cloudflare.com'],
        imgSrc: ['\'self\'', 'data:', 'unpkg.com', 'cdnjs.cloudflare.com'],
        connectSrc: ['\'self\''],
        fontSrc: ['\'self\''],
        objectSrc: ['\'none\''],
        mediaSrc: ['\'self\''],
        frameSrc: ['\'self\''],
      },
    },
  }),
)

app.use(
  koaBody({
    multipart: true, // 支持文件上传
    formidable: {
      maxFileSize: Number(process.env.UPLOAD_MAX_SIZE) || 200 * 1024 * 1024, // 最大文件大小
      uploadDir: config.server.fileDir, // 上传目录
      keepExtensions: true, // 保留文件扩展名
    },
  }),
)

// log
console.log('test', 'process.env.NODE_ENV', process.env.NODE_ENV)

console.log('test', router.stack.map((i) => i.path))

const shouldListen = process.env.IN_DOCKER === 'true' || process.env.NODE_ENV !== 'development'
if (shouldListen) {
  const port = Number(process.env.PORT) || 3000
  const host = '0.0.0.0'
  app.listen(port, host, () => {
    console.log(`[server] listening on http://${host}:${port} (${process.env.NODE_ENV})`)
  })
}

export const viteNodeApp = app
