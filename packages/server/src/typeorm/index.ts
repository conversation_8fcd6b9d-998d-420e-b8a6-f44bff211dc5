import type { DataSourceOptions } from 'typeorm'
import { DataSource } from 'typeorm'
import config from '../config'
import { entities } from './entity'
import 'reflect-metadata'

// MySQL 数据库连接配置
export const mysqlOptions: DataSourceOptions = {
  type: 'mysql',
  host: config.env.DB_HOST || 'localhost',
  port: config.env.DB_PORT || 3306,
  username: config.env.DB_USER || 'root',
  password: config.env.DB_PASSWORD || 'root',
  database: config.env.DB_NAME || 'cfe_node_server',

  // 连接池配置
  extra: {
    connectionLimit: 10, // 最大连接数
    acquireTimeout: 10000, // 获取连接超时时间
    timeout: 10000, // 查询超时时间
    reconnect: true, // 自动重连
  },

  // 开发环境配置
  synchronize: config.env.DB_SYNC || false, // 生产环境应设为 false
  logging: config.isDebug ? ['query', 'error', 'schema'] : false,

  // 实体配置
  entities, // 实体类数组
  migrations: ['src/typeorm/migration/*.ts'],
  subscribers: ['src/typeorm/subscriber/*.ts'],

  // MySQL 特定配置
  charset: 'utf8mb4',
  timezone: '+08:00', // 东八区时间

  // 性能优化配置
  cache: config.env.REDIS_ENABLED
    ? {
        duration: 30000, // 缓存 30 秒
        type: 'redis', // 使用 Redis 缓存
        options: {
          host: config.env.REDIS_HOST || 'localhost',
          port: config.env.REDIS_PORT || 6379,
          password: config.env.REDIS_PASSWORD || '',
          db: config.env.REDIS_DB || 0,
        },
      }
    : false,
}

// 创建数据库连接
export const createAppDataSource = async (): Promise<DataSource> => {
  console.log(
    'TypeORM AppDataSource createAppDataSource (MySQL) =>',
    `${mysqlOptions.host}:${mysqlOptions.port}/${mysqlOptions.database}`,
  )

  try {
    const dataSource: DataSource = await new DataSource(mysqlOptions).initialize()
    console.log(
      'TypeORM AppDataSource createAppDataSource =>',
      `dataSource.isInitialized:${dataSource.isInitialized}`,
    )

    // 验证数据库连接
    await dataSource.query('SELECT 1')
    console.log('MySQL database connection verified successfully')

    return dataSource
  } catch (error) {
    console.error('TypeORM AppDataSource createAppDataSource error =>', error)
    throw error
  }
}

// 获取全局数据源实例
let globalDataSource: DataSource | null = null

export const getDataSource = async (): Promise<DataSource> => {
  if (!globalDataSource || !globalDataSource.isInitialized) {
    globalDataSource = await createAppDataSource()
  }
  return globalDataSource
}

// 关闭数据库连接
export const closeDataSource = async (): Promise<void> => {
  if (globalDataSource && globalDataSource.isInitialized) {
    await globalDataSource.destroy()
    globalDataSource = null
    console.log('MySQL database connection closed')
  }
}

// 健康检查
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    const dataSource = await getDataSource()
    await dataSource.query('SELECT 1')
    return true
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}

export * from './entity'
export * from './model'
