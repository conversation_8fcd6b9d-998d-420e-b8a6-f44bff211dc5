/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-22 11:37:14
 * @LastEditTime: 2025-07-10 18:59:44
 * @LastEditors: shaojun
 * @Description: TypeORM MySQL 通用基类和工具定义
 */

import { z } from '@cfe-node/koa-swagger-decorator'
import {
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  BaseEntity as TypeOrmBaseEntity,
  UpdateDateColumn,
} from 'typeorm'

/**
 * MySQL 基础实体类
 * 包含通用字段：id, createdAt, updatedAt, deletedAt
 */
export abstract class BaseEntity extends TypeOrmBaseEntity {
  @PrimaryGeneratedColumn()
  id: number

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @DeleteDateColumn()
  deletedAt?: Date

  constructor() {
    super()
    this.id = 0
    this.createdAt = new Date()
    this.updatedAt = new Date()
  }
}

/**
 * 分页查询 Schema
 */
export const pageSchema = z.object({
  pageIndex: z.number().min(1).optional().openapi({
    description: '页码，从1开始',
    example: 1,
  }),
  pageSize: z.number().min(1).max(100).optional().openapi({
    description: '每页数量，最大100',
    example: 10,
  }),
  totalCounts: z.number().optional().openapi({
    description: '总数量',
    example: 100,
  }),
})

/**
 * 分页响应 Schema
 */
export const paginationSchema = z.object({
  page: z.number().openapi({ description: '当前页码' }),
  pageSize: z.number().openapi({ description: '每页数量' }),
  total: z.number().openapi({ description: '总数量' }),
  totalPages: z.number().openapi({ description: '总页数' }),
})

/**
 * 基础响应 Schema
 */
export const baseResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    code: z.number().openapi({ description: '响应码，0表示成功' }),
    message: z.string().openapi({ description: '响应消息' }),
    data: dataSchema.optional().openapi({ description: '响应数据' }),
  })

/**
 * 分页响应 Schema
 */
export const paginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  baseResponseSchema(z.object({
    items: z.array(itemSchema).openapi({ description: '数据列表' }),
    pagination: paginationSchema.openapi({ description: '分页信息' }),
  }))

/**
 * 时间范围查询 Schema
 */
export const dateRangeSchema = z.object({
  startDate: z.string().datetime().optional().openapi({
    description: '开始时间 (ISO 8601)',
    example: '2024-01-01T00:00:00Z',
  }),
  endDate: z.string().datetime().optional().openapi({
    description: '结束时间 (ISO 8601)',
    example: '2024-12-31T23:59:59Z',
  }),
})
