/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-04-19 13:02:00
 * @LastEditTime: 2025-07-20 21:04:31
 * @LastEditors: shaojun
 * @Description: 应用实体 - MySQL 版本 (优化版)
 */
import { z } from '@cfe-node/koa-swagger-decorator'
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm'
import { IpTypeEnum, UpdateModeEnum } from '../enums'

import { Build } from './build'
import { BaseEntity } from './common'
import { Download } from './download'

// 临时定义IpaAppInfo接口，直到找到正确的导入路径
interface IpaAppInfo {
  platform: string
  bundleId: string
  bundleName?: string
  appName?: string
  appLevel?: string
  currentVersion?: string
}

@Entity('apps')
@Index(['platform']) // 平台索引，用于按平台查询
@Index(['creatorId']) // 创建者索引，查询用户创建的应用
@Index(['ownerId']) // 拥有者索引，查询用户拥有的应用
@Index(['platform', 'creatorId']) // 复合索引，按平台和创建者查询
@Index(['createdAt']) // 时间索引，用于排序和范围查询
@Index(['totalDownloadCount']) // 下载次数索引，用于热门应用排序
@Index(['autoPublish', 'platform']) // 复合索引，查询自动发布的应用
@Index(['appName']) // 应用名称索引，用于搜索
export class App extends BaseEntity {
  constructor() {
    super()
    this.totalDownloadCount = 0
    this.autoPublish = false
    this.installWithPwd = false
    this.updateMode = UpdateModeEnum.normal
    this.appLevel = 'normal'
    this.bundleName = ''
    this.describe = undefined
    this.currentVersion = '1.0.0'
    this.status = 'active'
    this.appSize = 0
    this.ratingCount = 0
  }

  @Column({
    type: 'varchar',
    length: 20,
    comment: '平台类型 (iOS/Android)',
  })
  platform!: string

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: '应用包标识符',
  })
  bundleId!: string

  @Column({
    type: 'varchar',
    length: 255,
    default: '',
    comment: '包名称',
  })
  bundleName: string

  @Column({
    type: 'varchar',
    length: 255,
    comment: '应用名称',
  })
  appName!: string

  @Column({
    type: 'varchar',
    length: 50,
    default: '1.0.0',
    comment: '当前版本号',
  })
  currentVersion: string

  @Column({
    type: 'int',
    nullable: true,
    comment: '创建者用户ID',
  })
  creatorId?: number

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '创建者名称 (冗余字段)',
  })
  creator?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '应用图标URL',
  })
  icon?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '应用描述',
  })
  describe?: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '短链接',
  })
  shortUrl?: string

  @Column({
    type: 'boolean',
    default: false,
    comment: '是否自动发布',
  })
  autoPublish: boolean

  @Column({
    type: 'boolean',
    default: false,
    comment: '安装是否需要密码',
  })
  installWithPwd: boolean

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '安装密码',
  })
  installPwd?: string

  @Column({
    type: 'varchar',
    length: 50,
    default: 'normal',
    comment: '应用等级',
  })
  appLevel: string

  @Column({
    type: 'int',
    nullable: true,
    comment: '拥有者用户ID',
  })
  ownerId?: number

  @Column({
    type: 'text',
    nullable: true,
    comment: '更新日志',
  })
  changelog?: string

  @Column({
    type: 'enum',
    enum: UpdateModeEnum,
    default: UpdateModeEnum.normal,
    comment: '更新模式',
  })
  updateMode: UpdateModeEnum

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '当前发布版本代码',
  })
  releaseVersionCode?: string

  @Column({
    type: 'int',
    nullable: true,
    comment: '当前发布版本ID',
  })
  releaseVersionId?: number

  @Column({
    type: 'int',
    nullable: true,
    comment: '灰度发布版本ID',
  })
  grayReleaseVersionId?: number

  @Column({
    type: 'bigint',
    default: 0,
    comment: '总下载次数',
  })
  totalDownloadCount: number

  @Column({
    type: 'varchar',
    length: 50,
    default: 'active',
    comment: '应用状态 (active/inactive/archived/deleted)',
  })
  status: string

  @Column({
    type: 'int',
    default: 0,
    comment: '应用大小 (字节)',
  })
  appSize: number

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '应用分类',
  })
  category?: string

  @Column({
    type: 'json',
    nullable: true,
    comment: '应用标签 (JSON数组)',
  })
  tags?: string

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
    comment: '应用评分 (1.00-5.00)',
  })
  rating?: number

  @Column({
    type: 'int',
    default: 0,
    comment: '评分人数',
  })
  ratingCount: number

  @Column({
    type: 'datetime',
    nullable: true,
    comment: '最后发布时间',
  })
  lastReleaseAt?: Date

  // 优化关系映射：应用创建者
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'creatorId' })
  creator_user!: Promise<any> // User类型

  // 优化关系映射：应用拥有者
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'ownerId' })
  owner!: Promise<any> // User类型

  // 优化关系映射：应用版本
  @OneToMany('Version', 'app', {
    lazy: true,
    cascade: true,
  })
  versions!: Promise<any[]> // Version[]类型

  // 优化关系映射：构建记录
  @OneToMany(() => Build, (build) => build.app, {
    lazy: true,
  })
  builds!: Promise<Build[]>

  // 优化关系映射：下载记录
  @OneToMany(() => Download, (download) => download.app, {
    lazy: true,
  })
  downloads!: Promise<Download[]>

  // 优化关系映射：每日统计
  @OneToMany(() => AppDailyStat, (stat) => stat.app, {
    lazy: true,
  })
  dailyStats!: Promise<AppDailyStat[]>

  // 优化关系映射：灰度策略
  @OneToMany(() => GrayStrategy, (strategy) => strategy.app, {
    lazy: true,
    cascade: true,
  })
  grayStrategies!: Promise<GrayStrategy[]>

  // 便捷方法：设置应用信息
  setAppInfo(info: IpaAppInfo): void {
    this.platform = info.platform
    this.bundleId = info.bundleId
    this.bundleName = info.bundleName || ''
    this.appName = info.appName || ''
    this.appLevel = info.appLevel || 'normal'
    this.currentVersion = info.currentVersion || '1.0.0'
  }

  // 便捷方法：增加下载次数
  incrementDownloadCount(count: number = 1): void {
    this.totalDownloadCount += count
  }

  // 便捷方法：检查是否为拥有者
  isOwnedBy(userId: number): boolean {
    return this.ownerId === userId || this.creatorId === userId
  }

  // 便捷方法：检查是否为创建者
  isCreatedBy(userId: number): boolean {
    return this.creatorId === userId
  }

  // 便捷方法：检查应用是否活跃
  isActive(): boolean {
    return this.status === 'active'
  }

  // 便捷方法：检查是否已发布
  isReleased(): boolean {
    return !!this.releaseVersionId
  }

  // 便捷方法：检查是否有灰度发布
  hasGrayRelease(): boolean {
    return !!this.grayReleaseVersionId
  }

  // 便捷方法：获取标签列表
  getTags(): string[] {
    try {
      return this.tags ? JSON.parse(this.tags) : []
    } catch {
      return []
    }
  }

  // 便捷方法：设置标签列表
  setTags(tags: string[]): void {
    this.tags = JSON.stringify(tags)
  }

  // 便捷方法：添加标签
  addTag(tag: string): void {
    const tags = this.getTags()
    if (!tags.includes(tag)) {
      tags.push(tag)
      this.setTags(tags)
    }
  }

  // 便捷方法：移除标签
  removeTag(tag: string): void {
    const tags = this.getTags()
    const index = tags.indexOf(tag)
    if (index > -1) {
      tags.splice(index, 1)
      this.setTags(tags)
    }
  }

  // 便捷方法：更新评分
  updateRating(newRating: number): void {
    const totalScore = (this.rating || 0) * this.ratingCount + newRating
    this.ratingCount += 1
    this.rating = Number((totalScore / this.ratingCount).toFixed(2))
  }

  // 便捷方法：获取应用显示名称
  getDisplayName(): string {
    return this.appName || this.bundleId
  }

  // 便捷方法：获取下载量等级
  getDownloadLevel(): string {
    if (this.totalDownloadCount >= 100000) {
      return 'hot'
    }
    if (this.totalDownloadCount >= 10000) {
      return 'popular'
    }
    if (this.totalDownloadCount >= 1000) {
      return 'normal'
    }
    return 'new'
  }

  // 便捷方法：格式化应用大小
  getFormattedSize(): string {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = this.appSize
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`
  }

  // 便捷方法：设置最后发布时间
  setLastReleaseTime(): void {
    this.lastReleaseAt = new Date()
  }

  // 便捷方法：归档应用
  archive(): void {
    this.status = 'archived'
  }

  // 便捷方法：激活应用
  activate(): void {
    this.status = 'active'
  }

  // 便捷方法：停用应用
  deactivate(): void {
    this.status = 'inactive'
  }
}

/**
 * 每日下载统计实体 (优化版)
 */
@Entity('app_daily_stats')
@Index(['appId', 'statDate'], { unique: true }) // 复合唯一索引，确保每天每个应用只有一条记录
@Index(['statDate']) // 日期索引，用于时间范围查询
@Index(['downloadCount']) // 下载次数索引，用于排序
@Index(['appId', 'downloadCount']) // 复合索引，查询应用的下载排名
export class AppDailyStat extends BaseEntity {
  constructor(appId?: number, statDate?: Date) {
    super()
    if (appId) {
      this.appId = appId
    }
    if (statDate) {
      this.statDate = statDate
    } else {
      this.statDate = new Date()
    }
    this.downloadCount = 0
    this.viewCount = 0
    this.uniqueVisitors = 0
    this.conversionRate = 0
  }

  @Column({
    type: 'int',
    comment: '应用ID',
  })
  appId!: number

  @Column({
    type: 'date',
    comment: '统计日期',
  })
  statDate: Date

  @Column({
    type: 'bigint',
    default: 0,
    comment: '当日下载次数',
  })
  downloadCount: number

  @Column({
    type: 'bigint',
    default: 0,
    comment: '当日查看次数',
  })
  viewCount: number

  @Column({
    type: 'bigint',
    default: 0,
    comment: '当日独立访客数',
  })
  uniqueVisitors: number

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
    comment: '转化率 (下载/查看)',
  })
  conversionRate: number

  @Column({
    type: 'json',
    nullable: true,
    comment: '小时级别统计数据 (JSON对象)',
  })
  hourlyStats?: string

  // 关系映射
  @ManyToOne(() => App, (app) => app.dailyStats)
  @JoinColumn({ name: 'appId' })
  app!: App

  // 便捷方法：增加下载次数
  incrementDownload(count: number = 1): void {
    this.downloadCount += count
    this.updateConversionRate()
  }

  // 便捷方法：增加查看次数
  incrementView(count: number = 1): void {
    this.viewCount += count
    this.updateConversionRate()
  }

  // 便捷方法：增加独立访客
  incrementUniqueVisitor(count: number = 1): void {
    this.uniqueVisitors += count
  }

  // 便捷方法：更新转化率
  updateConversionRate(): void {
    this.conversionRate = this.viewCount > 0
      ? Number((this.downloadCount / this.viewCount * 100).toFixed(2))
      : 0
  }

  // 便捷方法：获取小时统计
  getHourlyStats(): Record<string, any> {
    try {
      return this.hourlyStats ? JSON.parse(this.hourlyStats) : {}
    } catch {
      return {}
    }
  }

  // 便捷方法：设置小时统计
  setHourlyStats(stats: Record<string, any>): void {
    this.hourlyStats = JSON.stringify(stats)
  }

  // 便捷方法：检查是否为今天的统计
  isToday(): boolean {
    const today = new Date().toDateString()
    return this.statDate.toDateString() === today
  }
}

/**
 * 灰度策略实体 (优化版)
 */
@Entity('gray_strategies')
@Index(['appId']) // 应用索引
@Index(['active']) // 状态索引，查询活跃策略
@Index(['ipType']) // IP类型索引
@Index(['appId', 'active']) // 复合索引，查询应用的活跃策略
export class GrayStrategy extends BaseEntity {
  constructor(appId?: number) {
    super()
    if (appId) {
      this.appId = appId
    }
    this.ipType = IpTypeEnum.white
    this.updateMode = UpdateModeEnum.normal
    this.active = true
    this.count = 0
    this.downloadCountLimit = 0
    this.percentage = 100
  }

  @Column({
    type: 'int',
    comment: '应用ID',
  })
  appId!: number

  @Column({
    type: 'enum',
    enum: IpTypeEnum,
    default: IpTypeEnum.white,
    comment: 'IP类型 (白名单/黑名单)',
  })
  ipType: IpTypeEnum

  @Column({
    type: 'bigint',
    default: 0,
    comment: '用户数量限制',
  })
  count: number

  @Column({
    type: 'text',
    nullable: true,
    comment: 'IP列表 (JSON数组)',
  })
  ipList?: string

  @Column({
    type: 'bigint',
    default: 0,
    comment: '下载次数限制',
  })
  downloadCountLimit: number

  @Column({
    type: 'enum',
    enum: UpdateModeEnum,
    default: UpdateModeEnum.normal,
    comment: '更新模式',
  })
  updateMode: UpdateModeEnum

  @Column({
    type: 'boolean',
    default: true,
    comment: '是否启用',
  })
  active: boolean

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '策略名称',
  })
  name?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '策略描述',
  })
  description?: string

  @Column({
    type: 'int',
    default: 100,
    comment: '灰度百分比 (0-100)',
  })
  percentage: number

  @Column({
    type: 'datetime',
    nullable: true,
    comment: '策略开始时间',
  })
  startTime?: Date

  @Column({
    type: 'datetime',
    nullable: true,
    comment: '策略结束时间',
  })
  endTime?: Date

  @Column({
    type: 'json',
    nullable: true,
    comment: '策略规则配置 (JSON对象)',
  })
  rules?: string

  // 关系映射
  @ManyToOne(() => App, (app) => app.grayStrategies)
  @JoinColumn({ name: 'appId' })
  app!: App

  // 便捷方法：获取IP列表
  getIpList(): string[] {
    try {
      return this.ipList ? JSON.parse(this.ipList) : []
    } catch {
      return []
    }
  }

  // 便捷方法：设置IP列表
  setIpList(ips: string[]): void {
    this.ipList = JSON.stringify(ips)
  }

  // 便捷方法：检查IP是否在策略中
  isIpAllowed(ip: string): boolean {
    const ips = this.getIpList()
    if (ips.length === 0) {
      return true
    }

    const isInList = ips.includes(ip)
    return this.ipType === IpTypeEnum.white ? isInList : !isInList
  }

  // 便捷方法：检查策略是否在有效期内
  isValidTime(): boolean {
    const now = new Date()
    if (this.startTime && now < this.startTime) {
      return false
    }
    if (this.endTime && now > this.endTime) {
      return false
    }
    return true
  }

  // 便捷方法：检查策略是否有效
  isValid(): boolean {
    return this.active && this.isValidTime()
  }

  // 便捷方法：获取策略规则
  getRules(): Record<string, any> {
    try {
      return this.rules ? JSON.parse(this.rules) : {}
    } catch {
      return {}
    }
  }

  // 便捷方法：设置策略规则
  setRules(rules: Record<string, any>): void {
    this.rules = JSON.stringify(rules)
  }

  // 便捷方法：激活策略
  activate(): void {
    this.active = true
  }

  // 便捷方法：停用策略
  deactivate(): void {
    this.active = false
  }

  // 便捷方法：设置时间范围
  setTimeRange(startTime: Date, endTime: Date): void {
    this.startTime = startTime
    this.endTime = endTime
  }

  // 便捷方法：检查是否为永久策略
  isPermanent(): boolean {
    return !this.startTime && !this.endTime
  }
}

// Schema 定义 =======================================

/**
 * 每日统计 Schema
 */
export const AppDailyStatSchema = z.object({
  id: z.number().openapi({ description: '统计ID' }),
  appId: z.number().openapi({ description: '应用ID' }),
  statDate: z.date().openapi({ description: '统计日期' }),
  downloadCount: z.number().openapi({ description: '下载次数' }),
  viewCount: z.number().openapi({ description: '查看次数' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
})

/**
 * 灰度策略 Schema
 */
export const GrayStrategySchema = z.object({
  id: z.number().openapi({ description: '策略ID' }),
  appId: z.number().openapi({ description: '应用ID' }),
  name: z.string().optional().openapi({ description: '策略名称' }),
  description: z.string().optional().openapi({ description: '策略描述' }),
  ipType: z.nativeEnum(IpTypeEnum).openapi({ description: 'IP类型' }),
  count: z.number().optional().openapi({ description: '用户数量限制' }),
  ipList: z.array(z.string()).optional().openapi({ description: 'IP列表' }),
  downloadCountLimit: z.number().optional().openapi({ description: '下载次数限制' }),
  updateMode: z.nativeEnum(UpdateModeEnum).openapi({ description: '更新模式' }),
  active: z.boolean().openapi({ description: '是否启用' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
})

/**
 * 应用信息 Schema
 */
export const AppInfo = z.object({
  id: z.number().openapi({ description: '应用ID' }),
  platform: z.string().openapi({
    description: '平台类型',
    example: 'iOS',
  }),
  bundleId: z.string().openapi({
    description: '包标识符',
    example: 'com.example.app',
  }),
  bundleName: z.string().openapi({ description: '包名称' }),
  appName: z.string().openapi({
    description: '应用名称',
    example: 'Example App',
  }),
  currentVersion: z.string().openapi({
    description: '当前版本',
    example: '1.0.0',
  }),
  creatorId: z.number().optional().openapi({ description: '创建者ID' }),
  creator: z.string().optional().openapi({ description: '创建者名称' }),
  icon: z.string().optional().openapi({ description: '应用图标URL' }),
  describe: z.string().optional().openapi({ description: '应用描述' }),
  shortUrl: z.string().optional().openapi({ description: '短链接' }),
  autoPublish: z.boolean().openapi({ description: '是否自动发布' }),
  installWithPwd: z.boolean().openapi({ description: '安装是否需要密码' }),
  installPwd: z.string().optional().openapi({ description: '安装密码' }),
  appLevel: z.string().openapi({ description: '应用等级' }),
  ownerId: z.number().optional().openapi({ description: '拥有者ID' }),
  changelog: z.string().optional().openapi({ description: '更新日志' }),
  updateMode: z.nativeEnum(UpdateModeEnum).openapi({ description: '更新模式' }),
  releaseVersionCode: z.string().optional().openapi({ description: '发布版本代码' }),
  releaseVersionId: z.number().optional().openapi({ description: '发布版本ID' }),
  grayReleaseVersionId: z.number().optional().openapi({ description: '灰度版本ID' }),
  totalDownloadCount: z.number().openapi({ description: '总下载次数' }),
  dailyStats: z.array(AppDailyStatSchema).optional().openapi({ description: '每日统计' }),
  grayStrategies: z.array(GrayStrategySchema).optional().openapi({ description: '灰度策略' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 创建应用 Schema
 */
export const CreateAppSchema = z.object({
  platform: z.string().openapi({ description: '平台类型' }),
  bundleId: z.string().openapi({ description: '包标识符' }),
  appName: z.string().openapi({ description: '应用名称' }),
  appLevel: z.string().openapi({ description: '应用等级' }),
  icon: z.string().url().optional().openapi({ description: '应用图标URL' }),
  describe: z.string().optional().openapi({ description: '应用描述' }),
  autoPublish: z.boolean().optional().openapi({ description: '是否自动发布' }),
  installWithPwd: z.boolean().optional().openapi({ description: '是否需要安装密码' }),
  installPwd: z.string().optional().openapi({ description: '安装密码' }),
})

/**
 * 更新应用 Schema
 */
export const UpdateAppSchema = z.object({
  appName: z.string().optional().openapi({
    description: '应用名称',
    example: 'My App',
  }),
  bundleName: z.string().optional().openapi({
    description: '包名称',
    example: 'MyBundle',
  }),
  currentVersion: z.string().optional().openapi({
    description: '当前版本号',
    example: '1.2.0',
  }),
  icon: z.string().url().optional().openapi({
    description: '应用图标URL',
    example: 'https://example.com/icon.png',
  }),
  describe: z.string().optional().openapi({
    description: '应用描述',
  }),
  shortUrl: z.string().optional().openapi({
    description: '短链接',
    example: 'myapp123',
  }),
  autoPublish: z.boolean().optional().openapi({
    description: '是否自动发布',
  }),
  installWithPwd: z.boolean().optional().openapi({
    description: '安装是否需要密码',
  }),
  installPwd: z.string().optional().openapi({
    description: '安装密码',
  }),
  appLevel: z.string().optional().openapi({
    description: '应用等级',
    example: 'enterprise',
  }),
  changelog: z.string().optional().openapi({
    description: '更新日志',
  }),
  updateMode: z.nativeEnum(UpdateModeEnum).optional().openapi({
    description: '更新模式',
  }),
  releaseVersionCode: z.string().optional().openapi({
    description: '发布版本代码',
    example: 'v1.2.0-release',
  }),
})
