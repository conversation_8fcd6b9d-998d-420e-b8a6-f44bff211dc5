/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-07-20 20:41:59
 * @LastEditTime: 2025-07-20 20:59:20
 * @LastEditors: shaojun
 * @Description:
 */
/*
 * @Author: shaojun
 * @Date: 2024-04-19 13:02:00
 * @LastEditTime: 2024-05-09 10:29:16
 * @LastEditors: shaojun
 * @Description:
 */
import type { ObjectId } from 'typeorm'
import type { IpaAppInfo } from '@/utils/upload-utils'
import { z } from '@cfe-node/koa-swagger-decorator'
// import { z } from 'zod'

import { BaseEntity, Column, CreateDateColumn, Entity, ObjectIdColumn } from 'typeorm'
import { IpTypeEnum, UpdateModeEnum } from '../enums'

@Entity({ name: 'apps' })
export class App extends BaseEntity {
  @ObjectIdColumn()
  id: ObjectId

  @Column('string')
  platform: string

  @Column('string')
  bundleId: string

  @Column('string')
  bundleName: string

  @Column('string')
  appName: string

  @Column('string')
  currentVersion: string

  @Column('string')
  creatorId?: string

  @Column('string')
  creator?: string

  @CreateDateColumn()
  createAt?: Date

  @Column('string')
  icon?: string

  @Column('string')
  describe?: string

  @CreateDateColumn()
  updateAt?: Date

  @Column('string')
  shortUrl?: string

  @Column('bool')
  autoPublish?: boolean

  @Column('bool')
  installWithPwd?: boolean

  @Column('string')
  installPwd?: string

  @Column('string')
  appLevel: string

  @Column('string')
  ownerId?: string

  @Column('string')
  changelog?: string

  @Column({
    type: 'enum',
    enum: UpdateModeEnum,
    default: UpdateModeEnum.normal,
  })
  updateMode?: UpdateModeEnum

  @Column('string')
  releaseVersionCode?: string // 当前对外发布的code号

  @Column('string')
  releaseVersionId?: string // 当前对外发布的最新版本号

  @Column('string')
  grayReleaseVersionId?: string

  @Column('int64')
  totalDownloadCount?: number

  @Column((type) => TodayDownloadCount)
  todayDownloadCount?: TodayDownloadCount

  @Column((type) => GrayStrategy)
  grayStrategy?: GrayStrategy

  setAppInfo(info: IpaAppInfo): void {
    this.platform = info.platform
    this.bundleId = info.bundleId
    this.bundleName = info.bundleName || ''
    this.appName = info.appName || ''
    this.appLevel = info.appLevel || ''
    this.currentVersion = info.currentVersion || ''
  }
}
export class TodayDownloadCount {
  @CreateDateColumn()
  date: Date

  @Column('int64')
  count: number

  constructor(date: Date, count: number) {
    this.date = date
    this.count = count
  }
}
export class UpdateMode {
  @CreateDateColumn()
  date: Date

  @Column('int64')
  count: number

  constructor(date: Date, count: number) {
    this.date = date
    this.count = count
  }
}

export class GrayStrategy {
  @Column(
    {
      type: 'enum',
      enum: IpTypeEnum,
      default: IpTypeEnum.white,
    },
  )
  ipType?: IpTypeEnum

  @Column('int64')
  count?: number

  @Column('simple-array')
  ipList?: string[]

  @Column('int64')
  downloadCountLimit?: number

  @Column({
    type: 'enum',
    enum: UpdateModeEnum,
    default: UpdateModeEnum.normal,
  })
  updateMode?: UpdateModeEnum
}

export const TodayDownloadCountSchema = z.object({
  date: z.date(),
  count: z.number(),
})

export const GrayStrategySchema = z.object({
  ipType: z.nativeEnum(IpTypeEnum).optional(),
  count: z.number().optional(),
  ipList: z.array(z.string()).optional(),
  downloadCountLimit: z.number().optional(),
  updateMode: z.nativeEnum(UpdateModeEnum).optional(),
})

export const AppInfo = z.object({
  id: z.string(),
  platform: z.string(),
  bundleId: z.string(),
  bundleName: z.string(),
  appName: z.string(),
  currentVersion: z.string(),
  creatorId: z.string().optional(),
  creator: z.string().optional(),
  createAt: z.date().optional(),
  icon: z.string().optional(),
  describe: z.string().optional(),
  updateAt: z.date().optional(),
  shortUrl: z.string().optional(),
  autoPublish: z.boolean().optional(),
  installWithPwd: z.boolean().optional(),
  installPwd: z.string().optional(),
  appLevel: z.string(),
  ownerId: z.string().optional(),
  changelog: z.string().optional(),
  updateMode: z.nativeEnum(UpdateModeEnum).optional(),
  releaseVersionCode: z.string().optional(),
  releaseVersionId: z.string().optional(),
  grayReleaseVersionId: z.string().optional(),
  totalDownloadCount: z.number().optional(),
  todayDownloadCount: TodayDownloadCountSchema.optional(),
  grayStrategy: GrayStrategySchema.optional(),
})
