import { z } from '@cfe-node/koa-swagger-decorator'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-22 13:19:20
 * @LastEditTime: 2025-07-11 13:13:32
 * @LastEditors: shaojun
 * @Description: 小程序实体 - MySQL 版本
 */
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './common'

@Entity('miniapps')
@Index(['platform'])
@Index(['creatorId'])
@Index(['ownerId'])
export class Miniapp extends BaseEntity {
  constructor(
    appName?: string,
    appId?: string,
    platform?: string,
    pagePath?: string,
    appEnv?: boolean,
    ownerId?: number,
    creatorId?: number,
    creator?: string,
  ) {
    super()
    // 初始化必需属性
    this.appName = appName || ''
    this.appId = appId || ''
    this.platform = platform || ''
    this.pagePath = pagePath || ''
    this.appEnv = appEnv || false
    if (ownerId) {
      this.ownerId = ownerId
    }
    if (creatorId) {
      this.creatorId = creatorId
    }
    if (creator) {
      this.creator = creator
    }
  }

  @Column({
    type: 'varchar',
    length: 255,
    comment: '小程序名称',
  })
  appName: string

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: '小程序ID',
  })
  appId: string

  @Column({
    type: 'varchar',
    length: 20,
    comment: '平台类型',
  })
  platform: string

  @Column({
    type: 'varchar',
    length: 500,
    comment: '页面路径',
  })
  pagePath: string

  @Column({
    type: 'boolean',
    default: false,
    comment: '应用环境',
  })
  appEnv: boolean

  @Column({
    type: 'int',
    nullable: true,
    comment: '拥有者用户ID',
  })
  ownerId?: number

  @Column({
    type: 'int',
    nullable: true,
    comment: '创建者用户ID',
  })
  creatorId?: number

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '创建者名称',
  })
  creator?: string

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '小程序密钥',
  })
  appSecret?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '小程序图标URL',
  })
  icon?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '小程序描述',
  })
  describe?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '更新日志',
  })
  changelog?: string

  // 关系映射：拥有者
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'ownerId' })
  owner!: Promise<any> // User类型

  // 关系映射：创建者
  @ManyToOne('User', { lazy: true })
  @JoinColumn({ name: 'creatorId' })
  creator_user!: Promise<any> // User类型

  // 关系映射：下载码图片
  @OneToMany(() => DownloadCodeImage, (downloadCode) => downloadCode.miniapp, {
    lazy: true,
    cascade: true,
  })
  downloadCodeImages!: Promise<DownloadCodeImage[]>

  // 便捷方法：检查是否为拥有者
  isOwnedBy(userId: number): boolean {
    return this.ownerId === userId || this.creatorId === userId
  }

  // 便捷方法：生成小程序链接
  generateMiniappUrl(): string {
    return `${this.platform}://miniapp/${this.appId}?path=${encodeURIComponent(this.pagePath)}`
  }
}

/**
 * 下载码图片实体
 */
@Entity('miniapp_download_codes')
@Index(['miniappId'])
@Index(['type'])
@Index(['env'])
export class DownloadCodeImage extends BaseEntity {
  constructor(
    miniappId?: number,
    type?: string,
    image?: string,
    env?: string,
    developer?: string,
    version?: string,
    desc?: string,
    pagePath?: string,
    searchQuery?: string,
  ) {
    super()
    // 初始化必需属性
    this.miniappId = miniappId || 0
    this.type = type || ''
    this.image = image || ''
    this.env = env || ''
    if (developer) {
      this.developer = developer
    }
    if (version) {
      this.version = version
    }
    if (desc) {
      this.desc = desc
    }
    if (pagePath) {
      this.pagePath = pagePath
    }
    if (searchQuery) {
      this.searchQuery = searchQuery
    }
  }

  @Column({
    type: 'int',
    comment: '小程序ID',
  })
  miniappId: number

  @Column({
    type: 'varchar',
    length: 50,
    comment: '下载码类型',
  })
  type: string

  @Column({
    type: 'varchar',
    length: 500,
    comment: '下载码图片URL',
  })
  image: string

  @Column({
    type: 'varchar',
    length: 50,
    comment: '环境标识',
  })
  env: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '开发者名称',
  })
  developer?: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '版本号',
  })
  version?: string

  @Column({
    type: 'text',
    nullable: true,
    comment: '描述信息',
  })
  desc?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '页面路径',
  })
  pagePath?: string

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '搜索查询参数',
  })
  searchQuery?: string

  // 关系映射：所属小程序
  @ManyToOne(() => Miniapp, (miniapp) => miniapp.downloadCodeImages)
  @JoinColumn({ name: 'miniappId' })
  miniapp!: Miniapp

  // 便捷方法：生成完整的下载链接
  generateDownloadUrl(): string {
    const baseUrl = this.pagePath || ''
    const query = this.searchQuery ? `?${this.searchQuery}` : ''
    return `${baseUrl}${query}`
  }
}

// Schema 定义 =======================================

/**
 * 下载码图片 Schema
 */
export const DownloadCodeImageInfo = z.object({
  id: z.number().openapi({ description: '下载码ID' }),
  miniappId: z.number().openapi({ description: '小程序ID' }),
  type: z.string().openapi({
    description: '下载码类型',
    example: 'qrcode',
  }),
  image: z.string().openapi({ description: '下载码图片URL' }),
  env: z.string().openapi({
    description: '环境标识',
    example: 'production',
  }),
  developer: z.string().optional().openapi({ description: '开发者名称' }),
  version: z.string().optional().openapi({
    description: '版本号',
    example: '1.0.0',
  }),
  desc: z.string().optional().openapi({ description: '描述信息' }),
  pagePath: z.string().optional().openapi({ description: '页面路径' }),
  searchQuery: z.string().optional().openapi({ description: '搜索查询参数' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 小程序信息 Schema
 */
export const MiniappInfo = z.object({
  id: z.number().openapi({ description: '小程序主键ID' }),
  appName: z.string().openapi({
    description: '小程序名称',
    example: '示例小程序',
  }),
  appId: z.string().openapi({
    description: '小程序ID',
    example: 'wx1234567890abcdef',
  }),
  platform: z.string().openapi({
    description: '平台类型',
    example: 'wechat',
  }),
  pagePath: z.string().openapi({ description: '页面路径' }),
  appEnv: z.boolean().openapi({ description: '应用环境' }),
  ownerId: z.number().optional().openapi({ description: '拥有者ID' }),
  creatorId: z.number().optional().openapi({ description: '创建者ID' }),
  creator: z.string().optional().openapi({ description: '创建者名称' }),
  appSecret: z.string().optional().openapi({ description: '小程序密钥' }),
  icon: z.string().optional().openapi({ description: '小程序图标' }),
  describe: z.string().optional().openapi({ description: '小程序描述' }),
  changelog: z.string().optional().openapi({ description: '更新日志' }),
  downloadCodeImages: z.array(DownloadCodeImageInfo).optional().openapi({
    description: '下载码列表',
  }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 创建小程序 Schema
 */
export const CreateMiniappSchema = z.object({
  appName: z.string().openapi({ description: '小程序名称' }),
  appId: z.string().openapi({ description: '小程序ID' }),
  platform: z.string().openapi({ description: '平台类型' }),
  pagePath: z.string().openapi({ description: '页面路径' }),
  appEnv: z.boolean().optional().openapi({ description: '应用环境' }),
  appSecret: z.string().optional().openapi({ description: '小程序密钥' }),
  icon: z.string().url().optional().openapi({ description: '小程序图标' }),
  describe: z.string().optional().openapi({ description: '小程序描述' }),
})

/**
 * 更新小程序 Schema
 */
export const UpdateMiniappSchema = z.object({
  appName: z.string().optional(),
  pagePath: z.string().optional(),
  appEnv: z.boolean().optional(),
  appSecret: z.string().optional(),
  icon: z.string().url().optional(),
  describe: z.string().optional(),
  changelog: z.string().optional(),
})

/**
 * 创建下载码 Schema
 */
export const CreateDownloadCodeSchema = z.object({
  miniappId: z.number().openapi({ description: '小程序ID' }),
  type: z.string().openapi({ description: '下载码类型' }),
  image: z.string().url().openapi({ description: '下载码图片URL' }),
  env: z.string().openapi({ description: '环境标识' }),
  developer: z.string().optional().openapi({ description: '开发者名称' }),
  version: z.string().optional().openapi({ description: '版本号' }),
  desc: z.string().optional().openapi({ description: '描述信息' }),
  pagePath: z.string().optional().openapi({ description: '页面路径' }),
  searchQuery: z.string().optional().openapi({ description: '搜索查询参数' }),
})
