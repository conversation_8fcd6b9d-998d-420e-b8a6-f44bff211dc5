import { z } from '@cfe-node/koa-swagger-decorator'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-22 13:11:32
 * @LastEditTime: 2025-07-11 13:17:41
 * @LastEditors: shaojun
 * @Description: 邀请实体 - MySQL 版本
 */
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { InviteStatusEnum, InviteTypeEnum } from '../enums'
import { BaseEntity } from './common'

@Entity('invites')
@Index(['teamId'])
@Index(['status'])
@Index(['type'])
export class Invite extends BaseEntity {
  constructor(userName?: string, teamId?: number, type?: InviteTypeEnum, status?: InviteStatusEnum) {
    super()
    // 初始化必需属性
    this.userName = userName || ''
    this.teamId = teamId || 0
    this.type = type || InviteTypeEnum.TEAM
    this.status = status || InviteStatusEnum.EBL
  }

  @Column({
    type: 'varchar',
    length: 100,
    comment: '邀请人名称',
  })
  userName: string

  @Column({
    type: 'int',
    comment: '团队ID',
  })
  teamId: number

  @Column({
    type: 'enum',
    enum: InviteTypeEnum,
    default: InviteTypeEnum.TEAM,
    comment: '邀请类型',
  })
  type: InviteTypeEnum

  @Column({
    type: 'enum',
    enum: InviteStatusEnum,
    default: InviteStatusEnum.EBL,
    comment: '邀请状态',
  })
  status: InviteStatusEnum

  // 关系映射：所属团队
  @ManyToOne('Team', { lazy: true })
  @JoinColumn({ name: 'teamId' })
  team!: Promise<any> // Team类型

  // 关系映射：邀请邮箱列表
  @OneToMany(() => InviteEmailStatus, (email) => email.invite, {
    lazy: true,
    cascade: true,
  })
  emails!: Promise<InviteEmailStatus[]>

  // 便捷方法：添加邮箱邀请
  async addEmail(email: string, status: InviteStatusEnum = InviteStatusEnum.DBL): Promise<void> {
    const emails = await this.emails
    const existingEmail = emails.find((e) => e.email === email)
    if (!existingEmail) {
      const newEmail = new InviteEmailStatus(this.id, email, status)
      emails.push(newEmail)
    }
  }

  // 便捷方法：检查邮箱是否已邀请
  async hasEmail(email: string): Promise<boolean> {
    const emails = await this.emails
    return emails.some((e) => e.email === email)
  }
}

/**
 * 邀请邮箱状态实体
 */
@Entity('invite_emails')
@Index(['inviteId'])
@Index(['email'])
@Index(['status'])
export class InviteEmailStatus extends BaseEntity {
  constructor(inviteId?: number, email?: string, status?: InviteStatusEnum) {
    super()
    // 初始化必需属性
    this.inviteId = inviteId || 0
    this.email = email || ''
    this.status = status || InviteStatusEnum.DBL
  }

  @Column({
    type: 'int',
    comment: '邀请ID',
  })
  inviteId: number

  @Column({
    type: 'varchar',
    length: 100,
    comment: '邀请邮箱',
  })
  email: string

  @Column({
    type: 'enum',
    enum: InviteStatusEnum,
    default: InviteStatusEnum.DBL,
    comment: '邮箱邀请状态',
  })
  status: InviteStatusEnum

  // 关系映射：所属邀请
  @ManyToOne(() => Invite, (invite) => invite.emails)
  @JoinColumn({ name: 'inviteId' })
  invite!: Invite

  // 便捷方法：标记为已发送
  markAsSent(): void {
    this.status = InviteStatusEnum.EBL
  }

  // 便捷方法：标记为已接受
  markAsAccepted(): void {
    this.status = InviteStatusEnum.EBL // 根据实际业务逻辑调整
  }
}

// Schema 定义 =======================================

/**
 * 邀请邮箱状态 Schema
 */
export const InviteEmailStatusInfo = z.object({
  id: z.number().openapi({ description: '邮箱状态ID' }),
  inviteId: z.number().openapi({ description: '邀请ID' }),
  email: z.string().email().openapi({
    description: '邀请邮箱',
    example: '<EMAIL>',
  }),
  status: z.nativeEnum(InviteStatusEnum).openapi({ description: '邀请状态' }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 邀请信息 Schema
 */
export const InviteInfo = z.object({
  id: z.number().openapi({ description: '邀请ID' }),
  userName: z.string().openapi({
    description: '邀请人名称',
    example: 'John Doe',
  }),
  teamId: z.number().openapi({ description: '团队ID' }),
  type: z.nativeEnum(InviteTypeEnum).openapi({ description: '邀请类型' }),
  status: z.nativeEnum(InviteStatusEnum).openapi({ description: '邀请状态' }),
  emails: z.array(InviteEmailStatusInfo).optional().openapi({
    description: '邀请邮箱列表',
  }),
  createdAt: z.date().openapi({ description: '创建时间' }),
  updatedAt: z.date().openapi({ description: '更新时间' }),
})

/**
 * 创建邀请 Schema
 */
export const CreateInviteSchema = z.object({
  teamId: z.number().openapi({ description: '团队ID' }),
  emails: z.array(z.string().email()).min(1).max(10).openapi({
    description: '邀请邮箱列表，最多10个',
    example: ['<EMAIL>', '<EMAIL>'],
  }),
  type: z.nativeEnum(InviteTypeEnum).optional().openapi({
    description: '邀请类型',
    default: InviteTypeEnum.TEAM,
  }),
})

/**
 * 更新邀请状态 Schema
 */
export const UpdateInviteStatusSchema = z.object({
  status: z.nativeEnum(InviteStatusEnum).openapi({ description: '新状态' }),
})

/**
 * 批量更新邮箱状态 Schema
 */
export const BatchUpdateEmailStatusSchema = z.object({
  emailIds: z.array(z.number()).openapi({ description: '邮箱状态ID列表' }),
  status: z.nativeEnum(InviteStatusEnum).openapi({ description: '新状态' }),
})

// InviteEmailStatus 已在上面定义和导出
