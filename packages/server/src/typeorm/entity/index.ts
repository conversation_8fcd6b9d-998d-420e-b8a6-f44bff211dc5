/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2023-12-20 14:25:41
 * @LastEditTime: 2025-07-15 15:08:01
 * @LastEditors: s<PERSON>jun
 * @Description: 实体导出文件 - MySQL 版本
 */

export * from './app'
export * from './build'
export * from './common'
export * from './download'
export * from './invite'
export * from './message'
export * from './miniapp'
export * from './team'
// 主要实体导出
export * from './user'
export * from './version'

import { App, AppDailyStat, AppDailyStatSchema, AppInfo, CreateAppSchema, GrayStrategy, GrayStrategySchema, UpdateAppSchema } from './app'
import { Build, BuildInfo, CreateBuildSchema } from './build'
import { CreateDownloadSchema, Download, DownloadInfo, DownloadStatSchema } from './download'
import { BatchUpdateEmailStatusSchema, CreateInviteSchema, Invite, InviteEmailStatus, InviteEmailStatusInfo, InviteInfo, UpdateInviteStatusSchema } from './invite'
import { CreateMessageSchema, Message, MessageInfo, MessageQuerySchema, UpdateMessageStatusSchema } from './message'

import { CreateDownloadCodeSchema, CreateMiniappSchema, DownloadCodeImage, DownloadCodeImageInfo, Miniapp, MiniappInfo, UpdateMiniappSchema } from './miniapp'
import { BatchInviteMemberSchema, CreateTeamSchema, InviteMemberSchema, Team, TeamInfo, TeamMember, TeamMemberSchema, UpdateMemberRoleSchema, UpdateTeamSchema } from './team'
// 导入所有实体类和 Schema
import {
  CreateUserSchema,
  LoginUserSchema,
  UpdateUserSchema,
  User,
  UserInfo,
  UserTeam,
  UserTeamSchema,
} from './user'
import { CreateVersionSchema, ReleaseVersionSchema, UpdateVersionSchema, Version, VersionInfo, VersionStatSchema } from './version'

// TypeORM 实体数组 - 用于数据库配置
export const entities = [
  // 核心实体
  User,
  Team,
  App,
  Build,
  Download,
  Invite,
  Message,
  Miniapp,
  Version,

  // 关系实体
  UserTeam,
  TeamMember,
  AppDailyStat,
  GrayStrategy,
  InviteEmailStatus,
  DownloadCodeImage,
]

// Zod Schema 全局集合 - 用于 API 文档生成
export const schemas = {
  // 主要实体 Schema
  UserInfo,
  TeamInfo,
  AppInfo,
  BuildInfo,
  DownloadInfo,
  InviteInfo,
  MessageInfo,
  MiniappInfo,
  VersionInfo,

  // 关系实体 Schema
  UserTeamSchema,
  TeamMemberSchema,
  AppDailyStatSchema,
  GrayStrategySchema,
  InviteEmailStatusInfo,
  DownloadCodeImageInfo,

  // 创建 Schema
  CreateUserSchema,
  CreateTeamSchema,
  CreateAppSchema,
  CreateBuildSchema,
  CreateDownloadSchema,
  CreateInviteSchema,
  CreateMessageSchema,
  CreateMiniappSchema,
  CreateVersionSchema,
  CreateDownloadCodeSchema,

  // 更新 Schema
  UpdateUserSchema,
  UpdateTeamSchema,
  UpdateAppSchema,
  UpdateInviteStatusSchema,
  UpdateMessageStatusSchema,
  UpdateMiniappSchema,
  UpdateVersionSchema,

  // 查询和特殊操作 Schema
  LoginUserSchema,
  InviteMemberSchema,
  BatchInviteMemberSchema,
  UpdateMemberRoleSchema,
  BatchUpdateEmailStatusSchema,
  MessageQuerySchema,
  ReleaseVersionSchema,
  VersionStatSchema,
  DownloadStatSchema,
}

// 实体类型映射 - 用于类型推断
export interface EntityTypes {
  User: User
  Team: Team
  App: App
  Build: Build
  Download: Download
  Invite: Invite
  Message: Message
  Miniapp: Miniapp
  Version: Version
  UserTeam: UserTeam
  TeamMember: TeamMember
  AppDailyStat: AppDailyStat
  GrayStrategy: GrayStrategy
  InviteEmailStatus: InviteEmailStatus
  DownloadCodeImage: DownloadCodeImage
}

// 实体名称常量
export const ENTITY_NAMES = {
  USER: 'User',
  TEAM: 'Team',
  APP: 'App',
  BUILD: 'Build',
  DOWNLOAD: 'Download',
  INVITE: 'Invite',
  MESSAGE: 'Message',
  MINIAPP: 'Miniapp',
  VERSION: 'Version',
  USER_TEAM: 'UserTeam',
  TEAM_MEMBER: 'TeamMember',
  APP_DAILY_STAT: 'AppDailyStat',
  GRAY_STRATEGY: 'GrayStrategy',
  INVITE_EMAIL_STATUS: 'InviteEmailStatus',
  DOWNLOAD_CODE_IMAGE: 'DownloadCodeImage',
} as const

// 表名常量
export const TABLE_NAMES = {
  USERS: 'users',
  TEAMS: 'teams',
  APPS: 'apps',
  BUILDS: 'builds',
  DOWNLOADS: 'downloads',
  INVITES: 'invites',
  MESSAGES: 'messages',
  MINIAPPS: 'miniapps',
  VERSIONS: 'versions',
  USER_TEAMS: 'user_teams',
  TEAM_MEMBERS: 'team_members',
  APP_DAILY_STATS: 'app_daily_stats',
  GRAY_STRATEGIES: 'gray_strategies',
  INVITE_EMAILS: 'invite_emails',
  MINIAPP_DOWNLOAD_CODES: 'miniapp_download_codes',
} as const

// 实体关系定义 - 用于文档和验证
export const ENTITY_RELATIONS = {
  User: {
    userTeams: 'UserTeam[]',
    createdApps: 'App[]',
    ownedApps: 'App[]',
    uploads: 'Version[]',
    messages: 'Message[]',
    downloads: 'Download[]',
    ownedMiniapps: 'Miniapp[]',
    createdMiniapps: 'Miniapp[]',
  },
  Team: {
    creator: 'User',
    userTeams: 'UserTeam[]',
    members: 'TeamMember[]',
    apps: 'App[]',
    invites: 'Invite[]',
  },
  App: {
    creator: 'User',
    owner: 'User',
    versions: 'Version[]',
    builds: 'Build[]',
    downloads: 'Download[]',
    dailyStats: 'AppDailyStat[]',
    grayStrategies: 'GrayStrategy[]',
  },
  Version: {
    app: 'App',
    uploader: 'User',
    downloads: 'Download[]',
  },
  Build: {
    app: 'App',
  },
  Download: {
    app: 'App',
    version: 'Version',
    user: 'User',
  },
  Message: {
    sender: 'User',
    receiver: 'User',
  },
  Invite: {
    team: 'Team',
    emails: 'InviteEmailStatus[]',
  },
  Miniapp: {
    owner: 'User',
    creator: 'User',
    downloadCodes: 'DownloadCodeImage[]',
  },
  UserTeam: {
    user: 'User',
    team: 'Team',
  },
  TeamMember: {
    user: 'User',
    team: 'Team',
  },
  AppDailyStat: {
    app: 'App',
  },
  GrayStrategy: {
    app: 'App',
  },
  InviteEmailStatus: {
    invite: 'Invite',
  },
  DownloadCodeImage: {
    miniapp: 'Miniapp',
  },
} as const

// 数据库配置相关常量
export const DATABASE_CONFIG = {
  // MySQL 相关配置
  MYSQL_CHARSET: 'utf8mb4',
  MYSQL_COLLATION: 'utf8mb4_unicode_ci',

  // 索引策略
  INDEX_STRATEGIES: {
    // 复合索引
    USER_EMAIL_USERNAME: ['email', 'username'],
    APP_BUNDLE_PLATFORM: ['bundleId', 'platform'],
    VERSION_APP_VERSION: ['appId', 'versionStr', 'versionCode'],
    DOWNLOAD_APP_DATE: ['appId', 'createdAt'],
    MESSAGE_RECEIVER_STATUS: ['receiverId', 'status'],

    // 唯一索引
    UNIQUE_INDEXES: {
      users: ['email', 'username'],
      apps: ['bundleId'],
      miniapps: ['appId'],
      user_teams: ['userId', 'teamId'],
      team_members: ['teamId', 'userId'],
      app_daily_stats: ['appId', 'statDate'],
    },
  },

  // 外键策略
  FOREIGN_KEY_ACTIONS: {
    ON_DELETE_CASCADE: ['user_teams', 'team_members', 'versions', 'builds', 'app_daily_stats', 'gray_strategies', 'invite_emails', 'miniapp_download_codes'],
    ON_DELETE_SET_NULL: ['apps.creatorId', 'apps.ownerId', 'versions.uploaderId', 'messages.senderId'],
  },
} as const

export const globals = {
  AppInfo,
  BuildInfo,
  DownloadInfo,
  InviteInfo,
  MessageInfo,
  MiniappInfo,
  TeamInfo,
  UserInfo,
  VersionInfo,
}
