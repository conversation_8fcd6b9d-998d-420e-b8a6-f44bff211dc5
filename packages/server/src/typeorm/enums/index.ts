export enum UpdateModeEnum {
  silent = 'silent',
  normal = 'normal',
  force = 'force',
}

export enum IpTypeEnum {
  white = 'white',
  black = 'black',
}

export enum RoleEnum {
  owner = 'owner',
  manager = 'manager',
  guest = 'guest',
}

// message 状态枚举
export enum MessageStatusEnum {
  unread = 'owner',
  hasread = 'hasread',
}
/**
 * @description: 邀请类型枚举
 */
export enum InviteTypeEnum {
  // 团队邀请
  TEAM = 'TEAM',
}
/**
 * @description: 邀请状态枚举
 */
export enum InviteStatusEnum {
  EBL = 'EBL', // 已经接受
  DBL = 'DBL', // 未接受
}
