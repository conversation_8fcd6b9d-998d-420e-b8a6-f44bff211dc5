/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-04-17 10:43:31
 * @LastEditTime: 2024-04-17 10:43:33
 * @LastEditors: shaojun
 * @Description:
 */

import Constants from '@/base/constants'

/**
 * @export
 * @interface DefaultObject
 */
export interface DefaultObject<T> {
  /**
   *
   * @type {object}
   * @memberof DefaultObject
   */
  data?: T
  /**
   *
   * @type {number}
   * @memberof DefaultObject
   */
  resultCode: number
  /**
   *
   * @type {string}
   * @memberof DefaultObject
   */
  resultMessage: string
}

/**
 * response封装
 * @param params (多参数)
 */
export const responseWrapper2 = (...params: [number, string, ...any]): DefaultObject<any> => {
  if (params.length === 3) {
    const success = params[0]
    const message = params[1]
    const data = params[2]
    return { resultCode: success, resultMessage: message, data }
  }
  // 只传2个参数,必须传是否成功 和 返回的提示信息
  if (params.length === 2) {
    const success = params[0]
    const message = params[1]
    return { resultCode: success, resultMessage: message }
  }
  // 如果只传一个参数 则默认当作请求成功 返回正常数据
  if (params.length === 1) {
    const data = params[0]
    return { resultCode: Constants.SUCCESS_CODE, resultMessage: '', data }
  }
  return { resultCode: Constants.ERROR_CODE_INTERNAL, resultMessage: 'responseWrapper 参数错误' }
}

/**
 * response封装
 * @param params (多参数)
 */
export const responseWrapper3 = <T>(res: { resultCode?: number, resultMessage?: string, data?: T }): DefaultObject<T> => {
  const { resultCode = Constants.SUCCESS_CODE, resultMessage = '', data } = res
  return {
    resultCode,
    resultMessage,
    data,
  }
}
