/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-11 11:17:30
 * @LastEditTime: 2025-07-11 11:25:07
 * @LastEditors: shaojun
 * @Description: 日期工具函数 - 使用 dayjs
 */

import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import relativeTime from 'dayjs/plugin/relativeTime'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import 'dayjs/locale/zh-cn' // 中文语言包

// 扩展 dayjs 插件
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)
dayjs.extend(relativeTime)

// 设置默认语言和时区
dayjs.locale('zh-cn')
dayjs.tz.setDefault('Asia/Shanghai')

/**
 * 判断两个日期是否为同一天
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 是否为同一天
 */
export const isOneDay = (date1: Date | string | Dayjs, date2: Date | string | Dayjs): boolean => {
  return dayjs(date1).isSame(dayjs(date2), 'day')
}

/**
 * 获取今天的开始时间 (00:00:00)
 * @param date 日期，默认为今天
 * @returns 今天的开始时间
 */
export const getStartOfDay = (date?: Date | string | Dayjs): Date => {
  return dayjs(date).startOf('day').toDate()
}

/**
 * 获取今天的结束时间 (23:59:59.999)
 * @param date 日期，默认为今天
 * @returns 今天的结束时间
 */
export const getEndOfDay = (date?: Date | string | Dayjs): Date => {
  return dayjs(date).endOf('day').toDate()
}

/**
 * 格式化日期为 YYYY-MM-DD 字符串
 * @param date 日期
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date?: Date | string | Dayjs): string => {
  return dayjs(date).format('YYYY-MM-DD')
}

/**
 * 判断是否为今天
 * @param date 要检查的日期
 * @returns 是否为今天
 */
export const isToday = (date: Date | string | Dayjs): boolean => {
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为昨天
 * @param date 要检查的日期
 * @returns 是否为昨天
 */
export const isYesterday = (date: Date | string | Dayjs): boolean => {
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断是否为明天
 * @param date 要检查的日期
 * @returns 是否为明天
 */
export const isTomorrow = (date: Date | string | Dayjs): boolean => {
  return dayjs(date).isSame(dayjs().add(1, 'day'), 'day')
}

/**
 * 获取指定日期是一周中的第几天
 * @param date 日期
 * @returns 0-6，0表示周日
 */
export const getDayOfWeek = (date?: Date | string | Dayjs): number => {
  return dayjs(date).day()
}

/**
 * 获取本周开始时间（周一 00:00:00）
 * @param date 日期，默认为今天
 * @returns 本周开始时间
 */
export const getStartOfWeek = (date?: Date | string | Dayjs): Date => {
  return dayjs(date).startOf('week').add(1, 'day').toDate() // dayjs 默认周日开始，调整为周一
}

/**
 * 获取本周结束时间（周日 23:59:59.999）
 * @param date 日期，默认为今天
 * @returns 本周结束时间
 */
export const getEndOfWeek = (date?: Date | string | Dayjs): Date => {
  return dayjs(date).endOf('week').add(1, 'day').toDate()
}

/**
 * 获取本月开始时间
 * @param date 日期，默认为今天
 * @returns 本月开始时间
 */
export const getStartOfMonth = (date?: Date | string | Dayjs): Date => {
  return dayjs(date).startOf('month').toDate()
}

/**
 * 获取本月结束时间
 * @param date 日期，默认为今天
 * @returns 本月结束时间
 */
export const getEndOfMonth = (date?: Date | string | Dayjs): Date => {
  return dayjs(date).endOf('month').toDate()
}

/**
 * 获取指定天数前的日期
 * @param days 天数
 * @param date 基准日期，默认为今天
 * @returns 指定天数前的日期
 */
export const getDaysAgo = (days: number, date?: Date | string | Dayjs): Date => {
  return dayjs(date).subtract(days, 'day').toDate()
}

/**
 * 获取指定天数后的日期
 * @param days 天数
 * @param date 基准日期，默认为今天
 * @returns 指定天数后的日期
 */
export const getDaysAfter = (days: number, date?: Date | string | Dayjs): Date => {
  return dayjs(date).add(days, 'day').toDate()
}

/**
 * 计算两个日期之间的天数差
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 天数差（正数表示 date1 在 date2 之后）
 */
export const getDaysDiff = (date1: Date | string | Dayjs, date2: Date | string | Dayjs): number => {
  return dayjs(date1).diff(dayjs(date2), 'day')
}

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (
  date?: Date | string | Dayjs,
  format: string = 'YYYY-MM-DD HH:mm:ss',
): string => {
  return dayjs(date).format(format)
}

/**
 * 解析日期字符串
 * @param dateString 日期字符串
 * @param format 格式字符串
 * @returns Date 对象
 */
export const parseDate = (dateString: string, format?: string): Date => {
  return format ? dayjs(dateString, format).toDate() : dayjs(dateString).toDate()
}

/**
 * 判断日期是否在指定范围内
 * @param date 要检查的日期
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 是否在范围内
 */
export const isDateInRange = (
  date: Date | string | Dayjs,
  startDate: Date | string | Dayjs,
  endDate: Date | string | Dayjs,
): boolean => {
  const target = dayjs(date)
  return target.isSameOrAfter(dayjs(startDate)) && target.isSameOrBefore(dayjs(endDate))
}

/**
 * 获取相对时间描述（如"2小时前"、"3天前"等）
 * @param date 日期
 * @param baseDate 基准日期，默认为现在
 * @returns 相对时间描述
 */
export const getRelativeTime = (
  date: Date | string | Dayjs,
  baseDate?: Date | string | Dayjs,
): string => {
  return dayjs(date).from(dayjs(baseDate))
}

/**
 * 创建 dayjs 实例
 * @param date 日期
 * @returns dayjs 实例
 */
export const createDayjs = (date?: Date | string | Dayjs): Dayjs => {
  return dayjs(date)
}

/**
 * 获取当前时间戳（毫秒）
 * @returns 当前时间戳
 */
export const now = (): number => {
  return dayjs().valueOf()
}

/**
 * 获取当前时间戳（秒）
 * @returns 当前时间戳（秒）
 */
export const nowInSeconds = (): number => {
  return dayjs().unix()
}
