import { dirname, join } from 'node:path'
import process from 'node:process'
import { fileURLToPath } from 'node:url'
import { createLogger } from '@cfe-node/utils'
import winston from 'winston'

const __dirname = dirname(fileURLToPath(import.meta.url))

export const logger = createLogger({
  logDir: join(__dirname, '../logs'),
  serviceName: 'server',
})

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple(),
  }))
}
