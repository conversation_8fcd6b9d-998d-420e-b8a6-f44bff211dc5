# 数据库设计文档

## 概述

本文档描述了 CFE Node Server 的 MySQL 数据库设计，包含核心业务实体和关系映射。系统主要用于移动应用分发平台，支持团队协作、应用管理、版本控制等功能。

## ER 图

```mermaid
erDiagram
    users {
        int id PK
        varchar username UK
        varchar userAvatar
        text userAvatarHistory
        varchar password
        varchar email UK
        varchar token
        varchar apiToken
        varchar mobile
        varchar qq
        varchar company
        varchar career
        datetime createdAt
        datetime updatedAt
        datetime deletedAt
    }

    teams {
        int id PK
        varchar name
        boolean isDefault
        varchar icon
        int creatorId FK
        datetime createdAt
        datetime updatedAt
        datetime deletedAt
    }

    user_teams {
        int id PK
        int userId FK
        int teamId FK
        varchar role
        datetime createdAt
        datetime updatedAt
    }

    apps {
        int id PK
        varchar platform
        varchar bundleId UK
        varchar bundleName
        varchar appName
        varchar currentVersion
        int creatorId FK
        varchar creator
        varchar icon
        text describe
        varchar shortUrl
        boolean autoPublish
        boolean installWithPwd
        varchar installPwd
        varchar appLevel
        int ownerId FK
        text changelog
        varchar updateMode
        varchar releaseVersionCode
        int releaseVersionId FK
        int grayReleaseVersionId FK
        bigint totalDownloadCount
        datetime createdAt
        datetime updatedAt
        datetime deletedAt
    }

    versions {
        int id PK
        int appId FK
        varchar bundleId
        varchar icon
        varchar versionStr
        varchar versionCode
        datetime uploadAt
        varchar uploader
        int uploaderId FK
        bigint size
        boolean active
        varchar downloadUrl
        bigint downloadCount
        varchar fileDownloadUrl
        varchar installUrl
        boolean showOnDownloadPage
        varchar appLevel
        text changelog
        varchar md5
        boolean hidden
        varchar updateMode
        boolean released
        datetime createdAt
        datetime updatedAt
        datetime deletedAt
    }

    builds {
        int id PK
        int appId FK
        varchar platform
        varchar bundleId
        bigint buildNo
        datetime uploadAt
        datetime createdAt
        datetime updatedAt
        datetime deletedAt
    }

    miniapps {
        int id PK
        varchar appName
        varchar appId UK
        varchar platform
        varchar pagePath
        boolean appEnv
        int ownerId FK
        int creatorId FK
        varchar creator
        varchar appSecret
        varchar icon
        text describe
        text changelog
        datetime createdAt
        datetime updatedAt
        datetime deletedAt
    }

    miniapp_download_codes {
        int id PK
        int miniappId FK
        varchar type
        varchar image
        varchar env
        varchar developer
        varchar version
        text desc
        varchar pagePath
        varchar searchQuery
        datetime createdAt
        datetime updatedAt
    }

    messages {
        int id PK
        varchar category
        text content
        int senderId FK
        int receiverId FK
        datetime sendAt
        varchar status
        text data
        datetime createdAt
        datetime updatedAt
        datetime deletedAt
    }

    invites {
        int id PK
        varchar userName
        int teamId FK
        varchar type
        varchar status
        datetime createdAt
        datetime updatedAt
        datetime deletedAt
    }

    invite_emails {
        int id PK
        int inviteId FK
        varchar email
        varchar status
        datetime createdAt
        datetime updatedAt
    }

    downloads {
        int id PK
        int appId FK
        int versionId FK
        int userId FK
        varchar clientIp
        varchar userAgent
        varchar downloadType
        datetime createdAt
        datetime updatedAt
    }

    app_daily_stats {
        int id PK
        int appId FK
        date statDate
        bigint downloadCount
        bigint viewCount
        datetime createdAt
        datetime updatedAt
    }

    gray_strategies {
        int id PK
        int appId FK
        varchar ipType
        bigint count
        text ipList
        bigint downloadCountLimit
        varchar updateMode
        boolean active
        datetime createdAt
        datetime updatedAt
    }

    %% 关系定义
    users ||--o{ user_teams : "belongs to"
    teams ||--o{ user_teams : "has"
    users ||--o{ teams : "creates"
    users ||--o{ apps : "creates"
    users ||--o{ apps : "owns"
    teams ||--o{ apps : "contains"
    apps ||--o{ versions : "has"
    apps ||--o{ builds : "has"
    apps ||--o{ downloads : "tracks"
    apps ||--o{ app_daily_stats : "statistics"
    apps ||--o{ gray_strategies : "configures"
    versions ||--o{ downloads : "downloaded"
    users ||--o{ versions : "uploads"
    users ||--o{ miniapps : "creates"
    users ||--o{ miniapps : "owns"
    miniapps ||--o{ miniapp_download_codes : "has"
    users ||--o{ messages : "sends"
    users ||--o{ messages : "receives"
    teams ||--o{ invites : "invites to"
    invites ||--o{ invite_emails : "contains"
    users ||--o{ downloads : "downloads"
```

## 核心表结构

### 1. 用户管理 (users)

| 字段              | 类型         | 约束                   | 说明                |
| ----------------- | ------------ | ---------------------- | ------------------- |
| id                | int          | PK, AUTO_INCREMENT     | 主键                |
| username          | varchar(50)  | UNIQUE, NOT NULL       | 用户名              |
| userAvatar        | varchar(500) | NULL                   | 用户头像            |
| userAvatarHistory | text         | NULL                   | 头像历史记录 (JSON) |
| password          | varchar(255) | NOT NULL, SELECT FALSE | 密码 (加密)         |
| email             | varchar(100) | UNIQUE, NOT NULL       | 邮箱                |
| token             | varchar(500) | NULL                   | 认证令牌            |
| apiToken          | varchar(500) | NULL                   | API 令牌            |
| mobile            | varchar(20)  | NULL                   | 手机号              |
| qq                | varchar(20)  | NULL                   | QQ 号               |
| company           | varchar(100) | NULL                   | 公司                |
| career            | varchar(100) | NULL                   | 职业                |

### 2. 团队管理 (teams)

| 字段      | 类型         | 约束               | 说明         |
| --------- | ------------ | ------------------ | ------------ |
| id        | int          | PK, AUTO_INCREMENT | 主键         |
| name      | varchar(100) | NOT NULL           | 团队名称     |
| isDefault | boolean      | DEFAULT false      | 是否默认团队 |
| icon      | varchar(500) | NULL               | 团队图标     |
| creatorId | int          | FK(users.id)       | 创建者ID     |

### 3. 用户团队关系 (user_teams)

| 字段   | 类型                            | 约束               | 说明   |
| ------ | ------------------------------- | ------------------ | ------ |
| id     | int                             | PK, AUTO_INCREMENT | 主键   |
| userId | int                             | FK(users.id)       | 用户ID |
| teamId | int                             | FK(teams.id)       | 团队ID |
| role   | enum('owner','manager','guest') | DEFAULT 'guest'    | 角色   |

### 4. 应用管理 (apps)

| 字段               | 类型                            | 约束               | 说明               |
| ------------------ | ------------------------------- | ------------------ | ------------------ |
| id                 | int                             | PK, AUTO_INCREMENT | 主键               |
| platform           | varchar(20)                     | NOT NULL           | 平台 (iOS/Android) |
| bundleId           | varchar(255)                    | UNIQUE, NOT NULL   | 包标识符           |
| bundleName         | varchar(255)                    | NULL               | 包名称             |
| appName            | varchar(255)                    | NOT NULL           | 应用名称           |
| currentVersion     | varchar(50)                     | NULL               | 当前版本           |
| creatorId          | int                             | FK(users.id)       | 创建者ID           |
| creator            | varchar(100)                    | NULL               | 创建者名称         |
| ownerId            | int                             | FK(users.id)       | 拥有者ID           |
| autoPublish        | boolean                         | DEFAULT false      | 自动发布           |
| installWithPwd     | boolean                         | DEFAULT false      | 安装需要密码       |
| installPwd         | varchar(50)                     | NULL               | 安装密码           |
| updateMode         | enum('normal','silent','force') | DEFAULT 'normal'   | 更新模式           |
| totalDownloadCount | bigint                          | DEFAULT 0          | 总下载次数         |

### 5. 版本管理 (versions)

| 字段          | 类型        | 约束               | 说明       |
| ------------- | ----------- | ------------------ | ---------- |
| id            | int         | PK, AUTO_INCREMENT | 主键       |
| appId         | int         | FK(apps.id)        | 应用ID     |
| versionStr    | varchar(50) | NULL               | 版本字符串 |
| versionCode   | varchar(50) | NULL               | 版本代码   |
| uploaderId    | int         | FK(users.id)       | 上传者ID   |
| size          | bigint      | NULL               | 文件大小   |
| active        | boolean     | DEFAULT true       | 是否激活   |
| downloadCount | bigint      | DEFAULT 0          | 下载次数   |
| released      | boolean     | DEFAULT false      | 是否已发布 |

## 关系说明

### 多对多关系

- **用户 ↔ 团队**: 通过 `user_teams` 中间表实现，支持角色控制
- **应用 ↔ 版本**: 一对多关系，一个应用有多个版本

### 一对多关系

- **团队 → 应用**: 团队可以拥有多个应用
- **用户 → 应用**: 用户可以创建/拥有多个应用
- **应用 → 构建**: 应用可以有多次构建记录
- **小程序 → 下载码**: 小程序可以有多个下载码

### 关键外键约束

```sql
-- 用户团队关系
ALTER TABLE user_teams ADD CONSTRAINT fk_user_teams_user
FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE user_teams ADD CONSTRAINT fk_user_teams_team
FOREIGN KEY (teamId) REFERENCES teams(id) ON DELETE CASCADE;

-- 应用关系
ALTER TABLE apps ADD CONSTRAINT fk_apps_creator
FOREIGN KEY (creatorId) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE apps ADD CONSTRAINT fk_apps_owner
FOREIGN KEY (ownerId) REFERENCES users(id) ON DELETE SET NULL;

-- 版本关系
ALTER TABLE versions ADD CONSTRAINT fk_versions_app
FOREIGN KEY (appId) REFERENCES apps(id) ON DELETE CASCADE;

ALTER TABLE versions ADD CONSTRAINT fk_versions_uploader
FOREIGN KEY (uploaderId) REFERENCES users(id) ON DELETE SET NULL;
```

## 索引设计

### 主要索引

```sql
-- 用户表
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);

-- 应用表
CREATE INDEX idx_apps_bundleId ON apps(bundleId);
CREATE INDEX idx_apps_creator ON apps(creatorId);
CREATE INDEX idx_apps_owner ON apps(ownerId);

-- 版本表
CREATE INDEX idx_versions_app ON versions(appId);
CREATE INDEX idx_versions_uploader ON versions(uploaderId);
CREATE INDEX idx_versions_version ON versions(versionStr, versionCode);

-- 下载记录
CREATE INDEX idx_downloads_app ON downloads(appId);
CREATE INDEX idx_downloads_user ON downloads(userId);
CREATE INDEX idx_downloads_date ON downloads(createdAt);
```

## 数据迁移注意事项

### MongoDB 到 MySQL 迁移

1. **ObjectId → Auto Increment ID**: MongoDB 的 ObjectId 转为 MySQL 的自增主键
2. **嵌入文档 → 关系表**: 如 UserTeam 数组转为 user_teams 关系表
3. **数组字段 → JSON 或关系表**: 根据查询需求选择存储方式
4. **日期格式**: 统一使用 MySQL DATETIME 格式

### 性能优化

1. **合理使用索引**: 基于查询模式创建复合索引
2. **分区策略**: 大表如下载记录可按时间分区
3. **缓存策略**: 频繁查询的数据使用 Redis 缓存
4. **读写分离**: 考虑主从复制架构

---

> 📝 **维护说明**: 此文档应与实体类定义保持同步，任何表结构变更都需要更新此文档并生成相应的迁移文件。
