version: '3.8'

networks:
  cfe-network:
    driver: bridge

services:
  # ===========================================
  # CFE Node Server Application
  # ===========================================
  cfe-node-server:
    build:
      context: ../../../
      dockerfile: packages/server/docker/Dockerfile
    image: cfe-node-server:latest
    ports:
      - "${EXTERNAL_SERVER_PORT}:${PORT}"

    environment:
      - IN_DOCKER=true
      - ROOT=${ROOT}
      - NODE_ENV=${NODE_ENV}
      - PORT=${PORT}
      - HOST=${HOST}
      - API_PREFIX=${API_PREFIX}
      - CLIENT_PORT=${CLIENT_PORT}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DB_SYNC=${DB_SYNC}
      - DB_LOGGING=${DB_LOGGING}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - REDIS_ENABLED=${REDIS_ENABLED}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=${REDIS_DB}
      - RATE_LIMIT_WINDOW=${RATE_LIMIT_WINDOW}
      - RATE_LIMIT_MAX=${RATE_LIMIT_MAX}
      - TZ=${TZ}
    volumes:
      - ${ROOT}/static/uploads:/app/static/uploads
      - ${ROOT}/static/logs:/app/static/logs
    networks:
      - cfe-network
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:${PORT}/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # ===========================================
  # MySQL Database
  # ===========================================
  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - TZ=${TZ}
    ports:
      - "${EXTERNAL_DB_PORT}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - mysql_logs:/var/log/mysql
      # - ${ROOT}/db:/var/lib/mysql
      # - ${ROOT}/db/logs:/var/log/mysql
    networks:
      - cfe-network
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=128M
      --max-connections=200
      --sql-mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # ===========================================
  # Redis Cache (Optional)
  # ===========================================
  redis:
    image: redis:7-alpine
    ports:
      - "${EXTERNAL_REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - cfe-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

# ===========================================
# Persistent Data Volumes
# ===========================================
volumes:
  mysql_data:
    driver: local
  mysql_logs:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
