# ===========================================
# CFE Node Server - 测试环境配置
# ===========================================

ROOT=../../../

# ============ 应用配置 ============
NODE_ENV=staging
PORT=3000
HOST=0.0.0.0
API_PREFIX=/api
TZ=Asia/Shanghai

CLIENT_PORT=8001

# ============ 数据库配置 ============
DB_HOST=db
DB_PORT=3306
DB_USER=root
DB_PASSWORD=staging-password
DB_NAME=cfe_node_server_staging
DB_SYNC=false
DB_LOGGING=true

# ============ JWT配置 ============
JWT_SECRET=staging-jwt-secret-please-change-this
JWT_EXPIRES_IN=7d

# ============ Redis配置 ============
REDIS_ENABLED=true
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# ============ 限流配置 ============
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=200

# ============ Docker端口映射 ============
EXTERNAL_SERVER_PORT=3001
EXTERNAL_DB_PORT=3307
EXTERNAL_REDIS_PORT=6380
