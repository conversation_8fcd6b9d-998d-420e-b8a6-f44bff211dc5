# CFE Node Server Docker 部署指南 🚀

## 📋 快速开始

### 1. 环境准备
- Docker (>= 20.0)
- Docker Compose (>= 2.0)

### 2. 启动服务

```bash
cd packages/server/docker

# 开发环境
./env-manager.sh start development

# 测试环境  
./env-manager.sh start staging

# 生产环境
./env-manager.sh start production
```

## 🌍 环境配置

| 环境 | 应用端口 | 数据库端口 | Redis端口 | 数据库名 |
|------|----------|------------|-----------|----------|
| 开发环境 | 3000 | 3306 | 6379 | cfe_node_server_dev |
| 测试环境 | 3001 | 3307 | 6380 | cfe_node_server_staging |
| 生产环境 | 8080 | 3308 | 6381 | cfe_node_server_prod |

## 🔧 环境管理

### 基本命令

```bash
# 列出所有环境
./env-manager.sh list

# 启动环境
./env-manager.sh start <environment>

# 停止环境
./env-manager.sh stop <environment>

# 查看状态
./env-manager.sh status <environment>

# 查看日志
./env-manager.sh logs <environment>
./env-manager.sh logs <environment> db    # 查看数据库日志

# 重启环境
./env-manager.sh restart <environment>

# 清理环境（删除所有数据）
./env-manager.sh clean <environment>
```

### 从项目根目录快捷命令

```bash
# 开发环境
pnpm docker                    # 启动
pnpm docker:stop              # 停止  
pnpm docker:logs              # 查看日志

# 测试环境
pnpm docker:staging           # 启动
pnpm docker:stop:staging      # 停止
pnpm docker:logs:staging      # 查看日志

# 生产环境
pnpm docker:prod              # 启动
pnpm docker:stop:prod         # 停止
pnpm docker:logs:prod         # 查看日志
```

## ⚙️ 配置修改

如需修改环境配置，编辑对应的环境文件：

```bash
vim env.development    # 开发环境配置
vim env.staging       # 测试环境配置  
vim env.production    # 生产环境配置
```

### 关键配置项

```env
# 应用配置
NODE_ENV=development
EXTERNAL_SERVER_PORT=3000

# 数据库配置
DB_NAME=cfe_node_server_dev
DB_PASSWORD=your-password

# JWT配置
JWT_SECRET=your-jwt-secret

# Redis配置
REDIS_ENABLED=false
```

## 🗄️ 数据库管理

### 数据库重置工具

```bash
# 重置数据库（删除所有数据）
./database-reset.sh reset <environment>

# 检查数据库状态
./database-reset.sh status <environment>

# 创建数据库（如果不存在）
./database-reset.sh create <environment>
```

### 数据库连接

```bash
# 使用 MySQL 客户端连接
mysql -h 127.0.0.1 -P 3306 -u root -p    # 开发环境
mysql -h 127.0.0.1 -P 3307 -u root -p    # 测试环境
mysql -h 127.0.0.1 -P 3308 -u root -p    # 生产环境
```

## 🐳 服务架构

### 容器组成
- **cfe-node-server**: 主应用服务
- **db**: MySQL 8.0 数据库
- **redis**: Redis 缓存（可选）

### 数据持久化
- `mysql_data`: MySQL 数据文件
- `redis_data`: Redis 数据文件  
- `app_logs`: 应用日志
- `app_uploads`: 上传文件

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库状态
   ./env-manager.sh status development
   ./env-manager.sh logs development db
   ```

2. **端口冲突**
   ```bash
   # 修改对应环境文件中的端口配置
   vim env.development
   ```

3. **权限问题**
   ```bash
   chmod +x env-manager.sh
   chmod +x database-reset.sh
   ```

4. **容器启动失败**
   ```bash
   # 查看详细日志
   ./env-manager.sh logs <environment>
   
   # 清理并重新启动
   ./env-manager.sh clean <environment>
   ./env-manager.sh start <environment>
   ```

### 完全重置

如果遇到严重问题，可以完全重置环境：

```bash
# 停止所有容器
docker stop $(docker ps -q)

# 删除所有 CFE 相关容器和卷
docker rm $(docker ps -aq --filter name=cfe)
docker volume rm $(docker volume ls -q --filter name=cfe)

# 重新启动
./env-manager.sh start development
```

## 🔐 生产环境注意事项

⚠️ **生产环境必须修改以下配置**：

```env
# 修改 env.production 文件
JWT_SECRET=your-super-secure-production-jwt-secret
DB_PASSWORD=your-secure-production-password
REDIS_PASSWORD=your-redis-password
```

## 📚 相关文档

- [环境配置详细指南](../README-Environment-Config.md)
- [项目完整文档](../README.md)

---

## 🎯 快速参考

```bash
# 最常用命令
./env-manager.sh start development     # 启动开发环境
./env-manager.sh logs development      # 查看开发环境日志
./env-manager.sh stop development      # 停止开发环境
./database-reset.sh reset development  # 重置开发环境数据库
```

**访问地址**：
- 开发环境：http://localhost:3000
- 测试环境：http://localhost:3001  
- 生产环境：http://localhost:8080 
