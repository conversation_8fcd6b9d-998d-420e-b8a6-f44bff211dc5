#!/bin/bash

# ===========================================
# 数据库重置脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 重置指定环境的数据库
reset_database() {
    local env=$1
    local project_name="cfe-$env"
    local env_file="env.$env"
    
    if [ ! -f "$env_file" ]; then
        log_error "Environment file $env_file not found!"
        exit 1
    fi
    
    local db_name=$(grep DB_NAME $env_file | cut -d'=' -f2)
    log_info "Resetting database for $env environment: $db_name"
    
    log_warning "This will remove all data in the database!"
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cancelled."
        exit 0
    fi
    
    # 停止环境
    log_info "Stopping $env environment..."
    docker-compose -p $project_name down
    
    # 删除数据库卷
    log_info "Removing database volume..."
    docker volume rm "${project_name}_mysql_data" 2>/dev/null || true
    docker volume rm "${project_name}_mysql_logs" 2>/dev/null || true
    docker volume rm "${project_name}_redis_data" 2>/dev/null || true
    docker volume rm "${project_name}_app_logs" 2>/dev/null || true
    docker volume rm "${project_name}_app_uploads" 2>/dev/null || true
    
    # 清理未使用的卷
    docker volume prune -f
    
    # 重新启动环境
    log_info "Starting $env environment with fresh database..."
    docker-compose --env-file $env_file -p $project_name up --build -d
    
    log_success "Database reset complete for $env environment!"
    log_info "Database $db_name will be automatically created."
}

# 显示数据库状态
show_database_status() {
    local env=$1
    local env_file="env.$env"
    
    if [ ! -f "$env_file" ]; then
        log_error "Environment file $env_file not found!"
        exit 1
    fi
    
    local db_name=$(grep DB_NAME $env_file | cut -d'=' -f2)
    local db_port=$(grep EXTERNAL_DB_PORT $env_file | cut -d'=' -f2)
    local db_password=$(grep DB_PASSWORD $env_file | cut -d'=' -f2)
    
    log_info "Database status for $env environment:"
    echo "  - Database name: $db_name"
    echo "  - Port: $db_port"
    echo "  - Connection test:"
    
    # 测试数据库连接
    if command -v mysql &> /dev/null; then
        mysql -h 127.0.0.1 -P $db_port -u root -p$db_password -e "SHOW DATABASES LIKE '$db_name';" 2>/dev/null && \
        log_success "Database $db_name exists and is accessible" || \
        log_error "Cannot connect to database $db_name"
    else
        log_warning "MySQL client not installed. Install with: brew install mysql"
    fi
}

# 创建数据库（如果不存在）
create_database() {
    local env=$1
    local env_file="env.$env"
    
    if [ ! -f "$env_file" ]; then
        log_error "Environment file $env_file not found!"
        exit 1
    fi
    
    local db_name=$(grep DB_NAME $env_file | cut -d'=' -f2)
    local db_port=$(grep EXTERNAL_DB_PORT $env_file | cut -d'=' -f2)
    local db_password=$(grep DB_PASSWORD $env_file | cut -d'=' -f2)
    
    log_info "Creating database $db_name for $env environment..."
    
    if command -v mysql &> /dev/null; then
        mysql -h 127.0.0.1 -P $db_port -u root -p$db_password -e "CREATE DATABASE IF NOT EXISTS \`$db_name\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" && \
        log_success "Database $db_name created successfully" || \
        log_error "Failed to create database $db_name"
    else
        log_error "MySQL client not installed. Install with: brew install mysql"
        log_info "Or the database should be created automatically by Docker"
    fi
}

# 主函数
main() {
    case ${1:-help} in
        reset)
            if [ -z "$2" ]; then
                log_error "Environment required: development|staging|production"
                exit 1
            fi
            reset_database $2
            ;;
        status)
            if [ -z "$2" ]; then
                log_error "Environment required: development|staging|production"
                exit 1
            fi
            show_database_status $2
            ;;
        create)
            if [ -z "$2" ]; then
                log_error "Environment required: development|staging|production"
                exit 1
            fi
            create_database $2
            ;;
        help|--help|-h)
            echo "Database Reset Tool for CFE Node Server"
            echo ""
            echo "Usage: $0 <command> <environment>"
            echo ""
            echo "Commands:"
            echo "  reset <env>    Reset database (removes all data)"
            echo "  status <env>   Show database status"
            echo "  create <env>   Create database if not exists"
            echo "  help          Show this help"
            echo ""
            echo "Environments: development, staging, production"
            echo ""
            echo "Examples:"
            echo "  $0 reset development"
            echo "  $0 status staging"
            echo "  $0 create production"
            ;;
        *)
            log_error "Unknown command: $1"
            main help
            exit 1
            ;;
    esac
}

main "$@" 
