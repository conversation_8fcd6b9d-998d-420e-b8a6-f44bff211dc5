#!/bin/bash

# ===========================================
# CFE Node Server 环境管理脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查环境文件
check_env_file() {
    local env=$1
    local env_file="env.$env"
    
    if [ ! -f "$env_file" ]; then
        log_error "Environment file $env_file not found!"
        exit 1
    fi
    
    log_info "Using environment file: $env_file"
}

# 显示环境信息
show_env_info() {
    local env=$1
    local env_file="env.$env"
    
    log_info "Environment: $env"
    echo "📋 Configuration:"
    echo "   - NODE_ENV: $(grep NODE_ENV $env_file | cut -d'=' -f2)"
    echo "   - DB_NAME: $(grep DB_NAME $env_file | cut -d'=' -f2)"
    echo "   - App Port: $(grep EXTERNAL_SERVER_PORT $env_file | cut -d'=' -f2)"
    echo "   - DB Port: $(grep EXTERNAL_DB_PORT $env_file | cut -d'=' -f2)"
    echo "   - Redis Enabled: $(grep REDIS_ENABLED $env_file | cut -d'=' -f2)"
    echo ""
}

# 启动环境
start_env() {
    local env=$1
    local project_name="cfe-$env"
    
    check_env_file $env
    show_env_info $env
    
    log_info "Starting $env environment..."
    
    if [ "$env" = "production" ]; then
        log_warning "Starting PRODUCTION environment!"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Cancelled."
            exit 0
        fi
    fi
    
    docker-compose --env-file env.$env -p $project_name up --build -d
    
    log_success "$env environment started successfully!"
    log_info "Services:"
    echo "   - App: http://localhost:$(grep EXTERNAL_SERVER_PORT env.$env | cut -d'=' -f2)"
    echo "   - Database: localhost:$(grep EXTERNAL_DB_PORT env.$env | cut -d'=' -f2)"
    if [ "$(grep REDIS_ENABLED env.$env | cut -d'=' -f2)" = "true" ]; then
        echo "   - Redis: localhost:$(grep EXTERNAL_REDIS_PORT env.$env | cut -d'=' -f2)"
    fi
}

# 停止环境
stop_env() {
    local env=$1
    local project_name="cfe-$env"
    
    log_info "Stopping $env environment..."
    docker-compose -p $project_name down
    log_success "$env environment stopped!"
}

# 查看环境状态
status_env() {
    local env=$1
    local project_name="cfe-$env"
    
    log_info "Status for $env environment:"
    docker-compose -p $project_name ps
}

# 查看环境日志
logs_env() {
    local env=$1
    local project_name="cfe-$env"
    local service=${2:-cfe-node-server}
    
    log_info "Showing logs for $env environment (service: $service):"
    docker-compose -p $project_name logs -f $service
}

# 重启环境
restart_env() {
    local env=$1
    local project_name="cfe-$env"
    
    check_env_file $env
    
    log_info "Restarting $env environment..."
    docker-compose --env-file env.$env -p $project_name down
    docker-compose --env-file env.$env -p $project_name up --build -d
    
    log_success "$env environment restarted successfully!"
}

# 清理环境
clean_env() {
    local env=$1
    local project_name="cfe-$env"
    
    log_warning "Cleaning $env environment (removing containers and volumes)..."
    read -p "Are you sure? This will remove all data! (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cancelled."
        exit 0
    fi
    
    docker-compose -p $project_name down -v --remove-orphans
    log_success "$env environment cleaned!"
}

# 显示帮助信息
show_help() {
    echo "CFE Node Server Environment Manager"
    echo ""
    echo "Usage: $0 <command> <environment> [options]"
    echo ""
    echo "Commands:"
    echo "  start <env>     Start environment (development|staging|production)"
    echo "  stop <env>      Stop environment"
    echo "  restart <env>   Restart environment"
    echo "  status <env>    Show environment status"
    echo "  logs <env> [service]  Show logs (default service: cfe-node-server)"
    echo "  clean <env>     Clean environment (remove containers and volumes)"
    echo "  list            List all environments"
    echo "  help            Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 start development"
    echo "  $0 stop staging"
    echo "  $0 logs production mysql"
    echo "  $0 clean development"
}

# 列出所有环境
list_envs() {
    log_info "Available environments:"
    echo ""
    
    for env_file in env.*; do
        if [ -f "$env_file" ]; then
            env=${env_file#env.}
            echo "🌍 $env"
            echo "   - NODE_ENV: $(grep NODE_ENV $env_file | cut -d'=' -f2)"
            echo "   - App Port: $(grep EXTERNAL_SERVER_PORT $env_file | cut -d'=' -f2)"
            echo ""
        fi
    done
}

# 主函数
main() {
    if [ $# -lt 1 ]; then
        show_help
        exit 1
    fi
    
    case $1 in
        start)
            if [ -z "$2" ]; then
                log_error "Environment required for start command"
                exit 1
            fi
            start_env $2
            ;;
        stop)
            if [ -z "$2" ]; then
                log_error "Environment required for stop command"
                exit 1
            fi
            stop_env $2
            ;;
        restart)
            if [ -z "$2" ]; then
                log_error "Environment required for restart command"
                exit 1
            fi
            restart_env $2
            ;;
        status)
            if [ -z "$2" ]; then
                log_error "Environment required for status command"
                exit 1
            fi
            status_env $2
            ;;
        logs)
            if [ -z "$2" ]; then
                log_error "Environment required for logs command"
                exit 1
            fi
            logs_env $2 $3
            ;;
        clean)
            if [ -z "$2" ]; then
                log_error "Environment required for clean command"
                exit 1
            fi
            clean_env $2
            ;;
        list)
            list_envs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 
