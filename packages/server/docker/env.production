# ===========================================
# CFE Node Server - 生产环境配置
# ===========================================

ROOT=../../../

# ============ 应用配置 ============
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
API_PREFIX=/api
TZ=Asia/Shanghai

CLIENT_PORT=8888

# ============ 数据库配置 ============
DB_HOST=db
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-secure-production-password
DB_NAME=cfe_node_server_prod
DB_SYNC=false
DB_LOGGING=false

# ============ JWT配置 ============
JWT_SECRET=your-super-secure-production-jwt-secret
JWT_EXPIRES_IN=7d

# ============ Redis配置 ============
REDIS_ENABLED=true
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# ============ 限流配置 ============
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# ============ Docker端口映射 ============
EXTERNAL_SERVER_PORT=8080
EXTERNAL_DB_PORT=3308
EXTERNAL_REDIS_PORT=6381
