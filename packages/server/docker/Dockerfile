FROM node:18-alpine

# 添加 tzdata 包以支持时区设置
RUN apk add --no-cache tzdata
ENV TZ=Asia/Shanghai

WORKDIR /app

# 复制workspace配置
COPY pnpm-workspace.yaml package.json pnpm-lock.yaml ./
COPY packages/server/package.json ./packages/server/
COPY packages/koa-swagger-decorator ./packages/koa-swagger-decorator
COPY packages/utils ./packages/utils

# 安装 pnpm 和依赖
RUN npm install -g pnpm
WORKDIR /app/packages/server
RUN pnpm install --no-frozen-lockfile --prod

# 复制构建好的 dist 目录
COPY packages/server/dist ./dist

# 复制 public 目录（如果存在）
COPY packages/server/public ./public

# 创建日志和上传目录
RUN mkdir -p /app/static/logs /app/static/uploads /app/static/db

# 使用非 root 用户运行应用
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodeapp -u 1001

# 设置目录权限
RUN chown -R nodeapp:nodejs /app/packages/server /app/static
RUN chmod -R 755 /app/static

USER nodeapp

EXPOSE 3000

# 启动应用
CMD ["node", "dist/app.cjs"]
