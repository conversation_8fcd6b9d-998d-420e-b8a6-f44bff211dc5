-- ===========================================
-- CFE Node Server MySQL Initialization
-- ===========================================

-- 注意：MYSQL_DATABASE 环境变量会自动创建主数据库
-- 此脚本运行在该数据库的上下文中

-- 确保字符集正确设置
ALTER DATABASE CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建应用用户（可选，用于更安全的连接）
-- CREATE USER IF NOT EXISTS 'cfe_app'@'%' IDENTIFIED BY 'cfe_app_password';
-- GRANT ALL PRIVILEGES ON `test`.* TO 'cfe_app'@'%';

-- 创建日志表（用于监控）
CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `level` varchar(20) NOT NULL,
  `message` text NOT NULL,
  `meta` json DEFAULT NULL,
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_logs_level` (`level`),
  KEY `idx_logs_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入初始化日志
INSERT INTO `system_logs` (`level`, `message`, `meta`) VALUES
('info', 'Database initialized successfully', JSON_OBJECT('action', 'init', 'timestamp', NOW()));

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示初始化完成信息
SELECT 'CFE Node Server Database Initialized Successfully' as status; 
