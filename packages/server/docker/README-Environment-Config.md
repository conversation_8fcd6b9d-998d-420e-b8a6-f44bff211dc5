# CFE Node Server 环境配置方案 🌍

## 概述

本项目使用 **环境配置文件** 替代多个 docker-compose 文件的方案，实现了更简洁、更标准化的多环境管理。

## 📁 文件结构

```
packages/server/docker/
├── docker-compose.yml          # 主要的 Docker Compose 配置
├── env.development            # 开发环境变量配置
├── env.staging               # 测试环境变量配置
├── env.production            # 生产环境变量配置
├── env-manager.sh            # 环境管理脚本
├── Dockerfile               # 应用构建镜像
├── init.sql                # 数据库初始化脚本
└── redis.conf              # Redis 配置文件
```

## 🎯 设计优势

### ✅ 替代前的问题
- ❌ 需要维护多个 docker-compose 文件
- ❌ 配置重复，维护困难
- ❌ 端口冲突，无法同时运行多环境

### ✅ 新方案优势
- ✅ **单一配置文件**：只需要一个 `docker-compose.yml`
- ✅ **环境隔离**：每个环境独立的端口和配置
- ✅ **标准化**：遵循 12-factor app 原则
- ✅ **易于维护**：配置集中在环境文件中
- ✅ **避免冲突**：不同环境使用不同端口

## 🌍 环境配置

### 开发环境 (development)
```bash
# 端口配置
App:      http://localhost:3000
Database: localhost:3306
Redis:    localhost:6379 (默认禁用)

# 特性
- 数据库自动同步表结构
- 详细的 SQL 日志
- 宽松的限流设置
```

### 测试环境 (staging)
```bash
# 端口配置
App:      http://localhost:3001
Database: localhost:3307
Redis:    localhost:6380

# 特性
- 禁用数据库自动同步
- 启用 Redis 缓存
- 中等限流设置
- 模拟生产环境配置
```

### 生产环境 (production)
```bash
# 端口配置
App:      http://localhost:8080
Database: localhost:3308
Redis:    localhost:6381

# 特性
- 严格的安全检查
- 最小化日志输出
- 严格的限流设置
- 强制使用安全密钥
```

## 🚀 使用方法

### 1. 快速启动

```bash
# 从项目根目录
pnpm dev                    # 开发环境
pnpm dev:staging           # 测试环境

# Docker 部署
pnpm docker                 # 开发环境 Docker
pnpm docker:staging        # 测试环境 Docker
pnpm docker:prod           # 生产环境 Docker
```

### 2. 环境管理脚本

```bash
cd packages/server/docker

# 列出所有可用环境
./env-manager.sh list

# 启动指定环境
./env-manager.sh start development
./env-manager.sh start staging
./env-manager.sh start production

# 管理环境
./env-manager.sh stop staging
./env-manager.sh restart development
./env-manager.sh status production

# 查看日志
./env-manager.sh logs development
./env-manager.sh logs staging db
./env-manager.sh logs production redis

# 清理环境
./env-manager.sh clean development
```

### 3. 停止和日志管理

```bash
# 停止环境
pnpm docker:stop           # 停止开发环境
pnpm docker:stop:staging   # 停止测试环境
pnpm docker:stop:prod      # 停止生产环境

# 查看日志
pnpm docker:logs           # 开发环境日志
pnpm docker:logs:staging   # 测试环境日志
pnpm docker:logs:prod      # 生产环境日志
```

## ⚙️ 环境变量配置

### 关键配置项

| 配置项 | 开发环境 | 测试环境 | 生产环境 | 说明 |
|--------|----------|----------|----------|------|
| `NODE_ENV` | development | staging | production | 环境标识 |
| `EXTERNAL_SERVER_PORT` | 3000 | 3001 | 8080 | 应用外部端口 |
| `EXTERNAL_DB_PORT` | 3306 | 3307 | 3308 | 数据库外部端口 |
| `EXTERNAL_REDIS_PORT` | 6379 | 6380 | 6381 | Redis 外部端口 |
| `DB_SYNC` | true | false | false | 数据库同步 |
| `REDIS_ENABLED` | false | true | true | Redis 启用状态 |
| `RATE_LIMIT_MAX` | 1000 | 200 | 100 | 限流阈值 |

### 自定义配置

如需修改环境配置，直接编辑对应的环境文件：

```bash
# 编辑开发环境
vim env.development

# 编辑测试环境
vim env.staging

# 编辑生产环境
vim env.production
```

## 🔐 安全注意事项

### 开发环境
- ✅ 使用默认配置即可
- ✅ 自动创建数据库表结构

### 测试环境
- ⚠️ 建议修改默认 JWT 密钥
- ⚠️ 数据库同步已禁用，需要运行迁移

### 生产环境
- ❌ **必须** 修改 JWT_SECRET
- ❌ **必须** 修改数据库密码
- ❌ **必须** 使用数据库迁移
- ❌ **禁止** 使用数据库自动同步

## 🛠️ 故障排除

### 端口冲突
如果遇到端口冲突，修改对应环境文件中的 `EXTERNAL_*_PORT` 配置。

### 权限问题
```bash
# 确保脚本有执行权限
chmod +x env-manager.sh
```

### 环境启动失败
```bash
# 查看详细日志
./env-manager.sh logs <environment>

# 清理并重新启动
./env-manager.sh clean <environment>
./env-manager.sh start <environment>
```

### 数据库连接问题
```bash
# 检查数据库服务状态
./env-manager.sh status <environment>

# 查看数据库日志
./env-manager.sh logs <environment> db
```

## 📚 相关文档

- [完整环境配置指南](../ENVIRONMENT_CONFIG.md)
- [Docker 部署文档](./README.md)
- [项目架构文档](../design.md)

## 🔄 迁移说明

### 从旧的 docker-compose.staging.yml 迁移

旧方案：
```bash
docker-compose -f docker-compose.yml -f docker-compose.staging.yml up
```

新方案：
```bash
docker-compose --env-file env.staging -p cfe-staging up
# 或者使用环境管理脚本
./env-manager.sh start staging
```

这种新方案提供了更好的：
- 配置管理
- 环境隔离
- 端口管理
- 操作便利性 
