# ===========================================
# CFE Node Server - 开发环境配置
# ===========================================

ROOT=../../../

# ============ 应用配置 ============
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
API_PREFIX=/api
TZ=Asia/Shanghai

CLIENT_PORT=8000

# ============ 数据库配置 ============
DB_HOST=db
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=cfe_node_server_dev
DB_SYNC=true
DB_LOGGING=true

# ============ JWT配置 ============
JWT_SECRET=dev-secret-key-change-in-production
JWT_EXPIRES_IN=7d

# ============ Redis配置 ============
REDIS_ENABLED=false
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ============ 限流配置 ============
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000

# ============ Docker端口映射 ============
EXTERNAL_SERVER_PORT=3000
EXTERNAL_DB_PORT=3306
EXTERNAL_REDIS_PORT=6379
