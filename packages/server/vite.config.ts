import type { PluginOption, UserConfig } from 'vite'
import path, { resolve } from 'node:path'
import process from 'node:process'
import { defineConfig, mergeConfig } from 'vite'
import { VitePluginNode } from 'vite-plugin-node'
import { createBaseConfig } from '../../configs/vite/base'

const baseDir = process.cwd()
const projectRoot = resolve(baseDir, '../..')

export default defineConfig((config: ConfigEnv) => {
  const baseConfig = createBaseConfig({ projectRoot })
  // const baseAlias = createAllAliases(projectRoot)
  const devConfig: UserConfig = {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
    },
    server: {
      port: 2999,
      hmr: true,
    },
    plugins: [
      ...VitePluginNode({
        adapter: 'koa',
        appPath: './src/app.ts',
        exportName: 'viteNodeApp',
        tsCompiler: 'esbuild',
      }),
    ] as PluginOption[],
    esbuild: {
      target: 'node18',
      tsconfigRaw: {
        compilerOptions: {
          experimentalDecorators: true,
          emitDecoratorMetadata: true,
        },
      },
    },
    optimizeDeps: {
      exclude: ['@cfe-node/utils', '@cfe-node/koa-swagger-decorator'],
    },
    build: {
      outDir: 'dist',
      target: 'node18',
      rollupOptions: {
        external: [
          '@cfe-node/koa-swagger-decorator',
          '@cfe-node/utils',
          '@asteasolutions/zod-to-openapi',
          'redis',
          'reflect-metadata',
          'typeorm',
          'mysql2',
          'winston',
          'koa',
          'koa-router',
          'koa-body',
          'koa-static',
          'koa-bodyparser',
          'koa-helmet',
          'koa-logger',
          '@koa/cors',
          'ioredis',
          'jsonwebtoken',
          'crypto-js',
          'dayjs',
          'lodash-es',
          'yamljs',
        ],
      },
    },
  }
  const mergedConfig = mergeConfig(baseConfig, devConfig)
  // console.dir(mergedConfig, { depth: null })
  return mergedConfig
})
