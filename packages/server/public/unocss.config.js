/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-03-05 10:11:59
 * @LastEditTime: 2025-03-07 14:53:05
 * @LastEditors: shaojun
 * @Description:
 */
window.__unocss = {
  theme: {
    colors: {
      primary: 'var(--cfe-c-primary)',
      card: 'var(--cfe-c-card)', // 卡片背景（底层）
      card2: 'var(--cfe-c-card2)', // 卡片背景2（底层-上层）
      card3: 'var(--cfe-c-card3)', // 卡片背景3（底层-上层-上层）
      border: 'var(--cfe-c-border)', // 边框颜色
      system: {
        primary: 'var(--system-primary)', // bg-system-primary
        success: 'var(--system-success)', // bg-system-success
        warning: 'var(--system-warning)', // bg-system-warning
        danger: 'var(--system-danger)', // bg-system-danger
        error: 'var(--system-error)', // bg-system-error
        info: 'var(--system-info)', // bg-system-info
      },
      cfe: {
        // text base color
        text: 'var(--cfe-c-text)',
        // bg base color
        bg: 'var(--cfe-c-bg)',
        // border base color
        border: 'var(--cfe-c-border)',
      },
      text: {
        // 一级文案颜色
        one: 'var(--cfe-text-one)',
        // 二级文案颜色
        two: 'var(--cfe-text-two)',
        // 三级文案颜色
        three: 'var(--cfe-text-three)',
        primary: 'var(--cfe-c-text-primary)',
        regular: 'var(--cfe-c-text-regular)',
        secondary: 'var(--cfe-c-text-secondary)',
        placeholder: 'var(--cfe-c-text-placeholder)',
        disabled: 'var(--cfe-c-text-disabled)',
      },
    },
    breakpoints: {
      xs: '320px',
      sm: '640px',
    },
  },
  shortcuts: [
    // base theme
    ['cfe-theme-base', 'color-cfe-text bg-cfe-bg border-cfe-border'], //
    // card
    ['base-card', 'cfe-theme-base bg-card p-x-10px p-y-15px border-rounded-6px'], // 卡片背景
    ['base-card2', 'base-card bg-card2'], // 卡片背景
    ['base-card3', 'base-card bg-card3'], // 卡片背景
    ['base-card-white', 'base-card bg-white'], // 卡片背景
    // text size
    ['text-size-12px', 'text-12px'], // size-small
    ['text-size-14px', 'text-14px'], // size-default
    ['text-size-16px', 'text-16px'], // size-large
    // icon size
    ['icon-size-small', 'text-16px'], // size-small
    ['icon-size-default', 'text-18px'], // size-default
    ['icon-size-large', 'text-20px'], // size-large
    // button size
    ['button-size-small', 'h-30px text-size-12px'], // size-small
    ['button-size-default', 'h-32px text-size-14px'], // size-default
    ['button-size-large', 'h-40px text-size-16px'], // size-large
    // default button
    ['cfe-button-bg', 'flex-center p-x-5px bg-white'],
    ['cfe-button-text', 'color-text-regular'],
    ['cfe-button-border', 'b b-solid b-color-cfe-border b-rounded-6px'],
    ['cfe-button', 'cfe-button-bg cfe-button-text cfe-button-border button-size-default'],
    ['cfe-button-small', 'cfe-button button-size-small'],
    ['cfe-button-large', 'cfe-button button-size-large'],
    // tools
    ['one-ellipsis', 'text-ellipsis overflow-hidden whitespace-nowrap'], // 一行...
    ['two-ellipsis', 'text-ellipsis overflow-hidden line-clamp-2'], // 两行...
    ['flex-center', 'flex items-center justify-center'], // flex items-center justify-center
    ['full', 'w-full h-full'], // flex items-center justify-center
    // footer-button
    ['footer-button-end', 'h-full flex justify-end items-center'], // footer button
    ['bg-hover', 'hover:bg-gray-100 dark:hover:bg-gray-200'],
    ['no-hover', 'hover:bg-transparent dark:hover:bg-transparent'],
    ['common-transtion', 'transition duration-200 ease-in-out'],
    ['common-icon', 'text-16px inline-block select-none opacity-85 transition duration-200 ease-in-out'],
    ['common-icon-bg', 'w-45px h-45px rounded-full text-center flex items-center justify-center '],
    ['nav-title', 'text-white/90 text-20px font-bold'],
    ['nav-text', 'text-white/90 text-16px'],
    ['nav-icon', 'common-icon h-24px w-24px text-white'],
    ['bg-nav-icon', 'common-icon-bg bg-hover'],
    ['t-line', 'border-t b-color-cfe-border b-t-solid'],
    ['b-line', 'border-b b-color-cfe-border b-b-solid'],
    ['l-line', 'border-l b-color-cfe-border b-l-solid'],
    ['r-line', 'border-r b-color-cfe-border b-r-solid'],
    ['bg-list-item', 'b-line bg-hover'],
    ['btn-bg', 'bg-primary/95 hover:bg-primary/80'],
    ['btn-disabled', 'disabled:cursor-default disabled:b-gray-600 disabled:bg-gray-600 disabled:opacity-50 disabled:hover:bg-gray-600'],
    ['cfe-link', 'inline-block text-primary cursor-pointer hover:underline hover:text-primary/90 btn-disabled'],
    [
      'cfe-btn',
      'px-4 py-1 b-blue rounded inline-block bg-blue-500 text-white cursor-pointer hover:bg-blue-700 disabled:cursor-default disabled:b-gray-600 disabled:bg-gray-600 disabled:opacity-50 disabled:hover:bg-gray-600',
    ],
    ['cfe-btn-orange', 'cfe-btn b-orange bg-orange hover:bg-orange-700'],
    ['bg-floating-btn', 'text-white/90 color-white btn-bg btn-disabled w-40px h-40px shadow rounded-full inline-block cursor-pointer '],

    // prompt-dialog button
    ['prompt-dialog-button', 'w-full h-40px text-size-14px border-rounded-5px'],
    // sample
    ['dashed-bg', 'px-5px p-y-5px border-dashed border border-primary rounded disabled:bg-gray-600 disabled:opacity-50'],
  ],
  rules: [

  ],
  presets: [
    () =>
      window.__unocss_runtime.presets.presetMini(),
    window.__unocss_runtime.presets.presetAttributify(),
    window.__unocss_runtime.presets.presetIcons({
      scale: 1.2,
      cdn: 'https://esm.sh/',
    }),
  ],
}
