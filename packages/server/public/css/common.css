:root {
  --cfe-c-primary: #1890ff;
  --cfe-c-success: #52c41a;
  --cfe-c-warning: #faad14;
  --cfe-c-danger: #f5222d;
  --cfe-c-info: #909399;
  --cfe-c-success-hover: #85ce61;
  --cfe-c-warning-hover: #ebb563;
  --cfe-c-danger-hover: #f78989;
  --cfe-c-info-hover: #a6a9ad;
  --cfe-c-bg: #ffffff;
  --cfe-c-text: #303133;
  --cfe-c-card: #fafbfc;
  --cfe-c-card2: #f7f9ff;
  --cfe-c-card3: #e4e7ed;
  --cfe-c-card4: #ffffff;
  --cfe-c-border: #dcdfe6;
  --cfe-c-hover: #fafbfc;
  --cfe-c-active: #f7f9ff;
  --cfe-text-one: #323b4b;
  --cfe-text-two: #4f596a;
  --cfe-text-three: #b0b7c3;
  --cfe-c-text-one: #323b4b;
  --cfe-c-text-two: #4f596a;
  --cfe-c-text-three: #b0b7c3;
  --cfe-c-text-primary: #323b4b;
  --cfe-c-text-regular: #4f596a;
  --cfe-c-text-secondary: #b0b7c3;
  --cfe-c-text-placeholder: #a8abb2;
  --cfe-c-text-disabled: #c0c4cc;
  --cfe-space-1: 2px;
  --cfe-space-2: 4px;
  --cfe-space-3: 6px;
  --cfe-space-4: 8px;
  --cfe-space-5: 10px;
  --cfe-space-6: 12px;
  --cfe-space-8: 16px;
  --cfe-space-10: 20px;
  --cfe-font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
  --cfe-font-size: 14px;
  --cfe-line-height: 1.5;
  --cfe-radius-sm: 4px;
  --cfe-radius-md: 6px;
  --cfe-radius-lg: 8px;
  --cfe-radius-full: 9999px;
  --cfe-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --cfe-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --cfe-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --cfe-transition: all 0.2s ease-in-out;
  --cfe-z-negative: -1;
  --cfe-z-normal: 1;
  --cfe-z-sticky: 100;
  --cfe-z-drawer: 900;
  --cfe-z-modal: 1000;
  --cfe-z-popup: 1100;
  --cfe-z-toast: 1200;
  --cfe-z-tooltip: 1300;
  --cfe-font-size-xs: 12px;
  --cfe-font-size-sm: 14px;
  --cfe-font-size-md: 16px;
  --cfe-font-size-lg: 18px;
  --cfe-font-size-xl: 20px;
  --cfe-font-size-2xl: 24px;
  --cfe-line-height-tight: 1.25;
  --cfe-line-height-normal: 1.5;
  --cfe-line-height-relaxed: 1.75;
  --cfe-breakpoint-sm: 640px;
  --cfe-breakpoint-md: 768px;
  --cfe-breakpoint-lg: 1024px;
  --cfe-breakpoint-xl: 1280px;
  --cfe-scrollbar-width: 6px;
  --cfe-scrollbar-radius: 3px;
  --cfe-scrollbar-track: #dcdfe6;
  --cfe-scrollbar-thumb: #f7f9ff;
  --cfe-scrollbar-thumb-hover: #fafbfc;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  /* background: var(--cfe-c-border); */
  background: var(--cfe-c-bg);
  overflow: hidden;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
  font-family:
    'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

html {
  background: var(--cfe-app-bg);
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
