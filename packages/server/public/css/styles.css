@import 'page.css';

/* 计时器样式 */
.timer-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: var(--standard-padding);
  box-shadow: var(--box-shadow);
  text-align: center;
  margin-bottom: var(--standard-margin);
}

.timer-circle {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto 20px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.timer-svg {
  transform: rotate(-90deg);
  width: 100%;
  height: 100%;
}

.timer-background {
  fill: none;
  stroke: #f0f0f0;
  stroke-width: 8;
}

.timer-progress {
  fill: none;
  stroke: var(--primary-color);
  stroke-width: 8;
  stroke-linecap: round;
  stroke-dasharray: 283;
  stroke-dashoffset: 283;
  transform-origin: center;
  transform: rotate(-90deg);
  transition: stroke-dashoffset 1s linear;
}

.timer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
}

.time {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  letter-spacing: 1px;
  line-height: 1;
  margin-bottom: 5px;
}

.timer-label {
  font-size: 0.85rem;
  color: var(--text-light);
  letter-spacing: 1px;
}

.timer-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: var(--btn-radius);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn.primary {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 5px rgba(255, 99, 71, 0.3);
}

.btn.primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 99, 71, 0.4);
}

.btn.secondary {
  background-color: #f1f1f1;
  color: var(--text-color);
}

.btn.secondary:hover:not(:disabled) {
  background-color: #e5e5e5;
  transform: translateY(-2px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 模式选择器 */
.mode-selector {
  display: flex;
  justify-content: space-between;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 5px;
  margin: 15px 0;
}

.mode-btn {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  border-radius: 5px;
  font-size: 0.9rem;
  border: none;
  background: none;
  color: var(--text-light);
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-btn:hover {
  color: var(--text-color);
}

.mode-btn.active {
  background-color: white;
  color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 任务部分 */
.tasks-section {
  margin-top: 15px;
  margin-bottom: 15px;
  background-color: #f9f9f9;
  border-radius: 15px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.tasks-section h2 {
  font-size: 1.2rem;
  margin-bottom: 12px;
  color: var(--text-color);
}

.task-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background-color: white;
  border-radius: var(--border-radius);
  padding: 10px 15px;
  margin-bottom: var(--standard-margin);
  box-shadow: var(--box-shadow);
}

.task-input input[type='text'] {
  flex: 1;
  border: none;
  outline: none;
  font-size: 1rem;
  padding: 8px 0;
  background-color: transparent;
}

.task-input input:focus {
  background-color: rgba(249, 249, 249, 0.5);
}

.task-input button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0 15px;
  cursor: pointer;
  transition: var(--transition);
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-input-options {
  display: flex;
  width: 100%;
  margin-top: 10px;
  align-items: center;
  background-color: rgba(249, 249, 249, 0.5);
  border-radius: 8px;
  padding: 8px;
}

.task-priority {
  padding: 6px 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 0.85rem;
  background-color: white;
  cursor: pointer;
}

.pomodoro-estimate {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: var(--text-light);
}

.pomodoro-estimate input {
  width: 40px;
  padding: 5px;
  margin: 0 5px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  text-align: center;
  font-size: 0.85rem;
}

.task-input button:hover {
  background-color: var(--primary-light);
}

.tasks-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.task-item:last-child {
  border-bottom: none;
}

.task-priority-indicator {
  width: 4px;
  height: 70%;
  position: absolute;
  left: 0;
  top: 15%;
  border-radius: 0 2px 2px 0;
}

.priority-high .task-priority-indicator {
  background-color: #ff6347;
}

.priority-medium .task-priority-indicator {
  background-color: #ffa500;
}

.priority-low .task-priority-indicator {
  background-color: #32cd32;
}

.task-item input[type='checkbox'] {
  margin-right: 10px;
  min-width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.task-item label {
  flex: 1;
  font-size: 0.95rem;
  transition: var(--transition);
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-pomodoros {
  display: flex;
  align-items: center;
  margin-right: 10px;
  color: var(--text-light);
  font-size: 0.85rem;
  white-space: nowrap;
}

.task-pomodoros-count {
  margin-right: 3px;
}

.task-item input[type='checkbox']:checked + label {
  text-decoration: line-through;
  color: var(--text-light);
}

.delete-task-btn {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统计部分 */
.stats-section {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: var(--standard-padding);
  box-shadow: var(--box-shadow);
}

.stats-section h2 {
  font-size: 1.2rem;
  margin-bottom: 12px;
  color: var(--text-color);
}

.stats {
  display: flex;
  justify-content: space-between;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* 导航栏样式修复 */
.navigation {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px 0 8px;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 30px 30px; /* 匹配手机屏幕下方圆角 */
  z-index: 10;
}

.nav-btn {
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 1.2rem;
  padding: 8px 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.nav-btn:hover {
  color: var(--primary-color);
  background-color: rgba(255, 99, 71, 0.1);
}

.nav-btn.active {
  color: var(--primary-color);
  background-color: rgba(255, 99, 71, 0.1);
}

/* 统计详情页面样式 */
.stats-card {
  margin-bottom: var(--standard-margin);
}

.stats-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 10px;
}

.date-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.85rem;
  color: var(--text-color);
  width: 100%;
  justify-content: space-between;
}

.date-btn {
  background: transparent;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition);
}

.date-btn:hover {
  background-color: #f0f0f0;
}

.chart-container {
  margin-top: 10px;
  padding: 5px 0 15px;
  position: relative;
}

.chart {
  height: 150px;
  margin-top: 15px;
  margin-bottom: 8px;
  display: flex;
  align-items: flex-end;
  position: relative;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 100%;
  width: 100%;
  position: relative;
  z-index: 1;
}

.chart-bar {
  flex: 1;
  background-color: rgba(255, 99, 71, 0.8);
  margin: 0 3px;
  border-radius: 4px 4px 0 0;
  position: relative;
  min-height: 5px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  transition: height 0.3s ease;
}

.chart-bar span {
  position: absolute;
  top: -20px;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-color);
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  width: 100%;
}

.chart-labels div {
  font-size: 0.7rem;
  color: var(--text-light);
  text-align: center;
  flex: 1;
  padding: 0 2px;
}

.chart-legend {
  text-align: center;
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 10px;
}

.stats-summary {
  display: flex;
  justify-content: space-between;
  background-color: #f9f9f9;
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 15px;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.summary-value {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--primary-color);
}

.summary-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

.stats-details {
  background-color: #f9f9f9;
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 15px;
}

.stats-details h2 {
  font-size: 1.2rem;
  margin-bottom: 12px;
}

.records-list {
  margin-top: 15px;
}

.record-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 0.95rem;
}

.record-item:last-child {
  border-bottom: none;
}

.record-date {
  flex: 1;
  color: var(--text-light);
}

.record-task {
  flex: 2;
  font-weight: 500;
}

.record-duration {
  flex: 1;
  text-align: right;
  color: var(--text-light);
}

/* 设置页面样式 */
.settings-section {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: 15px;
  box-shadow: var(--shadow);
  margin-bottom: 15px;
}

.settings-section h2 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--text-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 1rem;
}

.setting-control {
  display: flex;
  align-items: center;
}

.time-input {
  width: 60px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  text-align: center;
  font-size: 0.95rem;
}

.unit {
  margin-left: 8px;
  font-size: 0.9rem;
  color: var(--text-light);
}

.select-input {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: white;
  font-size: 0.95rem;
  min-width: 150px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: '';
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(24px);
}

.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

.color-options {
  display: flex;
  gap: 12px;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: var(--transition);
}

.color-option:first-child {
  border-color: white;
  box-shadow: 0 0 0 2px var(--primary-color);
}

.color-option:hover {
  transform: scale(1.1);
}

/* 个人档案页面样式 */
.profile-card {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: 15px;
  box-shadow: var(--shadow);
  margin-bottom: 15px;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.profile-avatar {
  font-size: 4rem;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-name {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

.profile-bio {
  color: var(--text-light);
  font-size: 0.95rem;
}

.achievements-section {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: 15px;
  box-shadow: var(--shadow);
  margin-bottom: 15px;
}

.achievements-section h2 {
  font-size: 1.2rem;
  margin-bottom: 15px;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-top: 10px;
}

.achievement-item {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 8px;
  text-align: center;
  transition: var(--transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.achievement-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.achievement-icon {
  font-size: 1.8rem;
  margin-bottom: 12px;
}

.achievement-icon.unlocked {
  color: var(--primary-color);
}

.achievement-icon.locked {
  color: #cccccc;
}

.achievement-name {
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.achievement-desc {
  font-size: 0.8rem;
  color: var(--text-light);
}

.stats-overview {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: 15px;
  box-shadow: var(--shadow);
  margin-bottom: 15px;
}

.stats-overview h2 {
  font-size: 1.2rem;
  margin-bottom: 15px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-top: 10px;
}

.stat-box {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 12px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-box-value {
  font-size: 1.6rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.stat-box-label {
  font-size: 0.9rem;
  color: var(--text-light);
}
