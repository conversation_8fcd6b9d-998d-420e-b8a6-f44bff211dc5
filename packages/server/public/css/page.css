:root {
  --primary-color: #ff6347;
  --primary-hover: #ff7f67;
  --primary-light: rgba(255, 99, 71, 0.2);
  --text-color: #333333;
  --text-light: #666666;
  --bg-color: #ffffff;
  --page-header-bg: #f5f5f5;
  --page-header-text: #333333;
  --page-header-height: 40px;

  --nav-bar-bg: #f5f5f5;
  --nav-bar-height: 45px;
  --card-bg: #f9f9f9;
  --border-radius: 12px;
  --btn-radius: 8px;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --hover-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --standard-padding: 15px;
  --standard-margin: 15px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Aria<PERSON>, sans-serif;
}

body {
  font-family: 'PingFang SC', 'Helvetica Neue', <PERSON>l, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: #f5f5f5;
  margin: 0;
  padding: 20px;
}

/* 页面标题样式改进 */
.pages-title {
  text-align: center;
  margin: 20px 0 30px;
  color: var(--text-color);
  font-size: 2rem;
  font-weight: 600;
  position: relative;
}

.pages-title:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

/* 修改页面容器布局为平铺展示 */
.pages-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px;
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
}
/* 手机边框样式美化 */
.phone-frame {
  position: relative;
  width: 340px;
  height: 680px;
  background: linear-gradient(145deg, #1a1a1a, #0c0c0c);
  border-radius: 40px;
  padding: 15px 10px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  margin-bottom: 50px;
  display: flex;
  justify-content: center;
  border: 1px solid #333;
  transition: transform 0.3s;
}

.phone-frame:hover {
  transform: translateY(-5px);
}

/* 添加电源和音量按钮 */
.phone-frame:before {
  content: '';
  position: absolute;
  top: 100px;
  right: -2px;
  width: 4px;
  height: 60px;
  background-color: #333;
  border-radius: 2px 0 0 2px;
}

.phone-frame:after {
  content: '';
  position: absolute;
  top: 180px;
  left: -2px;
  width: 4px;
  height: 100px;
  background-color: #333;
  border-radius: 0 2px 2px 0;
}

.phone-screen {
  width: 320px;
  height: 650px;
  background-color: #fff;
  border-radius: 30px;
  overflow: hidden;
  position: relative;
}

/* 美化刘海屏 */
.phone-notch {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 140px;
  height: 24px;
  background-color: #000;
  border-radius: 0 0 18px 18px;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-notch:before {
  content: '';
  width: 8px;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  position: absolute;
  left: 50px;
}

.phone-notch:after {
  content: '';
  width: 50px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  position: absolute;
}

.page-container {
  width: 100%;
  height: 100%;
  margin: 0;
  background-color: #fff;
  border-radius: 20px;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.page-title {
  background-color: #f5f5f5;
  color: #555;
  padding: 8px 15px;
  margin: 0;
  font-size: 0.9rem;
  text-align: center;
  border-bottom: 1px solid #ddd;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.page-content {
  padding: 12px 15px;
  overflow-y: auto;
  margin-bottom: var(--nav-bar-height); /* 为导航栏预留空间 */
  /* 隐藏滚动条但保留功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.page-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

header {
  text-align: center;
  width: 100%;
}

header h1 {
  color: var(--primary-color);
  font-size: 2.2rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

header .status-bar {
  width: 100%;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.8);
}

header .app-bar {
  width: 100%;
  height: var(--page-header-height);
  line-height: var(--page-header-height);
  background-color: var(--page-header-bg);
  border-bottom: 1px solid #ddd;
  color: var(--page-header-text);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

header .app-bar .app-bar-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
}

nav {
  background-color: var(--nav-bar-bg);
  height: var(--nav-bar-height);
  line-height: var(--nav-bar-height);
  text-align: center;
  font-size: 16px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .pages-container {
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .pages-container {
    gap: 20px;
    padding: 10px;
  }

  .phone-frame {
    width: 320px;
    height: 640px;
    margin-bottom: 30px;
  }

  .phone-screen {
    width: 300px;
    height: 610px;
  }
}

@media (max-width: 480px) {
  .pages-container {
    gap: 15px;
    padding: 5px;
  }

  .phone-frame {
    width: 300px;
    height: 600px;
    margin-bottom: 20px;
  }

  .phone-screen {
    width: 280px;
    height: 570px;
  }
}
