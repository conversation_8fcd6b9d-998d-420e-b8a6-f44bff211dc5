<!--
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-03-07 17:31:06
 * @LastEditTime: 2025-07-15 18:28:36
 * @LastEditors: shaojun
 * @Description:
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mermaid Diagram</title>
    <link rel="stylesheet" href="./css/common.css" />
    <!-- 引入unocss的preset-icons、preset-mini、preset-attributify -->
    <script src="https://cdn.jsdelivr.net/npm/@unocss/runtime/preset-icons.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@unocss/runtime/preset-mini.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@unocss/runtime/preset-attributify.global.js"></script>
    <!-- 使用EBM命名class，优先使用原子化css，使用unocss.config.js配置 -->
    <script src="unocss.config.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@unocss/runtime/core.global.js"></script>
    <!-- 使用vue3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  </head>

  <body>
    <section
      class="flex flex-col justify-start items-center base-card m-10px p-0"
      style="height: calc(100% - 20px)"
      border-rd-10px
    >
      <header class="bg-primary flex-shrink-0 text-white" w-full border-rd-t-10px>
        <div class="p-10px">
          <p class="text-24px font-bold tracking-tight">Welcome to CFE Node Server</p>
          <p class="text-16px mt-5px opacity-90">Server is running...</p>
        </div>
      </header>

      <main class="flex flex-1 flex-col overflow-y-auto" dashed-bg>
        <content class="flex-1 flex flex-col gap-6 base-card2 p-10px">
          <div class="links flex flex-wrap gap-4">
            <a
              class="cfe-btn px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              href="/swagger"
            >
              Swagger API Documentation
            </a>
            <a
              class="cfe-btn px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
              href="/api/swagger.json"
              >Swagger JSON</a
            >
            <a
              class="cfe-btn px-6 py-3 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors"
              href="/api/swagger.yaml"
              >Swagger YAML</a
            >
          </div>
        </content>
        <!-- images -->
        <div class="images flex flex-col justify-center items-center gap-5px">
          <img src="./images/1.jpg" alt="logo" w-full />
          <img src="./images/2.jpg" alt="logo" w-full />
          <img src="./images/3.jpg" alt="logo" w-full />
          <img src="./images/4.jpg" alt="logo" w-full />
        </div>
      </main>

      <footer class="flex-shrink-0 border-t border-gray-200 py-10px" w-full>
        <div flex="~ col gap-10px" class="text-center text-gray-600">
          <div class="text-base">由 koa 构建</div>
          <div class="text-sm opacity-75">© 2025 All Rights Reserved</div>
        </div>
      </footer>
    </section>
  </body>
</html>
