{"openapi": "3.0.0", "info": {"title": "CFE Node Server API", "version": "1.0.0", "description": "基于 Node.js + Koa + TypeScript 的后端服务框架"}, "paths": {"/api/user/login": {"post": {"summary": "用户登录", "description": "用户登录接口，返回用户信息和 JWT token", "operationId": "login", "tags": ["用户管理"], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}, {"required": true, "schema": {"type": "string", "enum": ["application/json"]}, "description": "请求内容类型，必须为 application/json", "name": "Content-Type", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "用户代理信息", "name": "User-Agent", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "请求追踪ID", "name": "X-Request-ID", "in": "header"}], "methodName": "login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_LoginVO_"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "string"}}}}, "401": {"description": "用户名或密码错误", "content": {"application/json": {"schema": {"type": "string"}}}}, "429": {"description": "请求过于频繁", "content": {"application/json": {"schema": {"type": "string"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/user/info": {"get": {"summary": "获取用户信息", "description": "获取当前登录用户的详细信息", "operationId": "getUserInfo", "tags": ["用户管理"], "parameters": [{"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}, {"required": true, "schema": {"type": "string", "pattern": "^Bearer .+"}, "description": "Bearer token，格式: Bearer <token>", "name": "Authorization", "in": "header"}, {"required": false, "schema": {"type": "string"}, "description": "请求追踪ID", "name": "X-Request-ID", "in": "header"}], "methodName": "getUserInfo", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_GetUserInfoVO_"}}}}, "401": {"description": "未授权，token 无效或已过期", "content": {"application/json": {"schema": {"type": "string"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"type": "string"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/health": {"get": {"summary": "健康检查", "description": "服务健康检查接口", "operationId": "getHealth", "tags": ["系统健康检查"], "parameters": [], "methodName": "getHealth", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/R_GetHealthVO_"}}}}}}}}, "components": {"parameters": {"0": {"name": "Authorization", "in": "header", "required": false, "schema": {"type": "string"}, "description": "Bearer token，登录后获取"}}, "schemas": {"R_LoginVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LoginVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_GetUserInfoVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetUserInfoVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_GetHealthVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetHealthVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_StringData_": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "StringData": {"_def": {"checks": [], "typeName": "ZodString", "coerce": false, "openapi": {"metadata": {"example": ""}}}, "~standard": {"version": 1, "vendor": "zod"}}, "AppInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "BuildInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "DownloadInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "InviteInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "MessageInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "MiniappInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "TeamInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "UserInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "VersionInfo": {"_def": {"unknownKeys": "strip", "catchall": {"_def": {"typeName": "<PERSON><PERSON><PERSON><PERSON>"}, "~standard": {"version": 1, "vendor": "zod"}}, "typeName": "ZodObject"}, "~standard": {"version": 1, "vendor": "zod"}, "_cached": null}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "minLength": 3, "maxLength": 20, "description": "用户名", "example": "admin"}, "password": {"type": "string", "minLength": 6, "maxLength": 20, "description": "密码", "example": "123456"}}, "required": ["username", "password"]}, "LoginVO": {"type": "object", "properties": {"data": {"type": "object", "properties": {"code": {"type": "number", "example": 0}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "admin"}, "nickname": {"type": "string", "example": "管理员"}, "avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "role": {"type": "string", "enum": ["admin", "user"], "example": "admin"}}, "required": ["id", "username", "role"]}, "token": {"type": "string", "description": "JWT 访问令牌", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}, "required": ["user", "token"]}, "message": {"type": "string", "example": "登录成功"}}, "required": ["code", "data", "message"]}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["data", "resultCode", "resultMessage"]}, "GetUserInfoVO": {"type": "object", "properties": {"data": {"type": "object", "properties": {"code": {"type": "number", "example": 0}, "data": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "admin"}, "nickname": {"type": "string", "example": "管理员"}, "avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "role": {"type": "string", "enum": ["admin", "user"], "example": "admin"}}, "required": ["id", "username", "role"]}, "message": {"type": "string", "example": "获取成功"}}, "required": ["code", "data", "message"]}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["data", "resultCode", "resultMessage"]}, "GetHealthVO": {"type": "object", "properties": {"status": {"type": "string", "description": "服务状态", "example": "ok"}, "timestamp": {"type": "string", "description": "时间戳", "example": "2025-07-20T12:59:51.902Z"}, "db": {"anyOf": [{"type": "string"}, {"nullable": true}, {"nullable": true}], "description": "数据库状态", "example": "ok"}}, "required": ["status", "timestamp"]}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization"}}}