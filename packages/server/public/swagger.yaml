openapi: 3.0.0
info:
  title: Server Api 列表
  description: API DOC
  version: 1.0.0
paths:
  '/api/apps/{teamId}':
    get:
      summary: 获取团队下App列表
      operationId: getAppsGET
      description: 获取团队下App列表
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetAppsResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/{id}':
    get:
      summary: 获取某个应用详情
      operationId: getAppDetailGET
      description: 获取某个应用详情
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetAppDetailResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
    delete:
      summary: 删除某个应用
      operationId: deleteAppDELETE
      description: 删除某个应用
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/{id}/versions':
    post:
      summary: 获取某个应用的版本列表(分页)
      operationId: getAppVersionsPOST
      description: 获取某个应用的版本列表(分页)
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: page
          description: 分页页码(可选)
          in: query
          schema:
            type: number
            default: 1
        - name: size
          description: 每页条数(可选)
          in: query
          schema:
            type: number
            default: 10
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetAppVersionsResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/{id}/versions/{versionId}':
    get:
      summary: 获取某个应用的某个版本详情
      operationId: getAppVersionDetailGET
      description: 获取某个应用的某个版本详情
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: versionId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetAppVersionDetailResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
    delete:
      summary: 删除某个版本
      operationId: deleteAppVersionDELETE
      description: 删除某个版本
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: versionId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/{appId}/updateMode':
    post:
      summary: 设置应用或版发布更新方式/静默/强制/普通
      operationId: setUpdateModePOST
      description: 设置应用或版发布更新方式/静默/强制/普通
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetUpdateModeRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/{appId}/profile':
    post:
      summary: 更新应用设置
      operationId: setAppProfilePOST
      description: 更新应用设置
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetAppProfileRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/{appId}/{versionId}/profile':
    post:
      summary: 更新版本设置设置
      operationId: setVersionProfilePOST
      description: 更新版本设置设置
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: versionId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetVersionProfileRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/{appId}/grayPublish':
    post:
      summary: 灰度发布一个版本
      operationId: grayReleaseAppVersionPOST
      description: 灰度发布一个版本
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GrayReleaseAppVersionRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/{appId}/release':
    post:
      summary: 发布或者取消发布某个版本
      operationId: releaseVersionPOST
      description: 发布或者取消发布某个版本
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReleaseVersionRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/app/checkupdate/{teamID}/{platform}/{bundleID}/{currentVersionCode}':
    get:
      summary: 检查版本更新
      operationId: checkUpdateGET
      description: 检查版本更新
      parameters:
        - name: teamID
          required: true
          in: path
          schema:
            type: string
        - name: bundleID
          required: true
          in: path
          schema:
            type: string
        - name: currentVersionCode
          required: true
          in: path
          schema:
            type: string
        - name: platform
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RCheckUpdateResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/app/{appShortUrl}':
    get:
      summary: 通过短链接获取应用最新版本
      operationId: getAppByShortGET
      description: 通过短链接获取应用最新版本
      parameters:
        - name: appShortUrl
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetAppByShortResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/app/{appId}/{versionId}':
    post:
      summary: 取消发布版本
      operationId: cancelReleaseByVersionIdPOST
      description: 取消发布版本
      parameters:
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: versionId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/plist/{appId}/{versionId}':
    get:
      summary: 获取应用的plist文件
      operationId: getAppPlistGET
      description: 获取应用的plist文件
      parameters:
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: versionId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/app/count/{appId}/{versionId}':
    get:
      summary: 增加一次下载次数
      operationId: appCountAppDownloadGET
      description: 增加一次下载次数
      parameters:
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: versionId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - app
      security:
        - ApiKeyAuth: []
  '/api/invite/{inviteId}':
    get:
      summary: 获取某条邀请记录
      operationId: getInviteGET
      description: 获取某条邀请记录
      parameters:
        - name: inviteId
          required: true
          description: 邀请记录id
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetInviteResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 邀请
      security:
        - ApiKeyAuth: []
  /api/messages:
    get:
      summary: 获取该用户消息列表
      operationId: getMessagesGET
      description: 获取该用户消息列表
      parameters:
        - name: page
          description: 分页页码(可选)
          in: query
          schema:
            type: number
            default: 0
        - name: size
          description: 每页条数(可选)
          in: query
          schema:
            type: number
            default: 10
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - message
      security:
        - ApiKeyAuth: []
    delete:
      summary: 清空消息列表
      operationId: clearMessagesDELETE
      description: 清空消息列表
      parameters:
        - name: page
          description: 分页页码(可选)
          in: query
          schema:
            type: number
            default: 0
        - name: size
          description: 每页条数(可选)
          in: query
          schema:
            type: number
            default: 10
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - message
      security:
        - ApiKeyAuth: []
  /api/messages/count:
    get:
      summary: 获取消息总条数和未读条数
      operationId: getMessageCountGET
      description: 获取消息总条数和未读条数
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - message
      security:
        - ApiKeyAuth: []
  /api/messages/markread:
    get:
      summary: 把消息全部标记为已读
      operationId: markMessageReadGET
      description: 把消息全部标记为已读
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - message
      security:
        - ApiKeyAuth: []
  /api/messages/add:
    post:
      summary: 添加消息记录
      operationId: addMessagePOST
      description: 添加消息记录
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddMessageRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - message
      security:
        - ApiKeyAuth: []
  /api/miniapps/create:
    post:
      summary: 创建一个小程序
      operationId: createMiniappPOST
      description: 创建一个小程序
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMiniappRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - miniapp
      security:
        - ApiKeyAuth: []
  '/api/miniapps/{teamId}':
    get:
      summary: 获取团队下小程序列表
      operationId: getMiniappsGET
      description: 获取团队下小程序列表
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetMiniappsResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - miniapp
      security:
        - ApiKeyAuth: []
  '/api/miniapps/{teamId}/{id}':
    get:
      summary: 获取某个小程序详情
      operationId: getMiniappDetailGET
      description: 获取某个小程序详情
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetMiniappDetailResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - miniapp
      security:
        - ApiKeyAuth: []
    delete:
      summary: 删除某个小程序应用
      operationId: deleteMiniappDELETE
      description: 删除某个小程序应用
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - miniapp
      security:
        - ApiKeyAuth: []
  '/api/miniappDetail/{platform}/{appId}':
    get:
      summary: 根据小程序appid和项目名称获取小程序详情
      operationId: getMiniappDetailByAppIdAndPlatformGET
      description: 根据小程序appid和项目名称获取小程序详情
      parameters:
        - name: platform
          required: true
          in: path
          schema:
            type: string
        - name: appId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: false
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetMiniappDetailByAppIdAndPlatformResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - miniapp
      security:
        - ApiKeyAuth: []
  /api/miniapps/addDownloadCode:
    post:
      summary: 添加小程序二维码
      operationId: adddownloadcodePOST
      description: 添加小程序二维码
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: false
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdddownloadcodeRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - miniapp
      security:
        - ApiKeyAuth: []
  /api/miniapps/removeDownloadCode:
    post:
      summary: 删除一个下载二维码
      operationId: removedownloadcodePOST
      description: 删除一个下载二维码
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RemovedownloadcodeRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - miniapp
      security:
        - ApiKeyAuth: []
  /api/team/create:
    post:
      summary: 创建一个团队
      operationId: createTeamPOST
      description: 创建一个团队
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTeamRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RCreateTeamResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 团队
      security:
        - ApiKeyAuth: []
  /api/team/changeMemberRole:
    post:
      summary: 修改用户角色
      operationId: changeMemberRolePOST
      description: 修改用户角色
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeMemberRoleRequest'
        description: request body
        required: true
      responses:
        '200':
          description: 用户角色已更新
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RChangeMemberRoleResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 团队
      security:
        - ApiKeyAuth: []
  /api/team/changeTeamName:
    post:
      summary: 更新团队名称
      operationId: changeTeamNamePOST
      description: 更新团队名称
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeTeamNameRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 团队
      security:
        - ApiKeyAuth: []
  '/api/team/{teamId}/members':
    get:
      summary: 获取团队成员列表
      operationId: getMembersGET
      description: 获取团队成员列表
      parameters:
        - name: teamId
          required: true
          description: 团队id
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetMembersResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 团队
      security:
        - ApiKeyAuth: []
  '/api/team/dissolve/{teamId}':
    delete:
      summary: 解散一个团队
      operationId: dissolveTeamDELETE
      description: 解散一个团队
      parameters:
        - name: teamId
          required: true
          description: 团队id
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 团队
      security:
        - ApiKeyAuth: []
  '/api/team/{id}/member/{userId}':
    delete:
      summary: 移除某个成员,或者自己离开团队
      operationId: removeMemberDELETE
      description: 移除某个成员,或者自己离开团队
      parameters:
        - name: id
          required: true
          description: 团队id
          in: path
          schema:
            type: string
        - name: userId
          required: true
          description: 成员id
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: 成员移除成功
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RRemoveMemberResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 团队
      security:
        - ApiKeyAuth: []
  '/api/team/{teamId}/invite':
    post:
      summary: 邀请某成员加入团队
      operationId: addMemberPOST
      description: 邀请某成员加入团队
      parameters:
        - name: teamId
          required: true
          description: 团队id
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddMemberRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 团队
      security:
        - ApiKeyAuth: []
  /api/team/join:
    post:
      summary: 受邀加入某个团队
      operationId: JoinTeamPOST
      description: 受邀加入某个团队
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JoinTeamRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 团队
      security:
        - ApiKeyAuth: []
  '/api/apps/{teamId}/upload':
    post:
      summary: 上传apk或者ipa文件到服务器
      operationId: upload1POST
      description: 上传apk或者ipa文件到服务器
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  description: file content
                  type: string
                  format: binary
              required:
                - file
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 上传
      security:
        - ApiKeyAuth: []
  /api/apps/uploadFile:
    post:
      summary: 上传文件
      operationId: uploadFilePOST
      description: 上传文件
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                classify:
                  description: file分类，子目录；不传文件直接存放当前用户id文件夹根目录；
                  type: string
                uploadFile:
                  description: file content
                  type: string
                  format: binary
              required:
                - uploadFile
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RUploadFileResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - 上传
      security:
        - ApiKeyAuth: []
  /api/user/apitoken:
    post:
      summary: 生成apitoken
      operationId: apiTokenPOST
      description: 生成apitoken
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: false
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RApiTokenResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - auth
      security:
        - ApiKeyAuth: []
  /api/user/login:
    post:
      summary: 登录
      operationId: loginPOST
      description: 登录
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: false
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RLoginResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - auth
      security:
        - ApiKeyAuth: []
  /api/user/register:
    post:
      summary: 注册用户
      operationId: registerPOST
      description: 注册用户
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: false
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RRegisterResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - auth
      security:
        - ApiKeyAuth: []
  /api/user/info:
    get:
      summary: 获取用户资料
      operationId: getUserInfoGET
      description: 获取用户资料
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetUserInfoResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - auth
      security:
        - ApiKeyAuth: []
  /api/user/password/modify:
    post:
      summary: 修改用户密码
      operationId: modifyPasswordPOST
      description: 修改用户密码
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ModifyPasswordRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - auth
      security:
        - ApiKeyAuth: []
  /api/user/modify:
    post:
      summary: 修改用户资料
      operationId: modifyUserInfoPOST
      description: 修改用户资料
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ModifyUserInfoRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - auth
      security:
        - ApiKeyAuth: []
  /api/user/teams:
    get:
      summary: 获取用户团队列表
      operationId: getUserTeamsGET
      description: 获取用户团队列表
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RGetUserTeamsResponse'
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - auth
      security:
        - ApiKeyAuth: []
  /api/user/resetPassword:
    post:
      summary: 通过邮箱重置密码
      operationId: resetPasswordPOST
      description: 通过邮箱重置密码
      parameters:
        - name: attach-info
          required: true
          in: header
          schema:
            type: string
        - name: Authorization
          required: true
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
        description: request body
        required: true
      responses:
        '200':
          description: success
        '400':
          description: bad request
        '401':
          description: unauthorized, missing/wrong jwt token
      tags:
        - auth
      security:
        - ApiKeyAuth: []
tags: []
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: apiToken
  schemas:
    DefaultObject:
      type: object
      required:
        - resultCode
        - resultMessage
      properties:
        data:
          type: object
        resultCode:
          type: number
        resultMessage:
          type: string
    App:
      type: object
      required:
        - id
        - platform
        - bundleId
        - bundleName
      properties:
        id:
          type: string
        platform:
          type: string
        bundleId:
          type: string
        bundleName:
          type: string
        appName:
          type: string
        currentVersion:
          type: string
        creatorId:
          type: string
        creator:
          type: string
        createAt:
          type: string
        icon:
          type: string
        describe:
          type: string
        updateAt:
          type: string
        shortUrl:
          type: string
        autoPublish:
          type: boolean
        installWithPwd:
          type: boolean
        installPwd:
          type: string
        appLevel:
          type: string
        ownerId:
          type: string
        changelog:
          type: string
        updateMode:
          type: string
          enum:
            - silent
            - normal
            - force
          default: normal
        releaseVersionCode:
          type: string
        releaseVersionId:
          type: string
        grayReleaseVersionId:
          type: string
        totalDownloadCount:
          type: number
        todayDownloadCount:
          type: object
          properties:
            date:
              type: string
            count:
              type: number
        grayStrategy:
          type: object
          properties:
            ipType:
              type: string
              enum:
                - white
                - black
            count:
              type: number
            ipList:
              type: array
              items:
                type: string
            downloadCountLimit:
              type: number
            updateMode:
              type: string
              enum:
                - silent
                - normal
                - force
              default: normal
    Build:
      type: object
      properties: {}
    Message:
      type: object
      properties: {}
    MiniApp:
      type: object
      required:
        - id
        - platform
        - appName
        - appId
        - appSecret
        - pagePath
      properties:
        id:
          type: string
        platform:
          type: string
        appName:
          type: string
        appId:
          type: string
        appSecret:
          type: string
        pagePath:
          type: string
        creatorId:
          type: string
        creator:
          type: string
        createAt:
          type: string
        icon:
          type: string
        describe:
          type: string
        updateAt:
          type: string
        ownerId:
          type: string
        changelog:
          type: string
        downloadCodeImage:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
              image:
                type: string
              env:
                type: string
              createAt:
                type: string
              developer:
                type: string
              version:
                type: string
              desc:
                type: string
              pagePath:
                type: string
              searchQuery:
                type: string
    Team:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        name:
          type: string
        isDefault:
          type: boolean
        icon:
          type: string
        creatorId:
          type: string
        createAt:
          type: string
        members:
          type: array
          items:
            type: object
            required:
              - id
              - username
              - email
              - role
            properties:
              id:
                type: string
              username:
                type: string
              email:
                type: string
              role:
                type: string
                enum:
                  - owner
                  - manager
                  - guest
                default: guest
              userAvatar:
                type: string
    User:
      type: object
      required:
        - id
        - username
        - password
        - email
      properties:
        id:
          type: string
        username:
          type: string
        userAvatar:
          type: string
        userAvatarHistory:
          type: array
          items:
            type: string
        password:
          type: string
        email:
          type: string
        token:
          type: string
        apiToken:
          type: string
        teams:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              name:
                type: string
              icon:
                type: string
              role:
                type: string
        mobile:
          type: string
        qq:
          type: string
        company:
          type: string
        career:
          type: string
    Version:
      type: object
      properties:
        id:
          type: string
        bundleId:
          type: string
        icon:
          type: string
        versionStr:
          type: string
        versionCode:
          type: string
        uploadAt:
          type: string
        uploader:
          type: string
        uploaderId:
          type: string
        size:
          type: number
        active:
          type: boolean
        downloadUrl:
          type: string
        downloadCount:
          type: number
        fileDownloadUrl:
          type: string
        installUrl:
          type: string
        showOnDownloadPage:
          type: boolean
        appLevel:
          type: string
        changelog:
          type: string
        md5:
          type: string
        hidden:
          type: boolean
        updateMode:
          type: string
          enum:
            - silent
            - normal
            - force
          default: normal
        released:
          type: boolean
    Page:
      type: object
      properties:
        pageIndex:
          type: number
          example: 1
        pageSize:
          type: number
          example: 10
        totalCounts:
          type: number
          example: 100
    Invite:
      type: object
      required:
        - id
        - userName
        - teamId
        - emails
        - type
        - status
      properties:
        id:
          type: string
        userName:
          type: string
        teamId:
          type: string
        emails:
          type: array
          items:
            type: object
            required:
              - email
              - status
            properties:
              email:
                type: string
              status:
                type: string
                enum:
                  - EBL
                  - DBL
                default: DBL
        type:
          type: string
          enum:
            - TEAM
          default: TEAM
        status:
          type: string
          enum:
            - EBL
            - DBL
          default: DBL
    RGetAppsResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          type: array
          items:
            $ref: '#/components/schemas/App'
    RGetAppDetailResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/App'
    GetAppVersionsResponseData:
      type: object
      properties:
        page:
          $ref: '#/components/schemas/Page'
        versions:
          type: array
          items:
            $ref: '#/components/schemas/Version'
    RGetAppVersionsResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/GetAppVersionsResponseData'
    RGetAppVersionDetailResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/Version'
    SetUpdateModeRequest:
      type: object
      required:
        - updateMode
      properties:
        updateMode:
          type: string
          description: 强制或者静默或者普通升级
          enum:
            - silent
            - normal
            - force
        versionId:
          type: string
          description: 如果传入了versionId则表示设置某个版本的更新方式
    SetAppProfileRequest:
      type: object
      required: []
      properties:
        shortUrl:
          type: string
          description: 应用短连接
        installWithPwd:
          type: boolean
          description: 应用安装是否需要密码
        installPwd:
          type: string
          description: 应用安装的密码
        autoPublish:
          type: boolean
          description: 新版本自动发布
    SetVersionProfileRequest:
      type: object
      required: []
      properties:
        installUrl:
          type: string
          description: 更新文件的安装地址
        showOnDownloadPage:
          type: boolean
          description: 是否显示到下载页
        changelog:
          type: string
          description: 修改日志
        updateMode:
          type: string
          description: 强制或者静默或者普通升级
          enum:
            - silent
            - normal
            - force
    GrayReleaseAppVersionRequestStrategy:
      type: object
      required: []
      properties:
        updateMode:
          type: string
          description: 强制或者静默或者普通升级
          enum:
            - silent
            - normal
            - force
        ipType:
          type: string
          enum:
            - white
            - black
          description: IP地址限制类型 {type:String,default:'black',enum:['black','white']},
        ipList:
          type: array
          items:
            type: string
          description: ip地址列表
        downloadCountLimit:
          type: number
          description: default 0 表示不现在下载次数
    GrayReleaseAppVersionRequestVersion:
      type: object
      required:
        - versionId
        - versionCode
        - release
      properties:
        versionId:
          type: string
        versionCode:
          type: string
        release:
          type: boolean
    GrayReleaseAppVersionRequest:
      type: object
      properties:
        strategy:
          $ref: '#/components/schemas/GrayReleaseAppVersionRequestStrategy'
        version:
          $ref: '#/components/schemas/GrayReleaseAppVersionRequestVersion'
      required: []
    ReleaseVersionRequest:
      type: object
      required:
        - versionId
        - versionCode
        - release
      properties:
        versionId:
          type: string
        versionCode:
          type: string
        release:
          type: boolean
    CheckUpdateResponseDataAppTodayDownloadCount:
      type: object
      properties:
        date:
          type: string
        count:
          type: number
    CheckUpdateResponseDataAppGrayStrategy:
      type: object
      properties:
        ipType:
          type: string
          enum:
            - white
            - black
        count:
          type: number
        ipList:
          type: array
          items:
            type: string
        downloadCountLimit:
          type: number
        updateMode:
          type: string
          enum:
            - silent
            - normal
            - force
          default: normal
    CheckUpdateResponseDataApp:
      type: object
      required:
        - id
        - platform
        - bundleId
        - bundleName
      properties:
        id:
          type: string
        platform:
          type: string
        bundleId:
          type: string
        bundleName:
          type: string
        appName:
          type: string
        currentVersion:
          type: string
        creatorId:
          type: string
        creator:
          type: string
        createAt:
          type: string
        icon:
          type: string
        describe:
          type: string
        updateAt:
          type: string
        shortUrl:
          type: string
        autoPublish:
          type: boolean
        installWithPwd:
          type: boolean
        installPwd:
          type: string
        appLevel:
          type: string
        ownerId:
          type: string
        changelog:
          type: string
        updateMode:
          type: string
          enum:
            - silent
            - normal
            - force
          default: normal
        releaseVersionCode:
          type: string
        releaseVersionId:
          type: string
        grayReleaseVersionId:
          type: string
        totalDownloadCount:
          type: number
        todayDownloadCount:
          $ref: '#/components/schemas/CheckUpdateResponseDataAppTodayDownloadCount'
        grayStrategy:
          $ref: '#/components/schemas/CheckUpdateResponseDataAppGrayStrategy'
    CheckUpdateResponseDataVersion:
      type: object
      properties:
        id:
          type: string
        bundleId:
          type: string
        icon:
          type: string
        versionStr:
          type: string
        versionCode:
          type: string
        uploadAt:
          type: string
        uploader:
          type: string
        uploaderId:
          type: string
        size:
          type: number
        active:
          type: boolean
        downloadUrl:
          type: string
        downloadCount:
          type: number
        fileDownloadUrl:
          type: string
        installUrl:
          type: string
        showOnDownloadPage:
          type: boolean
        appLevel:
          type: string
        changelog:
          type: string
        md5:
          type: string
        hidden:
          type: boolean
        updateMode:
          type: string
          enum:
            - silent
            - normal
            - force
          default: normal
        released:
          type: boolean
    CheckUpdateResponseData:
      type: object
      description: 返回app和version
      properties:
        app:
          $ref: '#/components/schemas/CheckUpdateResponseDataApp'
        version:
          $ref: '#/components/schemas/CheckUpdateResponseDataVersion'
    RCheckUpdateResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/CheckUpdateResponseData'
    GetAppByShortResponseDataAppTodayDownloadCount:
      type: object
      properties:
        date:
          type: string
        count:
          type: number
    GetAppByShortResponseDataAppGrayStrategy:
      type: object
      properties:
        ipType:
          type: string
          enum:
            - white
            - black
        count:
          type: number
        ipList:
          type: array
          items:
            type: string
        downloadCountLimit:
          type: number
        updateMode:
          type: string
          enum:
            - silent
            - normal
            - force
          default: normal
    GetAppByShortResponseDataApp:
      type: object
      required:
        - id
        - platform
        - bundleId
        - bundleName
      properties:
        id:
          type: string
        platform:
          type: string
        bundleId:
          type: string
        bundleName:
          type: string
        appName:
          type: string
        currentVersion:
          type: string
        creatorId:
          type: string
        creator:
          type: string
        createAt:
          type: string
        icon:
          type: string
        describe:
          type: string
        updateAt:
          type: string
        shortUrl:
          type: string
        autoPublish:
          type: boolean
        installWithPwd:
          type: boolean
        installPwd:
          type: string
        appLevel:
          type: string
        ownerId:
          type: string
        changelog:
          type: string
        updateMode:
          type: string
          enum:
            - silent
            - normal
            - force
          default: normal
        releaseVersionCode:
          type: string
        releaseVersionId:
          type: string
        grayReleaseVersionId:
          type: string
        totalDownloadCount:
          type: number
        todayDownloadCount:
          $ref: '#/components/schemas/GetAppByShortResponseDataAppTodayDownloadCount'
        grayStrategy:
          $ref: '#/components/schemas/GetAppByShortResponseDataAppGrayStrategy'
    GetAppByShortResponseDataVersion:
      type: object
      properties:
        id:
          type: string
        bundleId:
          type: string
        icon:
          type: string
        versionStr:
          type: string
        versionCode:
          type: string
        uploadAt:
          type: string
        uploader:
          type: string
        uploaderId:
          type: string
        size:
          type: number
        active:
          type: boolean
        downloadUrl:
          type: string
        downloadCount:
          type: number
        fileDownloadUrl:
          type: string
        installUrl:
          type: string
        showOnDownloadPage:
          type: boolean
        appLevel:
          type: string
        changelog:
          type: string
        md5:
          type: string
        hidden:
          type: boolean
        updateMode:
          type: string
          enum:
            - silent
            - normal
            - force
          default: normal
        released:
          type: boolean
    GetAppByShortResponseData:
      type: object
      description: 返回app和version
      properties:
        app:
          $ref: '#/components/schemas/GetAppByShortResponseDataApp'
        version:
          $ref: '#/components/schemas/GetAppByShortResponseDataVersion'
    RGetAppByShortResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/GetAppByShortResponseData'
    RGetInviteResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/Invite'
    AddMessageRequest:
      type: object
      required:
        - category
        - content
        - sender
        - receiver
        - sendAt
        - status
        - data
      properties:
        category:
          type: string
          example: DEFAULT
        content:
          type: string
          example: MessageContent
        sender:
          type: string
          example: STSTEM
        receiver:
          type: string
          example: id12345
        sendAt:
          type: string
          example: 2021/01/01 00:00:00
        status:
          type: string
          example: owner
        data:
          type: string
          example: MessageData
    CreateMiniappRequest:
      type: object
      required:
        - appName
        - appId
        - appSecret
        - platform
        - pagePath
        - ownerId
      properties:
        appName:
          type: string
        appId:
          type: string
        appSecret:
          type: string
        platform:
          type: string
        pagePath:
          type: string
        ownerId:
          type: string
    RGetMiniappsResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          type: array
          items:
            $ref: '#/components/schemas/MiniApp'
    RGetMiniappDetailResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/MiniApp'
    RGetMiniappDetailByAppIdAndPlatformResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/MiniApp'
    AdddownloadcodeRequest:
      type: object
      required:
        - appId
        - platform
        - type
        - image
        - env
        - developer
        - createAt
      properties:
        appId:
          type: string
        platform:
          type: string
        type:
          type: string
        image:
          type: string
        env:
          type: string
        developer:
          type: string
        createAt:
          type: string
        version:
          type: string
        desc:
          type: string
        pagePath:
          type: string
        searchQuery:
          type: string
    RemovedownloadcodeRequest:
      type: object
      required:
        - appId
        - platform
        - type
      properties:
        appId:
          type: string
        platform:
          type: string
        type:
          type: string
    CreateTeamRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: testTeam
          description: 团队名称
        icon:
          type: string
          description: 团队图标
    RCreateTeamResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/Team'
    ChangeMemberRoleRequest:
      type: object
      required:
        - teamId
        - memberId
        - role
      properties:
        teamId:
          type: string
          description: 团队id
        memberId:
          type: string
          description: 申请人id
        role:
          type: string
          enum:
            - owner
            - manager
            - guest
          example: guest
          description: 团队角色
    RChangeMemberRoleResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/Team'
    ChangeTeamNameRequest:
      type: object
      required:
        - teamId
        - name
      properties:
        teamId:
          type: string
          description: 团队id
        name:
          type: string
          description: 更改的团队名称
    RGetMembersResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/Team'
    RRemoveMemberResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/Team'
    AddMemberRequest:
      type: object
      required:
        - emailList
      properties:
        emailList:
          type: array
          items:
            type: string
          description: 邮箱列表
    JoinTeamRequest:
      type: object
      required:
        - teamId
        - inviteId
      properties:
        teamId:
          type: string
          description: 团队id
        inviteId:
          type: string
          description: 邀请id
    UploadFileResponseData:
      type: object
      required:
        - httpUrl
        - relativeUrl
      properties:
        httpUrl:
          type: string
          example: ''
        relativeUrl:
          type: string
          example: ''
    RUploadFileResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/UploadFileResponseData'
    RApiTokenResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          type: string
          description: 生成apitoken
    RLoginResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/User'
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          example: test
        password:
          type: string
          example: '123456'
    RRegisterResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/User'
    RegisterRequest:
      type: object
      required:
        - username
        - email
        - password
      properties:
        username:
          type: string
          example: test
        email:
          type: string
          example: <EMAIL>
        password:
          type: string
          example: '123456'
    RGetUserInfoResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/User'
    ModifyPasswordRequest:
      type: object
      required:
        - oldpwd
        - newpwd
      properties:
        oldpwd:
          type: string
          example: '123456'
        newpwd:
          type: string
          example: '123456'
    ModifyUserInfoRequest:
      type: object
      properties:
        mobile:
          type: string
        qq:
          type: string
        company:
          type: string
        career:
          type: string
        userAvatar:
          type: string
      required: []
    RGetUserTeamsResponse:
      type: object
      properties:
        resultCode:
          type: number
        resultMessage:
          type: string
          example: ''
        data:
          $ref: '#/components/schemas/User'
    ResetPasswordRequest:
      type: object
      required:
        - email
      properties:
        email:
          type: string
