{"name": "@cfe-node/server", "type": "module", "version": "1.0.0", "private": true, "exports": {".": {"types": "./dist/app.d.ts", "import": "./dist/app.mjs", "require": "./dist/app.cjs"}, "./*": "./*"}, "main": "./dist/app.cjs", "module": "./dist/app.mjs", "types": "./dist/app.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "dev": "cross-env NODE_ENV=development vite --open", "dev:staging": "cross-env NODE_ENV=staging vite --open", "build": "vite build", "build:development": "cross-env NODE_ENV=development vite build", "build:staging": "cross-env NODE_ENV=staging vite build", "build:prod": "cross-env NODE_ENV=production vite build", "prebuild": "<PERSON><PERSON><PERSON> dist", "docker:development": "cd docker && ./env-manager.sh start development", "docker:staging": "cd docker && ./env-manager.sh start staging", "docker:prod": "cd docker && ./env-manager.sh start production", "docker:stop": "cd docker && ./env-manager.sh stop development", "docker:stop:staging": "cd docker && ./env-manager.sh stop staging", "docker:stop:prod": "cd docker && ./env-manager.sh stop production", "docker:logs": "cd docker && ./env-manager.sh logs development", "docker:logs:staging": "cd docker && ./env-manager.sh logs staging", "docker:logs:prod": "cd docker && ./env-manager.sh logs production", "deploy:development": "pnpm run build:development && pnpm run docker:development", "deploy:staging": "pnpm run build:staging && pnpm run docker:staging", "deploy:prod": "pnpm run build:prod && pnpm run docker:prod", "lint": "eslint src --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "typeorm": "typeorm-ts-node-esm -d src/config/database.ts", "migration:generate": "npm run typeorm -- migration:generate", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert", "migration:show": "npm run typeorm -- migration:show"}, "dependencies": {"@cfe-node/koa-swagger-decorator": "workspace:*", "@cfe-node/utils": "workspace:*", "@koa/cors": "^5.0.0", "app-info-parser": "^1.1.6", "axios": "^1.6.8", "class-validator": "^0.14.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "koa": "^2.14.2", "koa-body": "^6.0.1", "koa-bodyparser": "^4.4.1", "koa-compose": "^4.1.0", "koa-convert": "^2.0.0", "koa-helmet": "^7.0.2", "koa-jwt": "^4.0.4", "koa-logger": "^3.2.1", "koa-ratelimit": "^5.1.0", "koa-router": "^12.0.1", "koa-static": "^5.0.0", "ldapjs": "^3.0.7", "lodash-es": "^4.17.21", "log4js": "^6.9.1", "mysql2": "^3.6.1", "redis": "^4.6.7", "reflect-metadata": "^0.1.13", "typeorm": "^0.3.17", "unzipper": "^0.12.3", "winston": "^3.10.0", "yamljs": "^0.3.0"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/fs-extra": "^11.0.4", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/koa": "^2.13.9", "@types/koa-bodyparser": "^4.3.12", "@types/koa-compose": "^3.2.8", "@types/koa-convert": "^1.2.7", "@types/koa-logger": "^3.1.5", "@types/koa-ratelimit": "^5.0.5", "@types/koa-router": "^7.4.5", "@types/koa-static": "^4.0.4", "@types/ldapjs": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^20.8.0", "@types/supertest": "^2.0.12", "@types/yamljs": "^0.2.34", "cross-env": "^7.0.3", "fs-extra": "^11.2.0", "supertest": "^6.3.3", "tsx": "^4.7.0", "vite-plugin-node": "^5.0.0"}}