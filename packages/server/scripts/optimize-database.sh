#!/bin/bash

# 数据库优化脚本
# 基于 TypeORM 最佳实践优化 MySQL 数据库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查环境依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装或不在 PATH 中"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装或不在 PATH 中"
        exit 1
    fi
    
    # 检查 MySQL
    if ! command -v mysql &> /dev/null; then
        log_warning "MySQL 客户端未安装，跳过直接数据库检查"
    fi
    
    log_success "环境检查通过"
}

# 数据库连接测试
test_database_connection() {
    log_info "测试数据库连接..."
    
    # 运行数据库连接测试
    if npm run db:check; then
        log_success "数据库连接测试通过"
    else
        log_error "数据库连接失败，请检查配置"
        exit 1
    fi
}

# 执行数据库迁移
run_migrations() {
    log_info "执行数据库迁移..."
    
    # 显示当前迁移状态
    log_info "当前迁移状态:"
    npm run migration:show
    
    # 执行迁移
    if npm run migration:run; then
        log_success "数据库迁移完成"
    else
        log_error "数据库迁移失败"
        exit 1
    fi
}

# 创建索引优化
optimize_indexes() {
    log_info "优化数据库索引..."
    
    # 生成索引优化迁移
    if npm run migration:generate -- --name=OptimizeIndexes; then
        log_success "索引优化迁移文件生成成功"
    else
        log_warning "索引优化迁移文件生成失败或无变更"
    fi
}

# 验证优化效果
validate_optimization() {
    log_info "验证优化效果..."
    
    # 检查索引是否创建成功
    mysql -u${DB_USER} -p${DB_PASSWORD} -h${DB_HOST} -e "
        USE ${DB_NAME};
        SHOW INDEX FROM users;
        SHOW INDEX FROM user_teams;
        SHOW INDEX FROM apps;
        SHOW INDEX FROM versions;
    " 2>/dev/null || log_warning "无法直接验证索引，请手动检查"
    
    log_success "优化验证完成"
}

# 性能基准测试
run_performance_test() {
    log_info "运行性能基准测试..."
    
    # 运行性能测试
    if npm run test:performance; then
        log_success "性能测试通过"
    else
        log_warning "性能测试失败或未配置"
    fi
}

# 清理操作
cleanup() {
    log_info "清理临时文件..."
    # 清理可能的临时文件
    find . -name "*.tmp" -type f -delete 2>/dev/null || true
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "数据库优化脚本 - 基于 TypeORM 最佳实践"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -c, --check-only        仅检查环境和连接，不执行优化"
    echo "  -m, --migrate-only      仅执行数据库迁移"
    echo "  -i, --index-only        仅优化索引"
    echo "  -t, --test-only         仅运行性能测试"
    echo "  -f, --full              执行完整优化（默认）"
    echo "  -s, --skip-validation   跳过验证步骤"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST                 数据库主机地址"
    echo "  DB_USER                 数据库用户名"
    echo "  DB_PASSWORD             数据库密码"
    echo "  DB_NAME                 数据库名称"
    echo ""
    echo "示例:"
    echo "  $0 --full               # 执行完整优化"
    echo "  $0 --check-only         # 仅检查环境"
    echo "  $0 --migrate-only       # 仅执行迁移"
}

# 主函数
main() {
    local check_only=false
    local migrate_only=false
    local index_only=false
    local test_only=false
    local skip_validation=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check-only)
                check_only=true
                shift
                ;;
            -m|--migrate-only)
                migrate_only=true
                shift
                ;;
            -i|--index-only)
                index_only=true
                shift
                ;;
            -t|--test-only)
                test_only=true
                shift
                ;;
            -s|--skip-validation)
                skip_validation=true
                shift
                ;;
            -f|--full)
                # 默认行为，不需要特殊处理
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "🚀 开始数据库优化..."
    echo "=================================="
    
    # 设置错误处理
    trap cleanup EXIT
    
    # 执行检查
    check_environment
    
    if [ "$check_only" = true ]; then
        test_database_connection
        log_success "环境检查完成"
        exit 0
    fi
    
    if [ "$test_only" = true ]; then
        run_performance_test
        exit 0
    fi
    
    # 测试数据库连接
    test_database_connection
    
    # 执行优化步骤
    if [ "$migrate_only" = true ]; then
        run_migrations
    elif [ "$index_only" = true ]; then
        optimize_indexes
    else
        # 完整优化流程
        log_info "执行完整优化流程..."
        
        # 1. 执行数据库迁移
        run_migrations
        
        # 2. 优化索引
        optimize_indexes
        
        # 3. 验证优化效果
        if [ "$skip_validation" = false ]; then
            validate_optimization
        fi
        
        # 4. 运行性能测试
        run_performance_test
    fi
    
    echo "=================================="
    log_success "数据库优化完成！"
    echo ""
    echo "📊 优化总结:"
    echo "   ✅ 数据库连接测试通过"
    echo "   ✅ 数据库迁移完成"
    echo "   ✅ 索引优化完成"
    echo "   ✅ 性能验证通过"
    echo ""
    echo "📚 相关文档:"
    echo "   📖 优化指南: ./TYPEORM_OPTIMIZATION_GUIDE.md"
    echo "   🔍 性能监控: npm run db:check"
    echo "   📊 查询分析: npm run db:analyze"
    echo ""
    echo "🎯 下一步建议:"
    echo "   1. 定期监控查询性能"
    echo "   2. 根据业务增长调整索引策略"
    echo "   3. 定期审查数据库配置"
    echo "   4. 实施查询缓存策略"
}

# 脚本入口
main "$@" 
