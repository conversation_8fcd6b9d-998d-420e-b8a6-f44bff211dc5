# CFE Node Server 🚀

> 基于 Koa + TypeScript + TypeORM 的现代化 Node.js 服务器，支持多环境配置、自动 API 文档生成和容器化部署。

## ✨ 特性

- 🏗️ **现代架构**：Koa2 + TypeScript + TypeORM
- 📚 **自动文档**：基于装饰器的 Swagger API 文档生成
- 🌍 **多环境支持**：Development / Staging / Production 环境隔离
- 🐳 **容器化部署**：完整的 Docker 配置和环境管理
- 🔐 **安全特性**：JWT 认证、限流、参数验证
- 📝 **完整日志**：结构化日志记录和监控
- ⚡ **高性能**：Redis 缓存、数据库优化
- 🧪 **测试覆盖**：单元测试和集成测试

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0
- Docker & Docker Compose (可选)

### 安装依赖

```bash
# 从项目根目录安装所有依赖
pnpm install

# 或者只安装 server 包依赖
cd packages/server && pnpm install
```

### 开发环境启动

```bash
# 方式1：直接启动开发服务器
pnpm dev

# 方式2：使用 Docker 环境
pnpm docker

# 访问应用
# - API: http://localhost:3000
# - Swagger 文档: http://localhost:3000/api/docs
```

## 🌍 多环境配置方案

> **重构亮点**：我们使用环境配置文件替代多个 docker-compose 文件，实现更简洁的多环境管理。

### 环境概览

| 环境 | 应用端口 | 数据库端口 | Redis端口 | 数据库名 | 特性 |
|------|----------|------------|-----------|----------|------|
| **Development** | 3000 | 3306 | 6379 | cfe_node_server_dev | 自动同步表结构、详细日志 |
| **Staging** | 3001 | 3307 | 6380 | cfe_node_server_staging | 模拟生产环境、启用缓存 |
| **Production** | 8080 | 3308 | 6381 | cfe_node_server_prod | 严格安全检查、性能优化 |

### 配置文件结构

```
packages/server/docker/
├── docker-compose.yml          # 主配置文件
├── env.development            # 开发环境变量
├── env.staging               # 测试环境变量
├── env.production            # 生产环境变量
└── env-manager.sh            # 环境管理脚本
```

### 环境启动命令

```bash
# 开发环境
pnpm dev                    # 本地开发服务器
pnpm docker                 # Docker 开发环境

# 测试环境
pnpm dev:staging           # 本地测试环境
pnpm docker:staging        # Docker 测试环境

# 生产环境
pnpm build:prod            # 构建生产版本
pnpm docker:prod           # Docker 生产环境
```

### 环境管理脚本

我们提供了强大的环境管理工具：

```bash
cd docker

# 列出所有环境
./env-manager.sh list

# 启动环境
./env-manager.sh start development
./env-manager.sh start staging
./env-manager.sh start production

# 停止环境
./env-manager.sh stop <environment>

# 查看状态
./env-manager.sh status <environment>

# 查看日志
./env-manager.sh logs <environment>
./env-manager.sh logs staging db     # 查看特定服务日志

# 重启环境
./env-manager.sh restart <environment>

# 清理环境
./env-manager.sh clean <environment>
```

### 环境切换

```bash
# 快速环境切换
NODE_ENV=development pnpm dev
NODE_ENV=staging pnpm dev
NODE_ENV=production pnpm build

# Docker 环境管理
pnpm docker:stop:staging    # 停止测试环境
pnpm docker:logs:prod       # 查看生产环境日志
```

## 📁 项目结构

```
packages/server/
├── src/
│   ├── app.ts                    # 应用入口
│   ├── config/
│   │   ├── database.ts           # 数据库配置
│   │   ├── env.ts               # 环境变量配置 ⭐
│   │   └── index.ts
│   ├── controllers/              # 控制器
│   ├── middlewares/             # 中间件
│   ├── typeorm/
│   │   ├── entity/              # 数据模型
│   │   ├── migration/           # 数据库迁移
│   │   └── index.ts
│   ├── routes/                  # 路由定义
│   └── utils/                   # 工具函数
├── docker/                      # Docker 配置 ⭐
│   ├── docker-compose.yml       # 主配置文件
│   ├── env.development          # 开发环境配置
│   ├── env.staging             # 测试环境配置
│   ├── env.production          # 生产环境配置
│   ├── env-manager.sh          # 环境管理脚本
│   ├── Dockerfile              # 应用镜像
│   └── init.sql               # 数据库初始化
├── ENVIRONMENT_CONFIG.md        # 环境配置指南 ⭐
└── package.json
```

## 🔧 开发指南

### 本地开发

```bash
# 启动开发服务器（带热重载）
pnpm dev

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 格式化代码
pnpm format
```

### 数据库操作

```bash
# 生成新的迁移文件
pnpm typeorm:generate CreateUsers

# 运行数据库迁移
pnpm typeorm:run

# 回滚迁移
pnpm typeorm:revert
```

### API 开发

1. **创建实体**：在 `src/typeorm/entity/` 下创建实体类
2. **创建控制器**：在 `src/controllers/` 下创建控制器
3. **定义路由**：在 `src/routes/` 下注册路由
4. **使用装饰器**：利用 `@cfe-node/koa-swagger-decorator` 自动生成 API 文档

示例控制器：

```typescript
import { request, summary, tagsAll, responsesAll } from '@cfe-node/koa-swagger-decorator'

@tagsAll(['Users'])
@responsesAll({ 200: { description: 'Success' }, 500: { description: 'Server Error' } })
export default class UserController {
  @request('get', '/users')
  @summary('获取用户列表')
  static async getUsers(ctx: any) {
    // 实现逻辑
  }
}
```

## 🚀 部署指南

### Docker 部署（推荐）

```bash
# 开发环境部署
pnpm deploy

# 测试环境部署
pnpm deploy:staging

# 生产环境部署
pnpm deploy:prod
```

### 生产环境注意事项

1. **环境变量**：确保设置安全的 JWT_SECRET 和数据库密码
2. **数据库迁移**：生产环境禁用自动同步，使用迁移管理表结构
3. **日志配置**：生产环境使用最小化日志输出
4. **资源限制**：Docker 容器已配置合理的内存和 CPU 限制

### 环境变量配置

关键环境变量（在对应的 env 文件中配置）：

```bash
# 应用配置
NODE_ENV=production
PORT=3000
JWT_SECRET=your-super-secure-secret

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-secure-password
DB_NAME=cfe_node_server_prod
DB_SYNC=false

# Redis 配置
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
```

## 📚 API 文档

### Swagger 文档

启动服务器后访问：
- 开发环境：http://localhost:3000/api/docs
- 测试环境：http://localhost:3001/api/docs
- 生产环境：http://localhost:8080/api/docs

### 主要 API 端点

```bash
# 健康检查
GET /api/health

# 用户管理
GET    /api/users
POST   /api/users
GET    /api/users/:id
PUT    /api/users/:id
DELETE /api/users/:id

# 认证
POST   /api/auth/login
POST   /api/auth/register
POST   /api/auth/refresh
```

## 🧪 测试

```bash
# 运行所有测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行特定测试文件
pnpm test health.test.ts
```

## 📊 监控和日志

### 日志配置

- **开发环境**：详细的控制台日志和文件日志
- **测试环境**：结构化日志，适度详细
- **生产环境**：最小化日志，只记录关键信息

### 健康检查

```bash
# 应用健康状态
curl http://localhost:3000/api/health

# 数据库连接状态
curl http://localhost:3000/api/health/db
```

## 🔐 安全特性

- **JWT 认证**：基于 Token 的用户认证
- **参数验证**：请求参数自动验证
- **限流保护**：API 访问频率限制
- **CORS 配置**：跨域请求安全配置
- **错误处理**：统一的错误处理和响应

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支：`git checkout -b feature/amazing-feature`
3. 提交变更：`git commit -m 'Add amazing feature'`
4. 推送到分支：`git push origin feature/amazing-feature`
5. 提交 Pull Request

### 开发规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 代码规范
- 编写单元测试
- 更新相关文档

## 📖 相关文档

- [环境配置详细指南](./ENVIRONMENT_CONFIG.md)
- [Docker 环境配置方案](./docker/README-Environment-Config.md)
- [数据库优化指南](./TYPEORM_OPTIMIZATION_GUIDE.md)
- [实体优化总结](./ENTITY_OPTIMIZATION_SUMMARY.md)
- [项目设计文档](./design.md)

## 🆘 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 修改对应环境文件中的端口配置
   vim docker/env.development
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库服务状态
   ./docker/env-manager.sh status development
   ./docker/env-manager.sh logs development db
   ```

3. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x docker/env-manager.sh
   ```

4. **TypeScript 编译错误**
   ```bash
   # 清理并重新构建
   pnpm clean && pnpm build
   ```

### 获取帮助

- 查看环境管理脚本帮助：`./docker/env-manager.sh help`
- 查看详细日志：`./docker/env-manager.sh logs <environment>`
- 查看项目文档：[ENVIRONMENT_CONFIG.md](./ENVIRONMENT_CONFIG.md)

## 📄 许可证

[MIT License](../../LICENSE)

---

## 🎯 重构亮点总结

### 🌟 环境配置方案重构

**从多文件方案** → **单一配置文件 + 环境变量**

**优势**：
- ✅ **更简洁**：一个 docker-compose.yml 管理所有环境
- ✅ **更标准**：遵循 12-factor app 原则
- ✅ **更安全**：环境完全隔离，避免端口冲突
- ✅ **更便捷**：强大的环境管理脚本
- ✅ **更易维护**：配置集中在环境文件中

**核心文件**：
- `docker/env.development` - 开发环境配置
- `docker/env.staging` - 测试环境配置
- `docker/env.production` - 生产环境配置
- `docker/env-manager.sh` - 一键环境管理

现在您可以轻松管理多个环境，享受更流畅的开发体验！🚀




```bash
cd docker && docker-compose --env-file env.development build --no-cache cfe-node-server

````
