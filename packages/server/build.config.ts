/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-03-21 17:09:56
 * @LastEditTime: 2025-07-11 16:36:50
 * @LastEditors: shaojun
 * @Description:
 */

import { resolve } from 'node:path'
import { defineBuildConfig } from 'unbuild'
import { commonConfig } from '../../configs/build/unbuild'

const root = resolve(__dirname, './')
console.log('root:', root)
export default defineBuildConfig(
  Object.assign(commonConfig, {
    entries: [
      {
        builder: 'mkdist',
        input: './src',
        pattern: ['**/*.ts'],
        format: 'cjs',
        ext: 'cjs',
        loaders: ['js'],
        esbuild: {
          tsconfigRaw: {
            compilerOptions: {
              experimentalDecorators: true,
              emitDecoratorMetadata: true,
            },
          },
        },
      },
      {
        builder: 'mkdist',
        input: './src',
        pattern: ['**/*.ts'],
        format: 'esm',
        ext: 'mjs',
        loaders: ['js'],
        esbuild: {
          minify: false,
          tsconfigRaw: {
            compilerOptions: {
              experimentalDecorators: true,
              emitDecoratorMetadata: true,
            },
          },
        },
      },
    ],
    externals: [
      'reflect-metadata',
      'typeorm',
      'mysql2',
      'winston',
      'koa',
      'koa-router',
      'koa-body',
      'koa-static',
      'koa-bodyparser',
      'koa-helmet',
      'koa-logger',
      '@koa/cors',
      '@asteasolutions/zod-to-openapi',
      'ioredis',
      'redis',
      'jsonwebtoken',
      'crypto-js',
      'dayjs',
      'lodash-es',
      'yamljs',
    ],
  }),
)
