import commonjs from '@rollup/plugin-commonjs'

import json from '@rollup/plugin-json'
import resolve from '@rollup/plugin-node-resolve'
/*
 * @Author: shao<PERSON>
 * @Date: 2024-04-16 16:32:19
 * @LastEditTime: 2024-05-07 10:25:11
 * @LastEditors: shaojun
 * @Description:
 */
import typescript from '@rollup/plugin-typescript'
import nodePolyfills from 'rollup-plugin-node-polyfills'

export default {
  input: 'src/app.ts',
  output: {
    format: 'cjs',
    file: 'dist/app.cjs',
    // format: 'es',
    // file: 'dist/app.mjs',
    sourcemap: true,
  },
  plugins: [
    nodePolyfills(),
    typescript({
      compilerOptions: {
        rootDir: 'src',
        declarationDir: './dist/types',
        declaration: true,
      },
      outDir: 'dist', // 此处的 'outDir' 路径必须与 Rollup 的 'dir' 选项指定的目录一致
    }),

    resolve({
      preferBuiltins: false,
      exportConditions: ['node'],
    }),
    commonjs(),
    json(),

  ],
}
