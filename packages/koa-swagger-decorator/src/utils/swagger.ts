/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-04-12 11:36:56
 * @LastEditTime: 2024-04-29 09:14:28
 * @LastEditors: shaojun
 * @Description:
 */

import type { OpenAPIRegistry } from '@asteasolutions/zod-to-openapi'
import { globalSchemas } from '../global'
import { logger } from './logger'

export function generateParameterObject(registry: OpenAPIRegistry, refId: string) {
  return {

  }
}

/**
 * 生成引用对象
 * @param refId
 * @param isArray
 * @returns
 */
export function generateReferenceObject(
  registry: OpenAPIRegistry,
  refId: string,
): string {
  // 检查是否为全局 schema
  const globalSchema = globalSchemas[refId]
  if (globalSchema) {
    return refId
  }

  const isArray = refId.startsWith('List_')
  const rRefId = `R_${refId}_`
  if (isArray) {
    // 正则匹配List_(UserInfo)_ --> childRefId
    const childRefId = refId.match(/List_(.*)_/)?.[1]
    if (!childRefId) {
      logger.warn(`⚠️ 无法为 ${refId} 生成引用对象 - 无法获取子引用对象refId`)
      return rRefId
    }
    registry.registerComponent('schemas', rRefId, {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          description: '数据载荷',
          items: {
            $ref: refLink({ refId: childRefId }),
          },
        },
        resultCode: {
          type: 'number',
          example: 200,
        },
        resultMessage: {
          type: 'string',
          example: 'success',
        },
      },
      required: ['resultCode', 'resultMessage', 'data'],

    })
  } else {
    registry.registerComponent('schemas', rRefId, {
      type: 'object',
      properties: {
        data: {
          $ref: refLink({ refId }),
        },
        resultCode: {
          type: 'number',
          example: 200,
        },
        resultMessage: {
          type: 'string',
          example: 'success',
        },
      },
      required: ['resultCode', 'resultMessage', 'data'],
    })
  }
  return rRefId
}

export function refLink(options?: { type?: string, refId?: string }) {
  const { type, refId } = Object.assign({ type: 'schemas', refId: '' }, options)
  return `#/components/${type}/${refId}`
}
