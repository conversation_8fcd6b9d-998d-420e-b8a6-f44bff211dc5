/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-10 10:10:52
 * @LastEditTime: 2025-07-10 10:54:21
 * @LastEditors: shaojun
 * @Description:
 */
import type { ConsolaInstance } from 'consola'
/*
 * @Author: s<PERSON>jun
 * @Date: 2025-03-12 10:56:34
 * @LastEditTime: 2025-03-12 11:01:57
 * @LastEditors: shaojun
 * @Description: 终端输出工具函数封装
 *
 * // ESM
  * import { consola, createConsola } from "consola";
  *
  * // CommonJS
  * const { consola, createConsola } = require("consola");
  *
  * consola.info("Using consola 3.0.0");
  * consola.start("Building project...");
  * consola.warn("A new version of consola is available: 3.0.1");
  * consola.success("Project built!");
  * consola.error(new Error("This is an example error. Everything is fine!"));
  * consola.box("I am a simple box");
  * await consola.prompt("Deploy to the production?", {
  *   type: "confirm",
  * });
 */
import { consola } from 'consola'

// 创建一个独立的 consola 实例
export const logger: ConsolaInstance = consola.create({
  level: 5, // debug 级别
  formatOptions: {
    date: false,
    colors: true,
  },
})

/**
 * 输出普通日志
 * @param message 消息内容
 * @param args 额外参数
 */
export function log(message: any, ...args: any[]) {
  logger.log(message, ...args)
}

/**
 * 输出成功信息
 * @param message 消息内容
 * @param args 额外参数
 */
export function success(message: any, ...args: any[]) {
  logger.success(message, ...args)
}

/**
 * 输出警告信息
 * @param message 消息内容
 * @param args 额外参数
 */
export function warn(message: any, ...args: any[]) {
  logger.warn(message, ...args)
}

/**
 * 输出错误信息
 * @param message 消息内容
 * @param args 额外参数
 */
export function error(message: any, ...args: any[]) {
  logger.error(message, ...args)
}

/**
 * 输出信息性消息
 * @param message 消息内容
 * @param args 额外参数
 */
export function info(message: any, ...args: any[]) {
  logger.info(message, ...args)
}

/**
 * 输出盒子信息
 * @param message 消息内容
 * @param args 额外参数
 */
export function box(message: any, ...args: any[]) {
  logger.box(message, ...args)
}

/**
 * 开始一个新的任务
 * @param message 任务描述
 * @param args 额外参数
 */
export function start(message: any, ...args: any[]) {
  logger.start(message, ...args)
}

/**
 * 输出调试信息(仅在调试级别可见)
 * @param message 消息内容
 * @param args 额外参数
 */
export function debug(message: any, ...args: any[]) {
  logger.debug(message, ...args)
}

/**
 * 输出详细信息(仅在详细级别可见)
 * @param message 消息内容
 * @param args 额外参数
 */
export function verbose(message: any, ...args: any[]) {
  logger.verbose(message, ...args)
}

/**
 * 输出准备信息
 * @param message 消息内容
 * @param args 额外参数
 */
export function ready(message: any, ...args: any[]) {
  logger.ready(message, ...args)
}

/**
 * 输出完成信息
 * @param message 消息内容
 * @param args 额外参数
 */
export function done(message: any, ...args: any[]) {
  logger.success(`✨ ${message}`, ...args)
}
