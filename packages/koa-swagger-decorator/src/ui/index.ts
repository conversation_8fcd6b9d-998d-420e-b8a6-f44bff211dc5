import type { Middleware } from 'koa'
import type { KoaSwaggerUiOptions } from 'koa2-swagger-ui'
import type { SwaggerUIConfig } from '../types'
import { koaSwagger } from 'koa2-swagger-ui'
import { logger } from '../utils/logger'

/**
 * 创建 Swagger UI 中间件
 * @param config Swagger UI 配置
 * @param jsonEndpoint JSON 文档端点路径
 * @returns Koa 中间件
 */
export function createSwaggerUI(
  config: SwaggerUIConfig = {},
  jsonEndpoint: string = '/swagger.json',
): Middleware {
  logger.debug('创建 Swagger UI 中间件', { config, jsonEndpoint })

  // 默认的 SwaggerUI 配置
  const DEFAULT_SWAGGER_UI_CONFIG: SwaggerUIConfig = {
    routePrefix: '/swagger',
    title: 'Swagger API',
    hideTopbar: false,
    swaggerOptions: {
      docExpansion: 'list',
      persistAuthorization: true,
      supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'],
      defaultModelRendering: 'schema',
    },
  }

  // 合并配置
  const mergedConfig = {
    ...DEFAULT_SWAGGER_UI_CONFIG,
    ...config,
    swaggerOptions: {
      ...DEFAULT_SWAGGER_UI_CONFIG.swaggerOptions,
      ...config?.swaggerOptions,
      url: config?.swaggerOptions?.url || jsonEndpoint,
    },
  }

  logger.info(`🎨 创建 Swagger UI: ${mergedConfig.routePrefix}`)
  logger.info(`   JSON源: ${mergedConfig.swaggerOptions?.url}`)
  logger.info(`   标题: ${mergedConfig.title}`)

  try {
    return koaSwagger(mergedConfig)
  } catch (error) {
    logger.error('❌ 创建 Swagger UI 失败:', error)
    throw error
  }
}

/**
 * 创建自定义配置的 Swagger UI
 * @param options 完整的 koa2-swagger-ui 配置
 * @returns Koa 中间件
 */
export function createCustomSwaggerUI(options: Partial<KoaSwaggerUiOptions>): Middleware {
  logger.debug('创建自定义 Swagger UI 中间件', options)

  try {
    return koaSwagger(options)
  } catch (error) {
    logger.error('❌ 创建自定义 Swagger UI 失败:', error)
    throw error
  }
}
