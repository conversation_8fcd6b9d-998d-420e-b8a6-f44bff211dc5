import { OpenAPIRegistry } from '@asteasolutions/zod-to-openapi'

export const createRegistry = () => {
  return new OpenAPIRegistry()
}

export const registry = createRegistry()

export const useRegistry = () => {
  let _registry: OpenAPIRegistry | undefined

  const createRegistry = () => {
    return new OpenAPIRegistry()
  }
  const resetRegistry = () => {
    _registry = undefined
    _registry = createRegistry()
  }
  const getRegistry = () => {
    if (!_registry) {
      _registry = createRegistry()
    }
    return _registry
  }

  return { _registry, getRegistry, createRegistry, resetRegistry }
}
