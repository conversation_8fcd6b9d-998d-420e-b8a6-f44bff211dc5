/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-16 16:45:53
 * @LastEditTime: 2025-07-17 13:11:51
 * @LastEditors: shaojun
 * @Description: Summary 和 Description 装饰器实现
 */
import { METADATA_KEYS } from '../types'

/**
 * @Summary 装饰器 - 方法级别的摘要定义
 * @param summary 摘要文本
 * @returns MethodDecorator
 * @example
 * @Summary('用户登录')
 */
export const Summary = (summary: string): MethodDecorator => {
  if (!summary || typeof summary !== 'string') {
    throw new Error('Summary must be a non-empty string')
  }

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(METADATA_KEYS.SUMMARY, summary, target, propertyKey)
    return descriptor
  }
}

/**
 * @Description 装饰器 - 方法级别的描述定义
 * @param description 描述文本
 * @returns MethodDecorator
 * @example
 * @Description('用户登录接口，支持用户名密码登录')
 */
export const Description = (description: string): MethodDecorator => {
  if (!description || typeof description !== 'string') {
    throw new Error('Description must be a non-empty string')
  }

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(METADATA_KEYS.DESCRIPTION, description, target, propertyKey)
    return descriptor
  }
}

/**
 * @Tags 装饰器 - 方法级别的标签定义
 * @param tags 标签数组或单个标签字符串
 * @returns MethodDecorator
 * @example
 * @Tags(['用户管理', 'API'])
 * @Tags('用户管理')
 */
export const Tags = (tags: string[] | string): MethodDecorator => {
  if (!tags) {
    throw new Error('Tags requires tags parameter')
  }

  // 确保 tags 是数组格式
  const tagsArray = Array.isArray(tags) ? tags : [tags]

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(METADATA_KEYS.TAGS, tagsArray, target, propertyKey)
    return descriptor
  }
}

/**
 * 获取方法的 summary 元数据
 */
export function getSummaryMetadata(target: any, methodName: string): string | undefined {
  return Reflect.getMetadata(METADATA_KEYS.SUMMARY, target, methodName)
}

/**
 * 获取方法的 description 元数据
 */
export function getDescriptionMetadata(target: any, methodName: string): string | undefined {
  return Reflect.getMetadata(METADATA_KEYS.DESCRIPTION, target, methodName)
}

/**
 * 获取方法的 tags 元数据
 */
export function getTagsMetadata(target: any, methodName: string): string[] | undefined {
  return Reflect.getMetadata(METADATA_KEYS.TAGS, target, methodName)
}
