/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-07-17 15:25:03
 * @LastEditTime: 2025-07-17 15:25:04
 * @LastEditors: shaojun
 * @Description:
 */
// import type { RouteConfig as ZodRouteConfig } from '@asteasolutions/zod-to-openapi'
// /*
//  * @Author: shaojun
//  * @Date: 2025-07-16 16:46:39
//  * @LastEditTime: 2025-07-16 16:46:39
//  * @LastEditors: shaojun
//  * @Description: 装饰器系统类型定义
//  */
// import type {
//   ComponentsObject,
//   ParameterObject,
//   ReferenceObject,
//   ResponseObject,
//   SchemaObject,
// } from 'openapi3-ts/oas30'
// import type { ZodObject, ZodTypeAny } from 'zod'

// // HTTP 方法类型
// export type HttpMethod = 'get' | 'post' | 'put' | 'delete' | 'patch' | 'options' | 'head'

// // 装饰器类型
// export type DecoratorType = 'class' | 'method' | 'property' | 'parameter'

// // 控制器配置接口
// export interface ControllerConfig {
//   paths?: {
//     parameters?: (ParameterObject | ReferenceObject)[]
//   }
//   components?: ComponentsObject
//   tags?: string[]
// }

// // 路由配置接口
// export interface RouteConfigDecorator {
//   [key: string]: any
//   className?: string
//   methodName?: string
//   identifier?: string
// }

// export type StrongRouteConfig = ZodRouteConfig & RouteConfigDecorator

// // 参数配置接口
// export interface ParameterConfig {
//   [name: string]: Omit<ParameterObject, 'name' | 'in'>
// }

// // Header 参数配置接口
// export interface HeaderConfig extends ParameterConfig {}

// // 响应配置接口
// export interface ResponsesConfig {
//   [statusCode: string]: {
//     description: string
//     schema?: ZodTypeAny | { $ref: string }
//   }
// }

// // 中间件函数类型
// export type MiddlewareFunction = (ctx: any, next: () => Promise<void>) => Promise<void>

// // 装饰器元数据类型映射
// export interface DecoratorMetadataMap {
//   'swagger:controller': ControllerConfig
//   'swagger:routeConfig': StrongRouteConfig
//   'swagger:request': { method: HttpMethod, path: string, operationId: string }
//   'swagger:body': ZodObject<any>
//   'swagger:responses': ResponsesConfig
//   'swagger:header': ParameterObject[]
//   'swagger:query': ParameterObject[]
//   'swagger:path': ParameterObject[]
//   'swagger:middlewares': MiddlewareFunction[]
//   'swagger:summary': string
//   'swagger:description': string
//   'swagger:tags': string[]
//   'swagger:tagsAll': string[]
//   'swagger:headerAll': { parameters: ParameterObject[], filters: string[] }
//   'swagger:queryAll': { parameters: ParameterObject[], filters: string[] }
//   'swagger:responsesAll': { responses: ResponsesConfig, filters: string[] }
// }

// // 元数据键类型
// export type MetadataKey = keyof DecoratorMetadataMap

// // 泛型元数据获取函数类型
// export type GetMetadata = <K extends MetadataKey>(
//   key: K,
//   target: any,
//   propertyKey?: string | symbol
// ) => DecoratorMetadataMap[K] | undefined

// // 泛型元数据设置函数类型
// export type SetMetadata = <K extends MetadataKey>(
//   key: K,
//   value: DecoratorMetadataMap[K],
//   target: any,
//   propertyKey?: string | symbol
// ) => void

// // 装饰器工厂函数返回类型
// export type DecoratorFactory<T extends DecoratorType> = T extends 'class'
//   ? ClassDecorator
//   : T extends 'method'
//     ? MethodDecorator
//     : T extends 'property'
//       ? PropertyDecorator
//       : T extends 'parameter'
//         ? ParameterDecorator
//         : never

// // 装饰器处理函数类型
// export type DecoratorHandler<T extends DecoratorType> = T extends 'class'
//   ? (target: any) => any
//   : T extends 'method'
//     ? (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => any
//     : T extends 'property'
//       ? (target: any, propertyKey: string | symbol) => any
//       : T extends 'parameter'
//         ? (target: any, propertyKey: string | symbol, parameterIndex: number) => any
//         : never

// // 装饰器配置选项
// export interface DecoratorOptions<T extends DecoratorType = DecoratorType> {
//   type: T
//   metadata?: Record<string, any>
//   handler?: DecoratorHandler<T>
//   enableLogging?: boolean
//   metadataKeyPrefix?: string
// }

// // 合并策略类型
// export type MergeStrategy = 'replace' | 'append' | 'merge'

// // 缓存统计信息
// export interface CacheStats {
//   size: number
//   enabled: boolean
// }

// // 方法信息接口
// export interface MethodInfo {
//   name: string
//   metadata: Record<string, any>
// }

// // 类信息接口
// export interface ClassInfo {
//   name: string
//   metadata: Record<string, any>
//   methods: MethodInfo[]
// }

// // Swagger 文档构建选项
// export interface SwaggerBuildOptions {
//   prefix?: string
//   globals?: Record<string, ZodTypeAny>
//   spec?: any
//   enableCache?: boolean
// }

// // 装饰器验证选项
// export interface ValidationOptions {
//   validateRequest?: boolean
//   validateResponse?: boolean
//   strict?: boolean
// }

// // 错误处理选项
// export interface ErrorHandlingOptions {
//   throwOnError?: boolean
//   logErrors?: boolean
//   customErrorHandler?: (error: Error, context: any) => void
// }

// // 装饰器系统配置
// export interface DecoratorSystemConfig {
//   validation?: ValidationOptions
//   errorHandling?: ErrorHandlingOptions
//   cache?: {
//     enabled?: boolean
//     maxSize?: number
//   }
//   logging?: {
//     enabled?: boolean
//     level?: 'debug' | 'info' | 'warn' | 'error'
//   }
// }

// // 工具类型：提取装饰器参数类型
// export type ExtractDecoratorParams<T> = T extends (param: infer P) => any ? P : never

// // 工具类型：条件装饰器类型
// export type ConditionalDecorator<T, U> = T extends true ? U : never

// // 工具类型：装饰器组合类型
// export type DecoratorComposition<T extends readonly DecoratorType[]> = {
//   [K in keyof T]: DecoratorFactory<T[K]>
// }

// // 导出所有类型
// export type {
//   ComponentsObject,
//   ParameterObject,
//   ReferenceObject,
//   ResponseObject,
//   SchemaObject,
//   ZodObject,
//   ZodRouteConfig,
//   ZodTypeAny,
// }
