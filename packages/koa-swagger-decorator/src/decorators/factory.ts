import type { DecoratorOptions, DecoratorType } from '../types'
import { logger } from '../utils/logger'
import 'reflect-metadata'

/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-12-23 10:06:23
 * @LastEditTime: 2025-07-17 12:49:02
 * @LastEditors: shaojun
 * @Description: 装饰器工厂函数
 *
 * 提供两种装饰器创建方式：
 * 1. createDecorator - 通用装饰器工厂，支持复杂的处理逻辑和自定义元数据存储
 *    适用场景：测试、第三方扩展、需要复杂处理逻辑的装饰器
 *
 * 2. createSwaggerDecorator - 专用于 Swagger 元数据存储的简化装饰器工厂
 *    适用场景：简单的 Swagger 元数据存储，性能优先
 *
 * 3. 直接实现 - 最简单的装饰器实现方式，直接使用 Reflect.defineMetadata
 *    适用场景：大部分 Swagger 装饰器，性能最优
 */

// 检查装饰器是否是类装饰器、方法装饰器、属性装饰器或参数装饰器
export const isDecorator = (
  decorator: any,
): decorator is
| ClassDecorator
| MethodDecorator
| PropertyDecorator
| ParameterDecorator => {
  // 根据装饰器类型判断
  return typeof decorator === 'function'
}

export const isClassDecorator: (
  type: DecoratorType,
  decorator: any
) => decorator is ClassDecorator = (
  type,
  decorator,
): decorator is ClassDecorator =>
  type === 'class' && typeof decorator === 'function'

export const isMethodDecorator: (
  type: DecoratorType,
  decorator: any
) => decorator is MethodDecorator = (
  type,
  decorator,
): decorator is MethodDecorator =>
  type === 'method' && typeof decorator === 'function'

export const isPropertyDecorator: (
  type: DecoratorType,
  decorator: any
) => decorator is PropertyDecorator = (
  type,
  decorator,
): decorator is PropertyDecorator =>
  type === 'property' && typeof decorator === 'function'

export const isParameterDecorator: (
  type: DecoratorType,
  decorator: any
) => decorator is ParameterDecorator = (
  type,
  decorator,
): decorator is ParameterDecorator =>
  type === 'parameter' && typeof decorator === 'function'

/**
 * 优化的装饰器工厂函数
 * @param options 装饰器配置选项
 * @returns 根据类型返回对应的装饰器函数
 */
export function createDecorator({
  type,
  metadata,
  handler,
  enableLogging = true,
  metadataKeyPrefix = 'custom',
  disableBuiltinMetadata = false,
}: DecoratorOptions):
  | ClassDecorator
  | MethodDecorator
  | PropertyDecorator
  | ParameterDecorator {
  // 优化的元数据处理函数
  const defineCustomMetadata = (target: any, propertyKey?: string | symbol) => {
    // 如果禁用内置元数据存储，则跳过
    if (disableBuiltinMetadata || !metadata) {
      return
    }

    const metadataKey = propertyKey
      ? `${metadataKeyPrefix}:metadata`
      : `${metadataKeyPrefix}:class:metadata`
    const metadataTarget = propertyKey ? target : target.prototype

    Reflect.defineMetadata(
      metadataKey,
      metadata,
      metadataTarget,
      propertyKey ? String(propertyKey) : '',
    )
  }

  // 优化的日志处理函数
  const logDecoratorInfo = (decoratorType: string, target: any, propertyKey?: string | symbol, logMetadata?: any) => {
    if (!enableLogging) {
      return
    }

    const targetName = target?.constructor?.name || target?.name || 'Unknown'
    const methodName = propertyKey ? `.${String(propertyKey)}` : ''

    // 优化metadata输出格式
    let metaInfo = ''
    if (logMetadata) {
      if (logMetadata.typeName === 'ZodObject') {
        metaInfo = 'ZodObject'
      } else if (logMetadata.method && logMetadata.path) {
        metaInfo = `${logMetadata.method.toUpperCase()} ${logMetadata.path}`
      } else if (logMetadata.tags && Array.isArray(logMetadata.tags)) {
        metaInfo = `tags: [${logMetadata.tags.join(', ')}]`
      } else if (typeof logMetadata === 'string') {
        metaInfo = logMetadata
      } else if (typeof logMetadata === 'object') {
        // 简化对象输出
        const keys = Object.keys(logMetadata).slice(0, 3)
        metaInfo = keys.length > 0 ? `{${keys.join(', ')}}` : 'Config'
      } else {
        metaInfo = 'Config'
      }
    }

    logger.debug(`🔧 ${decoratorType}: ${targetName}${methodName} → ${metaInfo}`)
  }

  // 根据装饰器类型创建对应的装饰器函数
  switch (type) {
    case 'class':
      return ((target: { new(...args: any[]): any }) => {
        logDecoratorInfo('Class', target, undefined, metadata)
        defineCustomMetadata(target)
        return handler?.(target) ?? target
      }) as ClassDecorator

    case 'method':
      return ((
        target: object,
        propertyKey: string | symbol,
        descriptor: PropertyDescriptor,
      ) => {
        logDecoratorInfo('Method', target, propertyKey, metadata)
        defineCustomMetadata(target, propertyKey)
        return handler?.(target, propertyKey, descriptor) ?? descriptor
      }) as MethodDecorator

    case 'property':
      return ((target: object, propertyKey: string | symbol) => {
        logDecoratorInfo('Property', target, propertyKey, metadata)
        defineCustomMetadata(target, propertyKey)
        return handler?.(target, propertyKey)
      }) as PropertyDecorator

    case 'parameter':
      return ((
        target: object,
        propertyKey: string | symbol,
        parameterIndex: number,
      ) => {
        logDecoratorInfo('Parameter', target, propertyKey, `param[${parameterIndex}]`)
        defineCustomMetadata(
          target,
          propertyKey
            ? `${String(propertyKey)}:${parameterIndex}`
            : `${parameterIndex}`,
        )
        return handler?.(target, propertyKey, parameterIndex)
      }) as ParameterDecorator

    default:
      throw new Error(`不支持的装饰器类型: ${type}`)
  }
}

/**
 * 创建 Swagger 装饰器的简化函数
 * 专门用于 swagger 元数据存储，避免重复存储问题
 */
export function createSwaggerDecorator<T extends DecoratorType>(
  type: T,
  metadataKey: string,
  metadataValue: any,
  enableLogging = true,
): DecoratorFactory<T> {
  const logDecoratorInfo = (decoratorType: string, target: any, propertyKey?: string | symbol) => {
    if (!enableLogging) {
      return
    }

    const targetName = target?.constructor?.name || target?.name || 'Unknown'
    const methodName = propertyKey ? `.${String(propertyKey)}` : ''

    // 简化的日志输出
    let metaInfo = ''
    if (metadataValue) {
      if (typeof metadataValue === 'string') {
        metaInfo = metadataValue
      } else if (metadataValue.method && metadataValue.path) {
        metaInfo = `${metadataValue.method.toUpperCase()} ${metadataValue.path}`
      } else if (Array.isArray(metadataValue)) {
        metaInfo = `[${metadataValue.length} items]`
      } else if (typeof metadataValue === 'object') {
        const keys = Object.keys(metadataValue).slice(0, 2)
        metaInfo = keys.length > 0 ? `{${keys.join(', ')}}` : 'Config'
      }
    }

    logger.debug(`🔧 ${decoratorType}: ${targetName}${methodName} → ${metaInfo}`)
  }

  switch (type) {
    case 'class':
      return ((target: any) => {
        logDecoratorInfo('Class', target)
        Reflect.defineMetadata(metadataKey, metadataValue, target)
        return target
      }) as DecoratorFactory<T>

    case 'method':
      return ((target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
        logDecoratorInfo('Method', target, propertyKey)
        Reflect.defineMetadata(metadataKey, metadataValue, target, propertyKey)
        return descriptor
      }) as DecoratorFactory<T>

    case 'property':
      return ((target: any, propertyKey: string | symbol) => {
        logDecoratorInfo('Property', target, propertyKey)
        Reflect.defineMetadata(metadataKey, metadataValue, target, propertyKey)
      }) as DecoratorFactory<T>

    case 'parameter':
      return ((target: any, propertyKey: string | symbol, parameterIndex: number) => {
        logDecoratorInfo('Parameter', target, propertyKey)
        const key = `${String(propertyKey)}:${parameterIndex}`
        Reflect.defineMetadata(metadataKey, metadataValue, target, key)
      }) as DecoratorFactory<T>

    default:
      throw new Error(`不支持的装饰器类型: ${type}`)
  }
}

// 类型定义
type DecoratorFactory<T extends DecoratorType> = T extends 'class'
  ? ClassDecorator
  : T extends 'method'
    ? MethodDecorator
    : T extends 'property'
      ? PropertyDecorator
      : T extends 'parameter'
        ? ParameterDecorator
        : never
