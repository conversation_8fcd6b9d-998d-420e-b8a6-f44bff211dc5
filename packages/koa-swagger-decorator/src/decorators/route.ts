import type { StrongRouteConfig } from '../types'
import { ConfigValidators, ErrorHandler, RouteConfigError } from '../errors'
import { METADATA_KEYS } from '../types'

/*
 * @Author: shaojun
 * @Date: 2024-12-25 11:32:57
 * @LastEditTime: 2025-07-17 13:05:47
 * @LastEditors: shaojun
 * @Description: 路由配置
 */
export const RouteConfig = (r: Partial<StrongRouteConfig>): MethodDecorator => {
  // 使用统一的错误处理和参数验证
  ErrorHandler.validateDecoratorConfig('RouteConfig', r, ConfigValidators.validateRouteConfig)

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    try {
      const methodName = String(propertyKey)

      // 设置默认 operationId
      if (!r.operationId) {
        r.operationId = methodName
      }

      // 使用安全的元数据存储
      ErrorHandler.safeDefineMetadata(METADATA_KEYS.ROUTE_CONFIG, r, target, propertyKey)

      return descriptor
    } catch (error) {
      // 处理元数据存储错误
      throw new RouteConfigError(
        String(propertyKey),
        `Failed to apply RouteConfig decorator: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { target: target.constructor?.name, method: r.method, path: r.path },
      )
    }
  }
}
