/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-16 16:46:39
 * @LastEditTime: 2025-07-17 13:04:47
 * @LastEditors: shaojun
 * @Description: 参数装饰器实现 - Query, Path, Param 等
 */
import type { ParameterObject } from 'openapi3-ts/oas30'
import type { ParameterConfig } from '../types'
import { METADATA_KEYS } from '../types'

// 合并参数的辅助函数
function mergeParameters(existing: ParameterObject[], newParams: ParameterObject[]): ParameterObject[] {
  const merged = [...existing]
  newParams.forEach((param) => {
    const existingIndex = merged.findIndex((existing) => existing.name === param.name)
    if (existingIndex >= 0) {
      // 覆盖已存在的参数
      merged[existingIndex] = param
    } else {
      // 添加新参数
      merged.push(param)
    }
  })
  return merged
}

/**
 * @Query 装饰器 - 查询参数定义
 * @param queries 查询参数配置对象
 * @returns MethodDecorator
 * @example
 * @Query({
 *   'page': {
 *     required: false,
 *     schema: { type: 'integer', minimum: 1 },
 *     description: '页码'
 *   },
 *   'size': {
 *     required: false,
 *     schema: { type: 'integer', minimum: 1, maximum: 100 },
 *     description: '每页大小'
 *   }
 * })
 */
export const Query = (queries: ParameterConfig): MethodDecorator => {
  if (!queries || typeof queries !== 'object') {
    throw new Error('Query config must be an object')
  }

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    // 转换为 OpenAPI 参数格式
    const parameters: ParameterObject[] = Object.keys(queries).map((name) => ({
      name,
      in: 'query',
      ...queries[name],
    }))

    // 获取现有的 query 参数，支持多次使用装饰器
    const existingQueries = Reflect.getMetadata(METADATA_KEYS.QUERY, target, propertyKey) || []
    const mergedQueries = mergeParameters(existingQueries, parameters)

    Reflect.defineMetadata(METADATA_KEYS.QUERY, mergedQueries, target, propertyKey)
    return descriptor
  }
}

/**
 * @Path 装饰器 - 路径参数定义
 * @param paths 路径参数配置对象
 * @returns MethodDecorator
 * @example
 * @Path({
 *   'id': {
 *     required: true,
 *     schema: { type: 'string' },
 *     description: '用户ID'
 *   }
 * })
 */
export const Path = (paths: ParameterConfig): MethodDecorator => {
  if (!paths || typeof paths !== 'object') {
    throw new Error('Path config must be an object')
  }

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    // 转换为 OpenAPI 参数格式，路径参数必须是 required: true
    const parameters: ParameterObject[] = Object.keys(paths).map((name) => ({
      name,
      in: 'path',
      required: true, // 路径参数必须是必需的
      ...paths[name],
    }))

    // 获取现有的 path 参数，支持多次使用装饰器
    const existingPaths = Reflect.getMetadata(METADATA_KEYS.PATH, target, propertyKey) || []
    const mergedPaths = mergeParameters(existingPaths, parameters)

    Reflect.defineMetadata(METADATA_KEYS.PATH, mergedPaths, target, propertyKey)
    return descriptor
  }
}

/**
 * @QueryAll 装饰器 - 类级别的查询参数定义，应用到所有方法
 * @param queries 查询参数配置对象
 * @param filters 过滤器，指定应用到哪些方法，默认为 ['ALL']
 * @returns ClassDecorator
 */
export const QueryAll = (queries: ParameterConfig, filters: string[] = ['ALL']): ClassDecorator => {
  if (!queries || typeof queries !== 'object') {
    throw new Error('QueryAll config must be an object')
  }

  return (target: any) => {
    // 转换为 OpenAPI 参数格式
    const parameters: ParameterObject[] = Object.keys(queries).map((name) => ({
      name,
      in: 'query',
      ...queries[name],
    }))

    // 存储类级别的 query 配置
    Reflect.defineMetadata(METADATA_KEYS.QUERY_ALL, {
      parameters,
      filters,
    }, target)

    return target
  }
}

/**
 * 获取方法的 query 元数据
 */
export function getQueryMetadata(target: any, methodName: string): ParameterObject[] {
  return Reflect.getMetadata(METADATA_KEYS.QUERY, target, methodName) || []
}

/**
 * 获取方法的 path 元数据
 */
export function getPathMetadata(target: any, methodName: string): ParameterObject[] {
  return Reflect.getMetadata(METADATA_KEYS.PATH, target, methodName) || []
}

/**
 * 获取类的 queryAll 元数据
 */
export function getQueryAllMetadata(target: any): { parameters: ParameterObject[], filters: string[] } | undefined {
  return Reflect.getMetadata(METADATA_KEYS.QUERY_ALL, target)
}
