import type { ZodObject } from 'zod'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-12-25 13:03:59
 * @LastEditTime: 2025-07-17 16:16:39
 * @LastEditors: shaojun
 * @Description: Body 装饰器实现
 */
import { DecoratorConfigError, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SchemaValidationError } from '../errors'
import { isZodObject, METADATA_KEYS } from '../types'

export const Body = (v: ZodObject<any>): MethodDecorator => {
  // 验证参数
  if (!v) {
    throw new DecoratorConfigError('Body', 'Schema is required', { received: v })
  }

  // 验证是否为 ZodObject
  if (!isZodObject(v)) {
    throw new SchemaValidationError(
      'Body',
      'Schema must be a Zod object schema',
      { received: v, type: typeof v },
    )
  }

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    try {
      // 使用安全的元数据存储
      ErrorHandler.safeDefineMetadata(METADATA_KEYS.BODY, v, target, propertyKey)
      return descriptor
    } catch (error) {
      throw new DecoratorConfigError(
        'Body',
        `Failed to apply decorator to ${target.constructor?.name || 'Unknown'}.${String(propertyKey)}`,
        { target: target.constructor?.name, method: String(propertyKey) },
      )
    }
  }
}
