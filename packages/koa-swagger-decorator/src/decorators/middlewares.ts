import type { MiddlewareFunction } from '../types'
/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-25 13:08:05
 * @LastEditTime: 2025-07-17 13:11:33
 * @LastEditors: shaojun
 * @Description: 中间件装饰器实现
 */
import { DecoratorConfigError, ErrorHandler } from '../errors'
import { METADATA_KEYS, TypeGuards } from '../types'

export const Middlewares = (middleware: MiddlewareFunction | MiddlewareFunction[]): MethodDecorator => {
  // 验证参数
  if (!middleware) {
    throw new DecoratorConfigError('Middlewares', 'Middleware is required', { received: middleware })
  }

  // 确保中间件是函数或函数数组
  const middlewareArray = Array.isArray(middleware) ? middleware : [middleware]

  // 验证每个中间件都是函数
  middlewareArray.forEach((mw, index) => {
    if (typeof mw !== 'function') {
      throw new DecoratorConfigError(
        'Middlewares',
        `Middleware at index ${index} must be a function`,
        { index, received: typeof mw },
      )
    }
  })

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    try {
      // 获取或初始化middlewares数组
      let existingMiddlewares = Reflect.getMetadata(METADATA_KEYS.MIDDLEWARES, target, propertyKey) || []

      // 添加新的中间件
      const updatedMiddlewares = [...existingMiddlewares, ...middlewareArray]

      // 使用安全的元数据存储
      ErrorHandler.safeDefineMetadata(METADATA_KEYS.MIDDLEWARES, updatedMiddlewares, target, propertyKey)

      return descriptor
    } catch (error) {
      throw new DecoratorConfigError(
        'Middlewares',
        `Failed to apply decorator to ${target.constructor?.name || 'Unknown'}.${String(propertyKey)}`,
        { target: target.constructor?.name, method: String(propertyKey) },
      )
    }
  }
}
