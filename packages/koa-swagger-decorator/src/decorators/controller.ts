/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-25 09:12:43
 * @LastEditTime: 2025-07-17 10:46:50
 * @LastEditors: shao<PERSON> <EMAIL>
 * @Description: Controller
 * 职责：控制器装饰器
 */

import type { ControllerConfig } from '../types'
import { ConfigValidators, DecoratorConfigError, ErrorHandler, SwaggerErrorType } from '../errors'
import { METADATA_KEYS } from '../types'
import { getMetadata } from './metadata-manager'
/**
 * 装饰器函数 @Controller
 * @param v ControllerConfig 控制器配置
 * @returns ClassDecorator 类装饰器
 * @description 控制器配置
 */
export const Controller = (c: ControllerConfig): ClassDecorator => {
  // 使用统一的错误处理和参数验证
  ErrorHandler.validateDecoratorConfig('Controller', c, ConfigValidators.validateControllerConfig)

  return (target: any) => {
    try {
      // 使用安全的元数据存储
      ErrorHandler.safeDefineMetadata(METADATA_KEYS.CONTROLLER, c, target)
      return target
    } catch (error) {
      // 处理元数据存储错误
      throw new DecoratorConfigError(
        'Controller',
        `Failed to apply decorator to ${target.name || 'Unknown'}`,
        { target: target.name, config: c },
      )
    }
  }
}

/**
 * @TagsAll 装饰器 - 类级别的标签定义，应用到所有方法
 * @param tags 标签数组或单个标签字符串
 * @returns ClassDecorator
 * @example
 * @TagsAll(['用户管理', 'API'])
 * @TagsAll('用户管理')
 */
export const TagsAll = (tags: string[] | string): ClassDecorator => {
  if (!tags) {
    throw new DecoratorConfigError('TagsAll', 'Tags parameter is required', { received: tags })
  }

  // 确保 tags 是数组格式
  const tagsArray = Array.isArray(tags) ? tags : [tags]

  return (target: any) => {
    try {
      // 使用安全的元数据存储
      ErrorHandler.safeDefineMetadata(METADATA_KEYS.TAGS_ALL, tagsArray, target)
      return target
    } catch (error) {
      throw new DecoratorConfigError(
        'TagsAll',
        `Failed to apply decorator to ${target.name || 'Unknown'}`,
        { target: target.name, tags: tagsArray },
      )
    }
  }
}

/**
 * 获取控制器元数据
 */
export function getControllerMetadata(ControllerClass: any): ControllerConfig | undefined {
  return getMetadata(METADATA_KEYS.CONTROLLER, ControllerClass)
}

/**
 * 获取类的 tagsAll 元数据
 */
export function getTagsAllMetadata(target: any): string[] | undefined {
  return getMetadata(METADATA_KEYS.TAGS_ALL, target)
}
