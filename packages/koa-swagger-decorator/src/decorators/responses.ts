import type { SchemaObject } from 'openapi3-ts/oas30'
import type { ZodObject, ZodTypeAny } from 'zod'
import type { ResponsesConfig } from '../types'
/*
 * @Author: shaojun
 * @Date: 2024-12-25 13:06:49
 * @LastEditTime: 2025-07-17 13:10:33
 * @LastEditors: shaojun
 * @Description: 响应装饰器实现
 */
import { DecoratorConfigError, ErrorHandler } from '../errors'
import { isSchemaObject, isZodType, METADATA_KEYS } from '../types'

/**
 * @Responses 装饰器 - 方法级别的响应定义
 * @param responses 响应配置对象、Zod Schema、全局 Schema 引用字符串或 SchemaObject
 * @returns MethodDecorator
 * @example
 * // 使用 Zod Schema（默认 200 响应）
 * @Responses(UserSchema)
 * @Responses(z.array(UserSchema))
 *
 * // 使用全局 Schema 引用
 * @Responses('UserInfo')
 * @Responses(GlobalSchemasKeyEnum.UserInfo)
 *
 * // 使用 SchemaObject（旧格式兼容）
 * @Responses(DetailInfoSchema)
 *
 * // 使用多状态码响应配置
 * @Responses({
 *   200: { description: 'Success', schema: UserSchema },
 *   400: { description: 'Bad Request' },
 *   404: { description: 'Not Found' }
 * })
 */
export const Responses = (responses: ZodTypeAny | ResponsesConfig | string | SchemaObject): MethodDecorator => {
  // 验证参数
  if (!responses) {
    throw new DecoratorConfigError('Responses', 'Responses configuration is required', { received: responses })
  }

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    try {
      // 处理不同类型的响应参数
      let responseConfig: ResponsesConfig | string | SchemaObject | ZodTypeAny

      if (typeof responses === 'string') {
        // 这是一个全局 Schema 引用字符串
        responseConfig = responses
      } else if (isZodType(responses)) {
        // 这是一个 Zod Schema（可能是 ZodObject, ZodArray, 等等）
        responseConfig = {
          200: {
            description: 'Success',
            schema: responses,
          },
        }
      } else if (isSchemaObject(responses)) {
        // 这是一个 SchemaObject（旧格式，直接传递给 builder 处理）
        responseConfig = responses
      } else {
        // 这是一个响应配置对象，直接使用
        responseConfig = responses as ResponsesConfig
      }

      // 使用安全的元数据存储
      ErrorHandler.safeDefineMetadata(METADATA_KEYS.RESPONSES, responseConfig, target, propertyKey)
      return descriptor
    } catch (error) {
      if (error instanceof DecoratorConfigError) {
        throw error
      }
      throw new DecoratorConfigError(
        'Responses',
        `Failed to apply decorator to ${target.constructor?.name || 'Unknown'}.${String(propertyKey)}`,
        { target: target.constructor?.name, method: String(propertyKey), error: error instanceof Error ? error.message : String(error) },
      )
    }
  }
}

/**
 * @ResponsesAll 装饰器 - 类级别的响应定义，应用到所有方法
 * @param responses 响应配置对象
 * @param filters 过滤器，指定应用到哪些方法，默认为 ['ALL']
 * @returns ClassDecorator
 */
export const ResponsesAll = (responses: ResponsesConfig, filters: string[] = ['ALL']): ClassDecorator => {
  // 验证参数
  if (!responses || typeof responses !== 'object') {
    throw new DecoratorConfigError('ResponsesAll', 'Responses configuration is required', { received: responses })
  }

  return (target: any) => {
    try {
      const config = { responses, filters }

      // 使用安全的元数据存储
      ErrorHandler.safeDefineMetadata(METADATA_KEYS.RESPONSES_ALL, config, target)
      return target
    } catch (error) {
      throw new DecoratorConfigError(
        'ResponsesAll',
        `Failed to apply decorator to ${target.name || 'Unknown'}`,
        { target: target.name, config: { responses, filters } },
      )
    }
  }
}

/**
 * 获取方法的响应元数据
 */
export function getResponsesMetadata(target: any, methodName: string): ResponsesConfig | undefined {
  return Reflect.getMetadata(METADATA_KEYS.RESPONSES, target, methodName)
}

/**
 * 获取类的 responsesAll 元数据
 */
export function getResponsesAllMetadata(target: any): { responses: ResponsesConfig, filters: string[] } | undefined {
  return Reflect.getMetadata(METADATA_KEYS.RESPONSES_ALL, target)
}
