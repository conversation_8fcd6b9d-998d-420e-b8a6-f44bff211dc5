/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-16 16:46:39
 * @LastEditTime: 2025-07-17 13:11:24
 * @LastEditors: shaojun
 * @Description: Header 装饰器实现
 */
import type { ParameterObject } from 'openapi3-ts/oas30'
import type { HeaderConfig } from '../types'
import { METADATA_KEYS } from '../types'
/**
 * @Header 装饰器 - 方法级别的 header 参数定义
 * @param headers Header 参数配置对象
 * @returns MethodDecorator
 * @example
 * @Header({
 *   'Authorization': {
 *     required: true,
 *     schema: { type: 'string' },
 *     description: 'Bearer token'
 *   }
 * })
 */
export const Header = (headers: HeaderConfig): MethodDecorator => {
  if (!headers || typeof headers !== 'object') {
    throw new Error('Header config must be an object')
  }

  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    // 转换为 OpenAPI 参数格式
    const parameters: ParameterObject[] = Object.keys(headers).map((name) => {
      return {
        ...headers[name],
        name,
        in: 'header',
      }
    })

    // 获取现有的 header 参数，支持多次使用装饰器
    const existingHeaders = Reflect.getMetadata(METADATA_KEYS.HEADER, target, propertyKey) || []

    // 合并参数，避免重复
    const mergedHeaders = [...existingHeaders]
    parameters.forEach((param) => {
      const existingIndex = mergedHeaders.findIndex((existing) => existing.name === param.name)
      if (existingIndex >= 0) {
        // 覆盖已存在的参数
        mergedHeaders[existingIndex] = param
      } else {
        // 添加新参数
        mergedHeaders.push(param)
      }
    })

    Reflect.defineMetadata(METADATA_KEYS.HEADER, mergedHeaders, target, propertyKey)
    return descriptor
  }
}

/**
 * @HeaderAll 装饰器 - 类级别的 header 参数定义，应用到所有方法
 * @param headers Header 参数配置对象
 * @param filters 过滤器，指定应用到哪些方法，默认为 ['ALL']
 * @returns ClassDecorator
 * @example
 * @HeaderAll({
 *   'Authorization': {
 *     required: true,
 *     schema: { type: 'string' },
 *     description: 'Bearer token'
 *   }
 * })
 */
export const HeaderAll = (headers: HeaderConfig, filters: string[] = ['ALL']): ClassDecorator => {
  if (!headers || typeof headers !== 'object') {
    throw new Error('HeaderAll config must be an object')
  }

  return (target: any) => {
    // 转换为 OpenAPI 参数格式
    const parameters: ParameterObject[] = Object.keys(headers).map((name) => ({
      ...headers[name],
      name,
      in: 'header',
    }))

    // 存储类级别的 header 配置
    Reflect.defineMetadata(METADATA_KEYS.HEADER_ALL, {
      parameters,
      filters,
    }, target)

    return target
  }
}

/**
 * 获取方法的 header 元数据
 */
export function getHeaderMetadata(target: any, methodName: string): ParameterObject[] {
  return Reflect.getMetadata(METADATA_KEYS.HEADER, target, methodName) || []
}

/**
 * 获取类的 headerAll 元数据
 */
export function getHeaderAllMetadata(target: any): { parameters: ParameterObject[], filters: string[] } | undefined {
  return Reflect.getMetadata(METADATA_KEYS.HEADER_ALL, target)
}
