/**
 * @fileoverview Swagger 装饰器错误处理系统
 *
 * 提供统一的错误处理机制，包括：
 * - 基础错误类型定义
 * - 特定错误类型实现
 * - 错误处理工具函数
 * - 配置验证器
 *
 * <AUTHOR>
 * @since 2025-07-16
 */

import type { ControllerConfig, StrongRouteConfig } from '../types'
import 'reflect-metadata'

/**
 * Swagger 错误类型枚举
 *
 * 定义了所有可能的 Swagger 相关错误类型，用于错误分类和处理
 */
export enum SwaggerErrorType {
  /** 装饰器配置错误 */
  DECORATOR_CONFIG = 'DECORATOR_CONFIG',
  /** Schema 验证错误 */
  SCHEMA_VALIDATION = 'SCHEMA_VALIDATION',
  /** 路由配置错误 */
  ROUTE_CONFIG = 'ROUTE_CONFIG',
  /** 元数据操作错误 */
  METADATA = 'METADATA',
  /** 构建过程错误 */
  BUILD = 'BUILD',
}

/**
 * Swagger 基础错误类
 *
 * 所有 Swagger 相关错误的基类，提供：
 * - 统一的错误结构
 * - 错误类型标识
 * - 上下文信息存储
 * - 错误链支持
 *
 * @example
 * ```typescript
 * throw new SwaggerError(
 *   SwaggerErrorType.DECORATOR_CONFIG,
 *   'Invalid configuration',
 *   { config: userConfig }
 * )
 * ```
 */
export class SwaggerError extends Error {
  /** 错误类型标识 */
  public readonly type: SwaggerErrorType
  /** 错误上下文信息 */
  public readonly context?: Record<string, any>
  /** 原始错误对象 */
  public readonly cause?: Error

  /**
   * 创建 Swagger 错误实例
   *
   * @param type - 错误类型
   * @param message - 错误消息
   * @param context - 错误上下文信息
   * @param cause - 原始错误对象
   */
  constructor(
    type: SwaggerErrorType,
    message: string,
    context?: Record<string, any>,
    cause?: Error,
  ) {
    super(message)
    this.name = 'SwaggerError'
    this.type = type
    this.context = context
    this.cause = cause

    // 确保错误堆栈正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SwaggerError)
    }
  }

  /**
   * 获取完整的错误信息
   *
   * @returns 包含类型、消息、上下文的完整错误信息
   */
  getFullMessage(): string {
    const parts = [`[${this.type}] ${this.message}`]

    if (this.context) {
      parts.push(`Context: ${JSON.stringify(this.context, null, 2)}`)
    }

    if (this.cause) {
      parts.push(`Caused by: ${this.cause.message}`)
    }

    return parts.join('\n')
  }

  /**
   * 转换为 JSON 格式
   *
   * @returns JSON 格式的错误信息
   */
  toJSON() {
    return {
      name: this.name,
      type: this.type,
      message: this.message,
      context: this.context,
      cause: this.cause?.message,
      stack: this.stack,
    }
  }
}

/**
 * 装饰器配置错误
 *
 * 当装饰器接收到无效配置参数时抛出此错误
 *
 * @example
 * ```typescript
 * // 当传入 null 配置时
 * throw new DecoratorConfigError(
 *   'Controller',
 *   'Configuration is required',
 *   { received: null }
 * )
 * ```
 */
export class DecoratorConfigError extends SwaggerError {
  /** 装饰器名称 */
  public readonly decoratorName: string

  /**
   * 创建装饰器配置错误
   *
   * @param decoratorName - 装饰器名称
   * @param message - 错误消息
   * @param context - 错误上下文
   * @param cause - 原始错误
   */
  constructor(
    decoratorName: string,
    message: string,
    context?: Record<string, any>,
    cause?: Error,
  ) {
    super(
      SwaggerErrorType.DECORATOR_CONFIG,
      `@${decoratorName}: ${message}`,
      { decoratorName, ...context },
      cause,
    )
    this.name = 'DecoratorConfigError'
    this.decoratorName = decoratorName
  }
}

/**
 * Schema 验证错误
 *
 * 当 Zod Schema 验证失败或 Schema 类型不正确时抛出此错误
 *
 * @example
 * ```typescript
 * throw new SchemaValidationError(
 *   'Body',
 *   'Schema must be a Zod object',
 *   { received: invalidSchema }
 * )
 * ```
 */
export class SchemaValidationError extends SwaggerError {
  /** 相关装饰器名称 */
  public readonly decoratorName: string

  /**
   * 创建 Schema 验证错误
   *
   * @param decoratorName - 相关装饰器名称
   * @param message - 错误消息
   * @param context - 错误上下文
   * @param cause - 原始错误
   */
  constructor(
    decoratorName: string,
    message: string,
    context?: Record<string, any>,
    cause?: Error,
  ) {
    super(
      SwaggerErrorType.SCHEMA_VALIDATION,
      `Schema ${decoratorName}: ${message}`,
      { decoratorName, ...context },
      cause,
    )
    this.name = 'SchemaValidationError'
    this.decoratorName = decoratorName
  }
}

/**
 * 路由配置错误
 *
 * 当路由配置无效或冲突时抛出此错误
 *
 * @example
 * ```typescript
 * throw new RouteConfigError(
 *   'login',
 *   'Duplicate route path',
 *   { method: 'POST', path: '/login' }
 * )
 * ```
 */
export class RouteConfigError extends SwaggerError {
  /** 路由方法名 */
  public readonly methodName: string

  /**
   * 创建路由配置错误
   *
   * @param methodName - 路由方法名
   * @param message - 错误消息
   * @param context - 错误上下文
   * @param cause - 原始错误
   */
  constructor(
    methodName: string,
    message: string,
    context?: Record<string, any>,
    cause?: Error,
  ) {
    super(
      SwaggerErrorType.ROUTE_CONFIG,
      `Route ${methodName}: ${message}`,
      { methodName, ...context },
      cause,
    )
    this.name = 'RouteConfigError'
    this.methodName = methodName
  }
}

/**
 * 元数据操作错误
 *
 * 当元数据读写操作失败时抛出此错误
 *
 * @example
 * ```typescript
 * throw new MetadataError(
 *   'Failed to read metadata',
 *   { key: 'swagger:controller', target: 'UserController' }
 * )
 * ```
 */
export class MetadataError extends SwaggerError {
  /**
   * 创建元数据操作错误
   *
   * @param message - 错误消息
   * @param context - 错误上下文
   * @param cause - 原始错误
   */
  constructor(
    message: string,
    context?: Record<string, any>,
    cause?: Error,
  ) {
    super(SwaggerErrorType.METADATA, message, context, cause)
    this.name = 'MetadataError'
  }
}

/**
 * 构建过程错误
 *
 * 当 Swagger 文档构建过程中发生错误时抛出此错误
 *
 * @example
 * ```typescript
 * throw new BuildError(
 *   'Failed to process controller',
 *   { controller: 'UserController', stage: 'route-processing' }
 * )
 * ```
 */
export class BuildError extends SwaggerError {
  /**
   * 创建构建过程错误
   *
   * @param message - 错误消息
   * @param context - 错误上下文
   * @param cause - 原始错误
   */
  constructor(
    message: string,
    context?: Record<string, any>,
    cause?: Error,
  ) {
    super(SwaggerErrorType.BUILD, message, context, cause)
    this.name = 'BuildError'
  }
}

/**
 * 配置验证器集合
 *
 * 提供各种配置对象的验证函数，用于在装饰器中验证用户输入
 */
export class ConfigValidators {
  /**
   * 验证控制器配置
   *
   * @param config - 控制器配置对象
   * @throws {DecoratorConfigError} 当配置无效时抛出错误
   *
   * @example
   * ```typescript
   * ConfigValidators.validateControllerConfig({ tags: ['user'] })
   * ```
   */
  static validateControllerConfig(config: any): asserts config is ControllerConfig {
    if (!config) {
      throw new DecoratorConfigError(
        'Controller',
        'Configuration is required',
        { received: config, type: typeof config },
      )
    }

    if (typeof config !== 'object') {
      throw new DecoratorConfigError(
        'Controller',
        'Invalid configuration: Config must be an object',
        { received: config, type: typeof config },
      )
    }

    // 验证 tags 字段
    if (config.tags !== undefined) {
      if (!Array.isArray(config.tags)) {
        throw new DecoratorConfigError(
          'Controller',
          'tags must be an array of strings',
          { received: config.tags, type: typeof config.tags },
        )
      }

      config.tags.forEach((tag: any, index: number) => {
        if (typeof tag !== 'string') {
          throw new DecoratorConfigError(
            'Controller',
            `tags[${index}] must be a string`,
            { received: tag, type: typeof tag, index },
          )
        }
      })
    }
  }

  /**
   * 验证路由配置
   *
   * @param config - 路由配置对象
   * @throws {DecoratorConfigError} 当配置无效时抛出错误
   *
   * @example
   * ```typescript
   * ConfigValidators.validateRouteConfig({
   *   method: 'post',
   *   path: '/login'
   * })
   * ```
   */
  static validateRouteConfig(config: any): asserts config is Partial<StrongRouteConfig> {
    if (!config || typeof config !== 'object') {
      throw new DecoratorConfigError(
        'RouteConfig',
        'Configuration must be an object',
        { received: config, type: typeof config },
      )
    }

    const errors: string[] = []

    // 检查必需字段
    if (!config.method) {
      errors.push('method is required')
    }
    if (!config.path) {
      errors.push('path is required')
    }

    if (errors.length > 0) {
      throw new DecoratorConfigError(
        'RouteConfig',
        `Invalid configuration: ${errors.join(', ')}`,
        { received: config, missingFields: errors },
      )
    }

    // 验证 method 字段
    if (config.method !== undefined) {
      if (typeof config.method !== 'string') {
        throw new DecoratorConfigError(
          'RouteConfig',
          'method must be a string',
          { received: config.method, type: typeof config.method },
        )
      }

      const validMethods = ['get', 'post', 'put', 'patch', 'delete', 'options', 'head']
      if (!validMethods.includes(config.method.toLowerCase())) {
        throw new DecoratorConfigError(
          'RouteConfig',
          `method must be one of: ${validMethods.join(', ')}`,
          { received: config.method, validMethods },
        )
      }
    }

    // 验证 path 字段
    if (config.path !== undefined) {
      if (typeof config.path !== 'string') {
        throw new DecoratorConfigError(
          'RouteConfig',
          'path must be a string',
          { received: config.path, type: typeof config.path },
        )
      }

      if (!config.path.startsWith('/')) {
        throw new DecoratorConfigError(
          'RouteConfig',
          'path must start with /',
          { received: config.path },
        )
      }
    }
  }
}

/**
 * 错误处理工具类
 *
 * 提供统一的错误处理和元数据操作工具函数
 */
export class ErrorHandler {
  /**
   * 验证装饰器配置
   *
   * 统一的装饰器配置验证入口，支持自定义验证器
   *
   * @param decoratorName - 装饰器名称
   * @param config - 配置对象
   * @param validator - 验证函数
   * @throws {DecoratorConfigError} 当配置无效时抛出错误
   *
   * @example
   * ```typescript
   * ErrorHandler.validateDecoratorConfig(
   *   'Controller',
   *   userConfig,
   *   ConfigValidators.validateControllerConfig
   * )
   * ```
   */
  static validateDecoratorConfig<T>(
    decoratorName: string,
    config: any,
    validator: (config: any) => asserts config is T,
  ): asserts config is T {
    try {
      validator(config)
    } catch (error) {
      if (error instanceof DecoratorConfigError) {
        throw error
      }

      throw new DecoratorConfigError(
        decoratorName,
        `Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { config, originalError: error instanceof Error ? error.message : error },
        error instanceof Error ? error : undefined,
      )
    }
  }

  /**
   * 安全的元数据定义
   *
   * 提供安全的元数据存储，包含错误处理和日志记录
   *
   * @param key - 元数据键
   * @param value - 元数据值
   * @param target - 目标对象
   * @param propertyKey - 属性键（可选）
   * @throws {MetadataError} 当元数据操作失败时抛出错误
   *
   * @example
   * ```typescript
   * ErrorHandler.safeDefineMetadata(
   *   'swagger:controller',
   *   config,
   *   UserController
   * )
   * ```
   */
  static safeDefineMetadata(
    key: string,
    value: any,
    target: any,
    propertyKey?: string | symbol,
  ): void {
    try {
      if (propertyKey !== undefined) {
        Reflect.defineMetadata(key, value, target, propertyKey)
      } else {
        Reflect.defineMetadata(key, value, target)
      }
    } catch (error) {
      throw new MetadataError(
        `Failed to define metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          key,
          target: target?.constructor?.name || target?.name || 'Unknown',
          propertyKey: propertyKey ? String(propertyKey) : undefined,
          value,
        },
        error instanceof Error ? error : undefined,
      )
    }
  }

  /**
   * 安全的元数据获取
   *
   * 提供安全的元数据读取，包含错误处理
   *
   * @param key - 元数据键
   * @param target - 目标对象
   * @param propertyKey - 属性键（可选）
   * @returns 元数据值，如果不存在则返回 undefined
   * @throws {MetadataError} 当元数据操作失败时抛出错误
   *
   * @example
   * ```typescript
   * const config = ErrorHandler.safeGetMetadata(
   *   'swagger:controller',
   *   UserController
   * )
   * ```
   */
  static safeGetMetadata<T = any>(
    key: string,
    target: any,
    propertyKey?: string | symbol,
  ): T | undefined {
    try {
      if (propertyKey !== undefined) {
        return Reflect.getMetadata(key, target, propertyKey)
      } else {
        return Reflect.getMetadata(key, target)
      }
    } catch (error) {
      throw new MetadataError(
        `Failed to get metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          key,
          target: target?.constructor?.name || target?.name || 'Unknown',
          propertyKey: propertyKey ? String(propertyKey) : undefined,
        },
        error instanceof Error ? error : undefined,
      )
    }
  }
}
