import type {
  OpenAPIRegistry,
} from '@asteasolutions/zod-to-openapi/dist/openapi-registry'
import type {
  OpenAPIObject,
  ParameterObject,
  ReferenceObject,
} from 'openapi3-ts/oas30'
import type {
  ControllerClass,
  ControllerConfig,
} from '../types'
import { METADATA_KEYS } from '../types'
import { logger } from '../utils/logger'
import { BuildLogger } from './BuildLogger'
import { processControllerMethod } from './route-processor'

// 全局构建日志器实例
const buildLogger = new BuildLogger()

/**
 * 处理单个控制器
 * @param controller 控制器类
 * @param registry OpenAPI 注册表
 * @param swaggerDoc Swagger 文档对象
 * @param prefix 路由前缀
 */
export function processController(
  controller: ControllerClass,
  registry: OpenAPIRegistry,
  swaggerDoc: OpenAPIObject,
  prefix?: string,
) {
  const controllerName = controller.name || 'Unknown'
  buildLogger.logControllerStart(controllerName)

  // 获取控制器级别的元数据
  const controllerMeta = Reflect.getMetadata(
    METADATA_KEYS.CONTROLLER,
    controller,
  ) as ControllerConfig

  if (!controllerMeta) {
    logger.warn(`⚠️ 控制器 ${controllerName} 缺少 @Controller 装饰器`)
    return
  }

  const controllerTags = controllerMeta.tags || []
  const controllerParameters = controllerMeta.paths?.parameters || []
  const controllerCommonComponents = controllerMeta.components?.parameters || {}

  // 处理控制器级别的参数
  const controllerCommonParameters = Object.values(controllerCommonComponents) as (ParameterObject | ReferenceObject)[]
  const controllerAllParameters = [
    ...controllerCommonParameters, // 先添加公共参数
    ...controllerParameters, // 再添加路径参数
  ] as (ParameterObject | ReferenceObject)[]

  // 注册控制器参数
  controllerAllParameters.forEach((param, index) => {
    // 使用数字索引作为参数名
    registry.registerComponent('parameters', String(index), param)
  })

  // 处理控制器方法
  processControllerMethods(controller, registry, swaggerDoc, {
    prefix,
    controllerTags,
    controllerAllParameters,
    controllerName,
  })
}

/**
 * 处理控制器的所有方法
 * @param controller 控制器类
 * @param registry OpenAPI 注册表
 * @param swaggerDoc Swagger 文档对象
 * @param options 处理选项
 */
export function processControllerMethods(
  controller: ControllerClass,
  registry: OpenAPIRegistry,
  swaggerDoc: OpenAPIObject,
  options: {
    prefix?: string
    controllerTags: string[]
    controllerAllParameters: (ParameterObject | ReferenceObject)[]
    controllerName: string
  },
) {
  Object.getOwnPropertyNames(controller.prototype).forEach((methodName) => {
    if (methodName === 'constructor') {
      return
    }

    processControllerMethod(
      controller,
      methodName,
      registry,
      swaggerDoc,
      options,
    )
  })
}

/**
 * 验证控制器类
 * @param controller 控制器类
 * @returns 是否有效
 */
export function validateController(controller: ControllerClass): boolean {
  if (!controller || typeof controller !== 'function') {
    return false
  }

  // 检查是否有 @Controller 装饰器
  const controllerMeta = Reflect.getMetadata(METADATA_KEYS.CONTROLLER, controller)
  return !!controllerMeta
}

/**
 * 获取控制器的所有方法名
 * @param controller 控制器类
 * @returns 方法名数组
 */
export function getControllerMethods(controller: ControllerClass): string[] {
  return Object.getOwnPropertyNames(controller.prototype)
    .filter((methodName) => methodName !== 'constructor')
}

/**
 * 检查方法是否有路由装饰器
 * @param controller 控制器类
 * @param methodName 方法名
 * @returns 是否有路由装饰器
 */
export function hasRouteDecorator(controller: ControllerClass, methodName: string): boolean {
  const routeConfigMeta = Reflect.getMetadata(
    METADATA_KEYS.ROUTE_CONFIG,
    controller.prototype,
    methodName,
  )
  return !!routeConfigMeta
}

/**
 * 批量处理控制器
 * @param controllers 控制器类数组
 * @param registry OpenAPI 注册表
 * @param swaggerDoc Swagger 文档对象
 * @param prefix 路由前缀
 */
export function processControllers(
  controllers: ControllerClass[],
  registry: OpenAPIRegistry,
  swaggerDoc: OpenAPIObject,
  prefix?: string,
) {
  logger.start(`处理 ${controllers.length} 个控制器...`)

  controllers.forEach((controller) => {
    try {
      processController(controller, registry, swaggerDoc, prefix)
    } catch (error) {
      logger.error(`处理控制器 ${controller.name} 时发生错误:`, error)
      // 继续处理其他控制器，不中断整个流程
    }
  })
}
