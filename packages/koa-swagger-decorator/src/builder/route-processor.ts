import type {
  OpenAPIRegistry,
} from '@asteasolutions/zod-to-openapi/dist/openapi-registry'
import type {
  OpenAPIObject,
  ParameterObject,
  ReferenceObject,
} from 'openapi3-ts/oas30'
import type { ZodObject } from 'zod'
import type {
  ControllerClass,
  StrongRouteConfig,
} from '../types'
import { z } from '../index'
import { METADATA_KEYS } from '../types'
import { firstUpperCase, generateReferenceObject, refLink } from '../utils'
import { BuildLogger } from './BuildLogger'
import { collectSchemas } from './schema-collector'

// HTTP 方法类型
type HttpMethod
  = | 'get'
    | 'post'
    | 'put'
    | 'delete'
    | 'patch'
    | 'options'
    | 'head'

// 全局构建日志器实例
const buildLogger = new BuildLogger()

/**
 * 处理控制器方法
 * @param controller 控制器类
 * @param methodName 方法名
 * @param registry OpenAPI 注册表
 * @param swaggerDoc Swagger 文档对象
 * @param options 处理选项
 */
export function processControllerMethod(
  controller: ControllerClass,
  methodName: string,
  registry: OpenAPIRegistry,
  swaggerDoc: OpenAPIObject,
  options: {
    prefix?: string
    controllerTags: string[]
    controllerAllParameters: (ParameterObject | ReferenceObject)[]
    controllerName: string
  },
) {
  const { prefix, controllerTags, controllerAllParameters, controllerName } = options

  // 获取路由配置元数据
  const routeConfigMeta = Reflect.getMetadata(
    METADATA_KEYS.ROUTE_CONFIG,
    controller.prototype,
    methodName,
  ) as StrongRouteConfig

  if (!routeConfigMeta) {
    return
  }

  // 处理响应
  if (!routeConfigMeta.responses) {
    routeConfigMeta.responses = {
      200: { description: 'Success' },
    }
  }

  // 处理请求体
  processRequestBody(controller, methodName, routeConfigMeta)

  // 处理响应
  processResponses(controller, methodName, registry, routeConfigMeta)

  // 处理方法级别的参数（Header、Query、Path 等）
  const methodParameters: (ParameterObject | ReferenceObject)[] = []

  // 处理 @Header 装饰器
  const headerParams = Reflect.getMetadata(METADATA_KEYS.HEADER, controller.prototype, methodName) as ParameterObject[]
  if (headerParams && Array.isArray(headerParams)) {
    methodParameters.push(...headerParams)
  }

  // 处理 @Query 装饰器
  const queryParams = Reflect.getMetadata(METADATA_KEYS.QUERY, controller.prototype, methodName) as ParameterObject[]
  if (queryParams && Array.isArray(queryParams)) {
    methodParameters.push(...queryParams)
  }

  // 处理 @Path 装饰器
  const pathParams = Reflect.getMetadata(METADATA_KEYS.PATH, controller.prototype, methodName) as ParameterObject[]
  if (pathParams && Array.isArray(pathParams)) {
    methodParameters.push(...pathParams)
  }

  // 处理路由配置中的参数
  if (routeConfigMeta.parameters) {
    Object.entries(routeConfigMeta.parameters).forEach(([name, param]) => {
      registry.registerComponent('parameters', name, param)
      methodParameters.push(param)
    })
  }

  // 处理路径前缀 - 避免修改原始metadata
  const pathWithPrefix = prefix
    ? `${prefix}${routeConfigMeta.path}`
    : routeConfigMeta.path

  // 创建路由配置副本，避免修改原始metadata
  const routeConfigWithMethod = {
    ...routeConfigMeta,
    path: pathWithPrefix,
    tags: [...controllerTags, ...(routeConfigMeta.tags || [])],
    parameters: routeConfigMeta.parameters || {},
    methodName,
  }

  // 合并所有参数
  const allParameters = [
    ...controllerAllParameters,
    ...methodParameters,
  ]

  // 更新 swaggerDoc
  updateSwaggerDoc(swaggerDoc, routeConfigWithMethod, allParameters)

  // 记录路由注册
  buildLogger.logRouteRegistered(routeConfigMeta.method, pathWithPrefix, controllerName)

  // 注册路由 - 使用带前缀的配置
  registry.registerPath({
    ...routeConfigWithMethod,
    parameters: allParameters,
  } as any)
}

/**
 * 处理请求体
 * @param controller 控制器类
 * @param methodName 方法名
 * @param routeConfigMeta 路由配置元数据
 */
export function processRequestBody(
  controller: ControllerClass,
  methodName: string,
  routeConfigMeta: StrongRouteConfig,
) {
  const bodyMeta = Reflect.getMetadata(
    'swagger:body',
    controller.prototype,
    methodName,
  ) as ZodObject<any>

  if (!bodyMeta) {
    return
  }

  const refId = `${firstUpperCase(methodName)}Request`
  collectSchemas(refId, bodyMeta)

  const bodyConfig = {
    body: {
      content: {
        'application/json': {
          schema: {
            $ref: refLink({ refId }),
          },
        },
      },
      required: true,
    },
  }

  routeConfigMeta.request = {
    ...routeConfigMeta.request,
    ...bodyConfig,
  }
}

/**
 * 处理响应
 * @param controller 控制器类
 * @param methodName 方法名
 * @param registry OpenAPI 注册表
 * @param routeConfigMeta 路由配置元数据
 */
export function processResponses(
  controller: ControllerClass,
  methodName: string,
  registry: OpenAPIRegistry,
  routeConfigMeta: StrongRouteConfig,
) {
  const mergedResponses = getMergedResponses(controller, methodName)

  if (!mergedResponses) {
    return
  }

  // 处理多状态码响应格式
  if (isMultiStatusResponse(mergedResponses)) {
    routeConfigMeta.responses = processMultiStatusResponses(mergedResponses, methodName, registry)
    return
  }

  // 处理单一响应格式（兼容旧格式）
  routeConfigMeta.responses = {
    ...routeConfigMeta.responses,
    ...processSingleResponse(mergedResponses, methodName, registry),
  }
}

/**
 * 获取合并后的响应配置
 * @param controller 控制器类
 * @param methodName 方法名
 * @returns 合并后的响应配置
 */
function getMergedResponses(controller: ControllerClass, methodName: string): any {
  // 获取类级别 responsesAll
  const responsesAll = getClassLevelResponses(controller, methodName)

  // 获取方法级别 responses
  const responsesMethod = Reflect.getMetadata(
    METADATA_KEYS.RESPONSES,
    controller.prototype,
    methodName,
  ) as any

  // 合并响应配置，方法级别优先
  return mergeResponses(responsesAll, responsesMethod)
}

/**
 * 获取类级别响应配置
 * @param controller 控制器类
 * @param methodName 方法名
 * @returns 类级别响应配置
 */
function getClassLevelResponses(controller: ControllerClass, methodName: string): any {
  const responsesAllMeta = Reflect.getMetadata(
    METADATA_KEYS.RESPONSES_ALL,
    controller,
  ) as { responses: any, filters: string[] } | undefined

  if (!responsesAllMeta) {
    return null
  }

  const { responses, filters } = responsesAllMeta

  // 检查是否应用到当前方法
  if (filters.includes('ALL') || filters.includes(methodName)) {
    return responses
  }

  return null
}

/**
 * 合并响应配置
 * @param responsesAll 类级别响应配置
 * @param responsesMethod 方法级别响应配置
 * @returns 合并后的响应配置
 */
function mergeResponses(responsesAll: any, responsesMethod: any): any {
  // 如果都没有，返回 null
  if (!responsesAll && !responsesMethod) {
    return null
  }

  // 只有方法级别响应
  if (!responsesAll) {
    return responsesMethod
  }

  // 只有类级别响应
  if (!responsesMethod) {
    return responsesAll
  }

  // 都是对象类型且不是 Zod Schema，进行合并
  if (isMultiStatusResponse(responsesAll) && isMultiStatusResponse(responsesMethod)) {
    return { ...responsesAll, ...responsesMethod }
  }

  // 方法级别优先
  return responsesMethod
}

/**
 * 判断是否为多状态码响应格式
 * @param responses 响应配置
 * @returns 是否为多状态码响应格式
 */
function isMultiStatusResponse(responses: any): boolean {
  return responses
    && typeof responses === 'object'
    && !('shape' in responses) // 不是 Zod Schema
    && Object.keys(responses).some((key) => /^\d{3}$/.test(key)) // 包含状态码键
}

/**
 * 处理多状态码响应
 * @param responses 响应配置
 * @param methodName 方法名
 * @param registry OpenAPI 注册表
 * @returns 处理后的响应配置
 */
function processMultiStatusResponses(
  responses: Record<string, any>,
  methodName: string,
  registry: OpenAPIRegistry,
): Record<string, any> {
  const processedResponses: Record<string, any> = {}

  Object.entries(responses).forEach(([statusCode, responseConfig]) => {
    processedResponses[statusCode] = processStatusCodeResponse(
      statusCode,
      responseConfig,
      methodName,
      registry,
    )
  })

  return processedResponses
}

/**
 * 处理单个状态码响应
 * @param statusCode 状态码
 * @param responseConfig 响应配置
 * @param methodName 方法名
 * @param registry OpenAPI 注册表
 * @returns 处理后的响应配置
 */
function processStatusCodeResponse(
  statusCode: string,
  responseConfig: any,
  methodName: string,
  registry: OpenAPIRegistry,
): any {
  // 没有 schema 的响应，只返回描述
  if (!responseConfig?.schema) {
    return {
      description: responseConfig?.description || getDefaultDescription(statusCode),
    }
  }

  const zodSchema = responseConfig.schema

  // 处理字符串引用（全局 schema）
  if (typeof zodSchema === 'string') {
    return createSchemaResponse(
      responseConfig.description || getDefaultDescription(statusCode),
      generateReferenceObject(registry, zodSchema),
    )
  }

  // 处理 Zod Schema
  return processZodSchemaResponse(statusCode, zodSchema, responseConfig, methodName, registry)
}

/**
 * 处理 Zod Schema 响应
 * @param statusCode 状态码
 * @param zodSchema Zod Schema
 * @param responseConfig 响应配置
 * @param methodName 方法名
 * @param registry OpenAPI 注册表
 * @returns 处理后的响应配置
 */
function processZodSchemaResponse(
  statusCode: string,
  zodSchema: any,
  responseConfig: any,
  methodName: string,
  registry: OpenAPIRegistry,
): any {
  // 检查是否已经是完整的响应格式（包含 resultCode 和 resultMessage）
  const isCompleteResponse = isCompleteResponseSchema(zodSchema)

  if (statusCode === '200') {
    // 200 响应的处理
    if (isCompleteResponse) {
      // 已经是完整响应格式，直接使用，不需要包装
      const dataRefId = `${firstUpperCase(methodName)}VO`
      collectSchemas(dataRefId, zodSchema)

      return createSchemaResponse(
        responseConfig.description || 'Success',
        dataRefId, // 直接使用 schema 引用，不需要包装
      )
    } else {
      // 需要包装的数据格式，使用全局的包装 schema
      return createWrappedResponse(zodSchema, responseConfig, methodName, registry)
    }
  } else {
    // 非 200 状态码的处理：统一使用全局的 R_StringData_
    return createSchemaResponse(
      responseConfig.description || getDefaultDescription(statusCode),
      'R_StringData_', // 直接使用全局 schema 引用，不需要通过 generateReferenceObject 处理
    )
  }
}

/**
 * 处理单一响应格式（兼容旧格式）
 * @param responses 响应配置
 * @param methodName 方法名
 * @param registry OpenAPI 注册表
 * @returns 处理后的响应配置
 */
function processSingleResponse(
  responses: any,
  methodName: string,
  registry: OpenAPIRegistry,
): Record<string, any> {
  let rRefId: string

  if (typeof responses === 'string') {
    // 字符串引用，直接使用全局 schema
    rRefId = generateReferenceObject(registry, responses)
  } else {
    // Zod Schema，需要判断是否需要包装
    const isCompleteResponse = isCompleteResponseSchema(responses)

    if (isCompleteResponse) {
      // 已经是完整响应格式，直接使用
      const dataRefId = `${firstUpperCase(methodName)}VO`
      rRefId = generateReferenceObject(registry, dataRefId)
      collectSchemas(dataRefId, responses)
    } else {
      // 需要包装，使用全局包装 schema
      return {
        200: createWrappedResponse(responses, { description: 'Success' }, methodName, registry),
      }
    }
  }

  return {
    200: createSchemaResponse('Success', rRefId),
  }
}

/**
 * 检查 Zod Schema 是否已经是完整的响应格式
 * @param zodSchema Zod Schema
 * @returns 是否为完整响应格式
 */
function isCompleteResponseSchema(zodSchema: any): boolean {
  // 检查是否为 Zod Object
  if (!zodSchema || typeof zodSchema !== 'object' || !zodSchema._def) {
    console.log(`🔍 [DEBUG] isCompleteResponseSchema: 不是有效的 Zod Schema`)
    return false
  }

  const def = zodSchema._def

  // 检查是否为 ZodObject
  if (def.typeName !== 'ZodObject') {
    console.log(`🔍 [DEBUG] isCompleteResponseSchema: 不是 ZodObject，类型为: ${def.typeName}`)
    return false
  }

  // 获取 shape
  const shape = typeof def.shape === 'function' ? def.shape() : def.shape
  if (!shape || typeof shape !== 'object') {
    console.log(`🔍 [DEBUG] isCompleteResponseSchema: 没有有效的 shape`)
    return false
  }

  // 检查是否包含响应格式的关键字段
  const hasResultCode = 'resultCode' in shape || 'code' in shape
  const hasResultMessage = 'resultMessage' in shape || 'message' in shape

  console.log(`🔍 [DEBUG] isCompleteResponseSchema: hasResultCode=${hasResultCode}, hasResultMessage=${hasResultMessage}`)
  console.log(`🔍 [DEBUG] isCompleteResponseSchema: shape keys=${Object.keys(shape)}`)

  const isComplete = hasResultCode && hasResultMessage
  console.log(`🔍 [DEBUG] isCompleteResponseSchema: 结果=${isComplete}`)

  return isComplete
}

/**
 * 创建包装后的响应
 * @param dataSchema 数据 Schema
 * @param responseConfig 响应配置
 * @param methodName 方法名
 * @param registry OpenAPI 注册表
 * @returns 包装后的响应配置
 */
function createWrappedResponse(
  dataSchema: any,
  responseConfig: any,
  methodName: string,
  registry: OpenAPIRegistry,
): any {
  // 先收集数据 schema
  const dataRefId = `${firstUpperCase(methodName)}Data`
  collectSchemas(dataRefId, dataSchema)

  // 创建包装后的 schema ID
  const wrappedRefId = `${firstUpperCase(methodName)}VO`

  // 使用 R 函数的模式创建包装后的 Zod Schema
  const wrappedSchema = z.object({
    resultCode: z.number().openapi({ example: 200 }),
    resultMessage: z.string().openapi({ example: 'success' }),
    data: dataSchema, // 直接使用原始的 dataSchema
  })

  // 收集包装后的 schema，让 schema-collector 统一处理注册
  collectSchemas(wrappedRefId, wrappedSchema)

  return createSchemaResponse(
    responseConfig.description || 'Success',
    generateReferenceObject(registry, wrappedRefId),
  )
}

/**
 * 创建带 schema 的响应配置
 * @param description 响应描述
 * @param refId 引用 ID
 * @returns 响应配置
 */
function createSchemaResponse(description: string, refId: string): any {
  return {
    description,
    content: {
      'application/json': {
        schema: {
          $ref: refLink({ refId }),
        },
      },
    },
  }
}

/**
 * 获取默认响应描述
 * @param statusCode 状态码
 * @returns 默认描述
 */
function getDefaultDescription(statusCode: string): string {
  return statusCode === '200' ? 'Success' : 'Error'
}

/**
 * 更新 Swagger 文档
 * @param swaggerDoc Swagger 文档对象
 * @param routeConfigMeta 路由配置元数据
 * @param allParameters 所有参数列表
 */
export function updateSwaggerDoc(
  swaggerDoc: OpenAPIObject,
  routeConfigMeta: StrongRouteConfig & { methodName?: string },
  allParameters: (ParameterObject | ReferenceObject)[],
) {
  if (!swaggerDoc.paths[routeConfigMeta.path]) {
    swaggerDoc.paths[routeConfigMeta.path] = {}
  }

  const method = routeConfigMeta.method.toLowerCase() as HttpMethod
  swaggerDoc.paths[routeConfigMeta.path][method] = {
    tags: routeConfigMeta.tags,
    parameters: allParameters,
    summary: routeConfigMeta.summary,
    description: routeConfigMeta.description,
    ...(routeConfigMeta.request?.body && {
      requestBody: routeConfigMeta.request.body as {
        content: Record<string, any>
        required?: boolean
      },
    }),
    responses: routeConfigMeta.responses || { 200: { description: 'Success' } },
    operationId: routeConfigMeta.operationId || routeConfigMeta.methodName,
    deprecated: routeConfigMeta.deprecated ?? false,
    ...(routeConfigMeta.security && {
      security: routeConfigMeta.security,
    }),
    ...(routeConfigMeta.servers && {
      servers: routeConfigMeta.servers,
    }),
    ...(routeConfigMeta.externalDocs && {
      externalDocs: routeConfigMeta.externalDocs,
    }),
  }
}
