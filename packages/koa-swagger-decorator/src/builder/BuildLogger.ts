/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-10 11:18:28
 * @LastEditTime: 2025-07-17 12:43:08
 * @LastEditors: shaojun
 * @Description: Swagger文档构建日志管理器
 */

import type { ZodTypeAny } from 'zod'
import type { SchemaMeta } from './schema-collector'
import { globalSchemas } from '../global'
import { logger } from '../utils/logger'

// 构建统计信息
interface BuildStats {
  controllers: number
  routes: number
  schemas: number
  globalSchemas: number
  controllerNames: string[]
  routeDetails: Array<{ method: string, path: string, controller: string }>
}

// 构建日志管理器
export class BuildLogger {
  private startTime: number = 0
  private stats: BuildStats = {
    controllers: 0,
    routes: 0,
    schemas: 0,
    globalSchemas: 0,
    controllerNames: [],
    routeDetails: [],
  }

  startBuild() {
    this.startTime = Date.now()
    logger.start('🚀 开始构建 Swagger 文档')
    logger.start('初始化文档构建器...')
  }

  logGlobalSchemas(globals?: Record<string, ZodTypeAny>) {
    const builtinNames = Object.keys(globalSchemas)
    const externalNames = globals ? Object.keys(globals) : []
    const allGlobalNames = [...builtinNames, ...externalNames]

    this.stats.globalSchemas = allGlobalNames.length
    if (allGlobalNames.length > 0) {
      logger.info(`📝 加载了 ${allGlobalNames.length} 个全局 Schema: ${allGlobalNames.join(', ')}`)
    }
  }

  logControllerStart(name: string) {
    this.stats.controllers++
    this.stats.controllerNames.push(name)
    logger.debug(`处理控制器: ${name}`)
  }

  logRouteRegistered(method: string, path: string, controller: string) {
    this.stats.routes++
    this.stats.routeDetails.push({ method: method.toUpperCase(), path, controller })
  }

  logSchemaCollection(count: number, collectionSchemas: SchemaMeta[]) {
    this.stats.schemas = count
    if (count > 0) {
      const schemaNames = collectionSchemas.map((s) => s.refId).join(', ')
      logger.info(`🔗 收集了 ${count} 个自定义 Schema: ${schemaNames}`)
    }
  }

  finishBuild() {
    const duration = Date.now() - this.startTime

    logger.success('✨ Swagger 文档构建完成')

    // 显示构建统计 - 使用普通日志格式
    logger.info(`📊 构建统计:`)
    logger.info(`   控制器数量: ${this.stats.controllers}`)
    logger.info(`   路由数量: ${this.stats.routes}`)
    logger.info(`   Schema数量: ${this.stats.globalSchemas + this.stats.schemas} (全局: ${this.stats.globalSchemas}, 自定义: ${this.stats.schemas})`)
    logger.info(`   构建耗时: ${duration}ms`)

    // 显示控制器列表
    if (this.stats.controllerNames.length > 0) {
      logger.info(`📂 控制器列表: ${this.stats.controllerNames.join(', ')}`)
    }
  }

  getStats(): BuildStats {
    return { ...this.stats }
  }
}

// 导出BuildStats接口供其他地方使用
export type { BuildStats }
