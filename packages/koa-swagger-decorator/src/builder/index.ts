import type {
  OpenAPIRegistry,
} from '@asteasolutions/zod-to-openapi/dist/openapi-registry'
import type {
  OpenAPIObject,
} from 'openapi3-ts/oas30'
import type { ZodTypeAny } from 'zod'
import type {
  ControllerClass,
} from '../types'
import { OpenApiGeneratorV3 } from '@asteasolutions/zod-to-openapi'
import { logger } from '../utils/logger'
import { BuildLogger } from './BuildLogger'
import { processControllers } from './controller-processor'
import {
  addGlobalSchemas,
  clearSchemas,
  getSchemasCount,
  getSchemasList,
  registerSchemas,
} from './schema-collector'

const TAG = 'swagger-builder'

// 全局构建日志器实例
const buildLogger = new BuildLogger()

/**
 * 默认的 Swagger 文档配置
 *
 * 提供了最基本的 OpenAPI 3.0 规范配置，包括：
 * - 版本信息
 * - 基础文档结构
 * - 空的路径和组件定义
 *
 * @see https://spec.openapis.org/oas/v3.0.3
 */
export const defaultSwaggerDoc: OpenAPIObject = {
  openapi: '3.0.0',
  info: {
    title: 'Swagger API',
    version: '3.0.0',
  },
  paths: {},
  components: {
    schemas: {},
  },
}

/**
 * Swagger 文档生成协调器
 *
 * 这是整个 Swagger 文档生成流程的核心协调器，负责：
 * 1. 初始化和清理构建环境
 * 2. 协调各个子模块的工作流程
 * 3. 管理全局状态和配置
 * 4. 生成最终的 OpenAPI 文档
 *
 * ### 工作流程：
 * 1. **环境准备** - 清理缓存，初始化日志
 * 2. **配置合并** - 合并默认配置和用户配置
 * 3. **全局处理** - 添加全局 schemas
 * 4. **控制器处理** - 处理所有控制器类
 * 5. **Schema 注册** - 注册收集的 schemas
 * 6. **文档生成** - 生成最终的 OpenAPI 文档
 * 7. **结果返回** - 返回 JSON 和对象格式
 *
 * @param registry - OpenAPI 注册表，用于注册组件和路径
 * @param controllerClasses - 控制器类数组，包含所有需要处理的控制器
 * @param prefix - 可选的路由前缀，会添加到所有路径前面
 * @param globals - 可选的全局 schemas，会被注册为全局组件
 * @param spec - 可选的文档配置，会与默认配置合并
 *
 * @returns 包含三种格式的文档对象：
 *   - `json`: JSON 字符串格式的文档
 *   - `object`: 完整的 OpenAPI 对象
 *   - `swaggerDoc`: 用于构建的基础文档对象
 *
 * @throws {Error} 当控制器处理或文档生成失败时抛出错误
 *
 * @example
 * ```typescript
 * import { useRegistry } from '../registry'
 * import { UserController, ProductController } from './controllers'
 *
 * const { getRegistry } = useRegistry()
 * const registry = getRegistry()
 *
 * // 基础使用
 * const result = generateSwaggerDoc(registry, [UserController])
 *
 * // 带配置的使用
 * const result = generateSwaggerDoc(
 *   registry,
 *   [UserController, ProductController],
 *   '/api/v1',
 *   { GlobalUser: UserSchema },
 *   {
 *     info: {
 *       title: 'My API',
 *       version: '1.0.0',
 *       description: 'API 文档'
 *     }
 *   }
 * )
 *
 * console.log(result.json) // JSON 字符串
 * console.log(result.object) // OpenAPI 对象
 * ```
 *
 * @see {@link processControllers} 控制器处理逻辑
 * @see {@link addGlobalSchemas} 全局 Schema 处理
 * @see {@link registerSchemas} Schema 注册逻辑
 */
export function generateSwaggerDoc(
  registry: OpenAPIRegistry,
  controllerClasses: ControllerClass[],
  prefix?: string,
  globals?: Record<string, ZodTypeAny>,
  spec?: Partial<OpenAPIObject>,
): { json: string, object: OpenAPIObject, swaggerDoc: OpenAPIObject } {
  try {
    // 🧹 第一步：环境准备
    logger.start(`${TAG}: 开始生成 Swagger 文档`)

    // 清空之前收集的schemas，确保状态隔离
    clearSchemas()

    // 开始构建日志
    buildLogger.startBuild()

    // 📋 第二步：配置合并
    logger.info(`${TAG}: 合并文档配置`)

    // 复制默认的 swaggerDoc，并合并自定义配置，确保paths是新的
    const swaggerDoc: OpenAPIObject = {
      ...defaultSwaggerDoc,
      ...(spec || {}),
      paths: {}, // 确保每次都从空的paths开始
      components: {
        ...defaultSwaggerDoc.components,
        schemas: {},
      },
    }

    logger.debug(`${TAG}: 文档配置合并完成`, {
      title: swaggerDoc.info?.title,
      version: swaggerDoc.info?.version,
      hasCustomSpec: !!spec,
    })

    // 🌐 第三步：全局处理
    logger.start(`${TAG}: 处理全局 Schema...`)
    addGlobalSchemas(globals)
    buildLogger.logGlobalSchemas(globals)

    // 🎯 第四步：控制器处理
    logger.start(`${TAG}: 处理 ${controllerClasses.length} 个控制器...`)
    processControllers(controllerClasses, registry, swaggerDoc, prefix)

    // 📦 第五步：Schema 注册
    logger.start(`${TAG}: 注册 Schema 组件...`)
    const schemasCount = getSchemasCount()
    const schemasList = getSchemasList()

    buildLogger.logSchemaCollection(schemasCount, schemasList)
    registerSchemas(registry)

    logger.success(`${TAG}: 成功注册 ${schemasCount} 个 Schema`)

    // 📄 第六步：文档生成
    logger.start(`${TAG}: 生成 OpenAPI 文档...`)
    const generator = new OpenApiGeneratorV3(registry.definitions)
    const object = generator.generateDocument(swaggerDoc)
    const json = JSON.stringify(object, null, 2)

    // 🎉 第七步：完成构建
    buildLogger.finishBuild()

    logger.success(`${TAG}: Swagger 文档生成完成`)
    logger.info(`${TAG}: 文档包含 ${Object.keys(object.paths || {}).length} 个路径`)

    return { json, object, swaggerDoc }
  } catch (error) {
    // 错误处理和日志记录
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    logger.error(`${TAG}: 文档生成失败: ${errorMessage}`, error)

    // 确保构建日志器知道构建失败
    buildLogger.finishBuild()

    throw new Error(`Swagger 文档生成失败: ${errorMessage}`)
  }
}

// 导出控制器处理相关函数以保持向后兼容
export * from './controller-processor'

// 导出路由处理相关函数以保持向后兼容
export * from './route-processor'

// 导出 schema 收集相关函数以保持向后兼容
export * from './schema-collector'
