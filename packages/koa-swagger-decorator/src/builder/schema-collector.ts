import type {
  ComponentTypeOf,
  OpenAPIRegistry,
} from '@asteasolutions/zod-to-openapi/dist/openapi-registry'
import type { ReferenceObject, SchemaObject } from 'openapi3-ts/oas30'
import type { ZodTypeAny } from 'zod'
import { extendZodWithOpenApi } from '@asteasolutions/zod-to-openapi'
import { z } from 'zod'
import { globalSchemas } from '../global'
import { refLink } from '../utils'
import { logger } from '../utils/logger'

// Schema 元数据接口
export interface SchemaMeta {
  refId: string
  zodSchema: ZodTypeAny | ComponentTypeOf<'schemas'> | SchemaObject | ReferenceObject
}

// 收集 schemas 的全局数组
export const CollectionSchemas: SchemaMeta[] = []

/**
 * 添加全局 schemas
 * @param globals 全局 schemas 对象
 */
export function addGlobalSchemas(globals?: Record<string, ZodTypeAny>) {
  // 处理内置的 schemas (按照旧代码的逻辑)
  Object.entries(globalSchemas).forEach(([key, element]) => {
    if (element && typeof element === 'object') {
      logger.info(`📋 收集内置 schema: ${key}`)
      collectSchemas(key, element) // 不强制覆盖
    }
  })

  // 处理外部传入的 schemas (可以覆盖内置的 schemas)
  if (globals) {
    logger.info(`🌐 开始处理 ${Object.keys(globals).length} 个外部全局 schema`)

    for (const [refId, zodSchema] of Object.entries(globals)) {
      if (!refId || !zodSchema) {
        continue
      }

      // 详细调试信息
      const hasOpenapi = !!(zodSchema as any).openapi
      const hasOpenapiMethod = typeof (zodSchema as any).openapi === 'function'
      const hasOpenapiProperty = (zodSchema as any).openapi && typeof (zodSchema as any).openapi === 'object'
      const hasZodDef = !!(zodSchema as any)._def
      const zodType = (zodSchema as any)._def?.typeName || 'unknown'

      logger.info(`📋 外部 schema ${refId}:`)
      logger.info(`  - hasOpenapi: ${hasOpenapi}`)
      logger.info(`  - hasOpenapiMethod: ${hasOpenapiMethod}`)
      logger.info(`  - hasOpenapiProperty: ${hasOpenapiProperty}`)
      logger.info(`  - hasZodDef: ${hasZodDef}`)
      logger.info(`  - zodType: ${zodType}`)
      logger.info(`  - zodSchema constructor: ${zodSchema.constructor.name}`)

      // 额外的调试信息
      console.log(`🔍 [SCHEMA-COLLECTOR] ${refId} openapi 属性类型:`, typeof (zodSchema as any).openapi)
      console.log(`🔍 [SCHEMA-COLLECTOR] ${refId} openapi 属性值:`, (zodSchema as any).openapi)
      console.log(`🔍 [SCHEMA-COLLECTOR] ${refId} 所有属性:`, Object.getOwnPropertyNames(zodSchema))
      console.log(`🔍 [SCHEMA-COLLECTOR] ${refId} 原型链属性:`, Object.getOwnPropertyNames(Object.getPrototypeOf(zodSchema)))

      // 尝试手动添加 openapi 方法
      if (!hasOpenapiMethod && hasZodDef) {
        console.log(`🔧 [SCHEMA-COLLECTOR] 尝试手动为 ${refId} 添加 openapi 方法`)
        try {
          // 重新扩展这个 schema
          extendZodWithOpenApi(z)

          // 检查是否现在有 openapi 方法了
          const hasOpenapiAfterExtend = typeof (zodSchema as any).openapi === 'function'
          console.log(`🔧 [SCHEMA-COLLECTOR] ${refId} 扩展后有 openapi 方法:`, hasOpenapiAfterExtend)
        } catch (error) {
          console.log(`🔧 [SCHEMA-COLLECTOR] ${refId} 手动扩展失败:`, error instanceof Error ? error.message : String(error))
        }
      }

      // 尝试调用 openapi 方法看看会发生什么
      if (hasOpenapiMethod) {
        try {
          const openapiResult = (zodSchema as any).openapi({})
          logger.info(`  - openapi() 调用成功: ${!!openapiResult.openapi}`)
        } catch (error) {
          logger.error(`  - openapi() 调用失败: ${error instanceof Error ? error.message : String(error)}`)
        }
      }

      collectSchemas(refId, zodSchema, true) // 强制添加覆盖
    }
  }
}

/**
 * 收集 schemas
 * @param refId Schema 引用 ID
 * @param zodSchema Zod Schema 对象
 * @param force 是否强制覆盖已存在的 Schema
 */
export function collectSchemas(
  refId: string,
  zodSchema: ZodTypeAny,
  force = false,
) {
  if (!refId) {
    return
  }

  const isExist = (id: string) =>
    CollectionSchemas.some((o: SchemaMeta) => o.refId === id)

  if (force && isExist(refId)) {
    const index = CollectionSchemas.findIndex((o: SchemaMeta) => o.refId === refId)
    CollectionSchemas.splice(index, 1)
  } else if (!force && isExist(refId)) {
    return
  }

  // 调试：记录收集的 schema 信息
  logger.info(`📋 收集 schema ${refId}: hasOpenapi=${!!(zodSchema as any).openapi}, force=${force}`)

  CollectionSchemas.push({ refId, zodSchema })
}

/**
 * 注册所有收集到的 schemas
 * @param registry OpenAPI 注册表
 */
export function registerSchemas(registry: OpenAPIRegistry) {
  if (!CollectionSchemas.length) {
    return
  }

  logger.info(`📋 正在注册 ${CollectionSchemas.length} 个 Schema...`)

  CollectionSchemas.forEach((o) => {
    // 检查是否为 Zod Schema 且有 openapi 方法
    const isZodSchema = o.zodSchema && typeof o.zodSchema === 'object' && '_def' in o.zodSchema
    const hasOpenapiMethod = typeof (o.zodSchema as any).openapi === 'function'

    if (isZodSchema && hasOpenapiMethod) {
      // 如果是 Zod schema 且有 openapi 方法，使用 registry.register
      console.log(`🔍 [DEBUG] 注册 Zod Schema (有 openapi): ${o.refId}`)
      registry.register(o.refId, o.zodSchema as ZodTypeAny)
      logger.info(`✅ 已注册 Zod Schema: ${o.refId}`)
    } else if (isZodSchema) {
      // 如果是 Zod schema 但没有 openapi 方法，我们需要手动转换
      console.log(`🔍 [DEBUG] 转换并注册 Zod Schema (无 openapi): ${o.refId}`)
      try {
        // 创建一个临时的 registry 来转换这个 schema
        const tempRegistry = new (registry.constructor as any)()

        // 给这个 schema 添加 openapi 方法（通过扩展当前的 z 实例）
        extendZodWithOpenApi(z)

        // 尝试使用临时 registry 转换
        tempRegistry.register(o.refId, o.zodSchema as ZodTypeAny)
        const tempDoc = tempRegistry.generateDocument({ openapi: '3.0.0', info: { title: 'temp', version: '1.0.0' } })

        // 提取转换后的 schema
        const convertedSchema = tempDoc.components?.schemas?.[o.refId]
        if (convertedSchema) {
          registry.registerComponent('schemas', o.refId, convertedSchema as ComponentTypeOf<'schemas'>)
          logger.info(`✅ 已转换并注册 Zod Schema: ${o.refId}`)
        } else {
          throw new Error('转换失败')
        }
      } catch (error) {
        logger.error(`❌ 转换 Zod Schema 失败: ${o.refId}, 错误: ${error instanceof Error ? error.message : String(error)}`)
        // 降级处理：跳过这个 schema
      }
    } else {
      // 如果不是 Zod schema，使用 registerComponent
      console.log(`🔍 [DEBUG] 注册 Component Schema: ${o.refId}`)
      registry.registerComponent('schemas', o.refId, o.zodSchema as ComponentTypeOf<'schemas'>)
      logger.info(`✅ 已注册 Component Schema: ${o.refId}`)
    }
  })
}

/**
 * 创建全局引用对象
 * @param globalKey 全局 Schema 键
 * @returns Schema 对象或引用对象
 */
export const globalRefObject = (
  globalKey: string,
): SchemaObject | ReferenceObject => {
  const match = /List_(.*)_/.exec(globalKey)
  if (match) {
    const theKey = match[1]
    return {
      type: 'array',
      items: {
        $ref: refLink({ refId: theKey }),
      },
    }
  }

  return {
    $ref: refLink({ refId: globalKey }),
  }
}

/**
 * 清空收集的 schemas
 * 用于确保状态隔离
 */
export function clearSchemas() {
  CollectionSchemas.length = 0
}

/**
 * 获取收集的 schemas 数量
 * @returns Schema 数量
 */
export function getSchemasCount(): number {
  return CollectionSchemas.length
}

/**
 * 获取收集的 schemas 列表
 * @returns Schema 列表的副本
 */
export function getSchemasList(): SchemaMeta[] {
  return [...CollectionSchemas]
}
