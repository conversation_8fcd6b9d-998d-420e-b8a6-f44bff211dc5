import type {
  ComponentTypeOf,
  OpenAPIRegistry,
} from '@asteasolutions/zod-to-openapi/dist/openapi-registry'
import type { ReferenceObject, SchemaObject } from 'openapi3-ts/oas30'
import type { ZodTypeAny } from 'zod'
import { globalSchemas } from '../global'
import { refLink } from '../utils'
import { logger } from '../utils/logger'

// Schema 元数据接口
export interface SchemaMeta {
  refId: string
  zodSchema: ZodTypeAny | ComponentTypeOf<'schemas'> | SchemaObject | ReferenceObject
}

// 收集 schemas 的全局数组
export const CollectionSchemas: SchemaMeta[] = []

/**
 * 添加全局 schemas
 * @param globals 全局 schemas 对象
 */
export function addGlobalSchemas(globals?: Record<string, ZodTypeAny>) {
  // 处理内置的 schemas (按照旧代码的逻辑)
  Object.entries(globalSchemas).forEach(([key, element]) => {
    if (element && typeof element === 'object') {
      logger.info(`📋 收集内置 schema: ${key}`)
      collectSchemas(key, element) // 不强制覆盖
    }
  })

  // 处理外部传入的 schemas (可以覆盖内置的 schemas)
  if (globals) {
    logger.info(`🌐 开始处理 ${Object.keys(globals).length} 个外部全局 schema`)

    for (const [refId, zodSchema] of Object.entries(globals)) {
      if (!refId || !zodSchema) {
        continue
      }

      // 详细调试信息
      const hasOpenapi = !!(zodSchema as any).openapi
      const hasOpenapiMethod = typeof (zodSchema as any).openapi === 'function'
      const hasOpenapiProperty = (zodSchema as any).openapi && typeof (zodSchema as any).openapi === 'object'
      const hasZodDef = !!(zodSchema as any)._def
      const zodType = (zodSchema as any)._def?.typeName || 'unknown'

      logger.info(`📋 外部 schema ${refId}:`)
      logger.info(`  - hasOpenapi: ${hasOpenapi}`)
      logger.info(`  - hasOpenapiMethod: ${hasOpenapiMethod}`)
      logger.info(`  - hasOpenapiProperty: ${hasOpenapiProperty}`)
      logger.info(`  - hasZodDef: ${hasZodDef}`)
      logger.info(`  - zodType: ${zodType}`)
      logger.info(`  - zodSchema constructor: ${zodSchema.constructor.name}`)

      // 尝试调用 openapi 方法看看会发生什么
      if (hasOpenapiMethod) {
        try {
          const openapiResult = (zodSchema as any).openapi({})
          logger.info(`  - openapi() 调用成功: ${!!openapiResult?.openapi}`)
        } catch (error) {
          logger.error(`  - openapi() 调用失败: ${error instanceof Error ? error.message : String(error)}`)
        }
      }

      collectSchemas(refId, zodSchema, true) // 强制添加覆盖
    }
  }
}

/**
 * 收集 schemas
 * @param refId Schema 引用 ID
 * @param zodSchema Zod Schema 对象
 * @param force 是否强制覆盖已存在的 Schema
 */
export function collectSchemas(
  refId: string,
  zodSchema: ZodTypeAny,
  force = false,
) {
  if (!refId) {
    return
  }

  const isExist = (id: string) =>
    CollectionSchemas.some((o: SchemaMeta) => o.refId === id)

  if (force && isExist(refId)) {
    const index = CollectionSchemas.findIndex((o: SchemaMeta) => o.refId === refId)
    CollectionSchemas.splice(index, 1)
  } else if (!force && isExist(refId)) {
    return
  }

  // 调试：记录收集的 schema 信息
  logger.info(`📋 收集 schema ${refId}: hasOpenapi=${!!(zodSchema as any).openapi}, force=${force}`)

  CollectionSchemas.push({ refId, zodSchema })
}

/**
 * 检查是否为 Zod Schema（带有 openapi 扩展）
 * @param schema 要检查的 schema
 * @returns 是否为 Zod Schema
 */
function isZodSchemaWithOpenApi(schema: any): boolean {
  // 检查是否有 _def 属性（Zod schema 的标识）
  if (!schema || !schema._def) {
    logger.debug(`❌ Schema 没有 _def 属性`)
    return false
  }

  // 对于 Zod Schema，我们总是使用 registry.register
  // 因为 extendZodWithOpenApi 会给所有 Zod Schema 添加 openapi 方法
  const isZodSchema = schema._def && schema._def.typeName

  logger.debug(`🔍 Schema 检查: isZodSchema=${isZodSchema}, typeName=${schema._def?.typeName}`)

  return isZodSchema
}

/**
 * 注册所有收集到的 schemas
 * @param registry OpenAPI 注册表
 */
export function registerSchemas(registry: OpenAPIRegistry) {
  if (!CollectionSchemas.length) {
    return
  }

  logger.info(`📋 正在注册 ${CollectionSchemas.length} 个 Schema...`)

  CollectionSchemas.forEach((o) => {
    const isZodSchema = isZodSchemaWithOpenApi(o.zodSchema)

    // 强制输出到控制台进行调试
    console.log(`🔍 [DEBUG] 注册 schema ${o.refId}: isZodSchema=${isZodSchema}`)
    console.log(`🔍 [DEBUG] Schema 类型:`, typeof o.zodSchema)
    console.log(`🔍 [DEBUG] Schema 构造函数:`, o.zodSchema?.constructor?.name)
    console.log(`🔍 [DEBUG] 有 _def:`, !!(o.zodSchema as any)?._def)
    console.log(`🔍 [DEBUG] 有 openapi 方法:`, typeof (o.zodSchema as any)?.openapi === 'function')

    logger.info(`📋 注册 schema ${o.refId}: isZodSchema=${isZodSchema}`)

    if (isZodSchema) {
      // register zod schema - 这会正确处理 .openapi() 扩展
      console.log(`🔍 [DEBUG] 使用 registry.register 注册: ${o.refId}`)
      registry.register(o.refId, o.zodSchema as ZodTypeAny)
      logger.info(`✅ 已注册 Zod Schema: ${o.refId}`)
    } else {
      // register component schemas - 用于普通的 OpenAPI schema 对象
      console.log(`🔍 [DEBUG] 使用 registry.registerComponent 注册: ${o.refId}`)
      registry.registerComponent('schemas', o.refId, o.zodSchema as ComponentTypeOf<'schemas'>)
      logger.info(`✅ 已注册 Component Schema: ${o.refId}`)
    }
  })
}

/**
 * 创建全局引用对象
 * @param globalKey 全局 Schema 键
 * @returns Schema 对象或引用对象
 */
export const globalRefObject = (
  globalKey: string,
): SchemaObject | ReferenceObject => {
  const match = /List_(.*)_/.exec(globalKey)
  if (match) {
    const theKey = match[1]
    return {
      type: 'array',
      items: {
        $ref: refLink({ refId: theKey }),
      },
    }
  }

  return {
    $ref: refLink({ refId: globalKey }),
  }
}

/**
 * 清空收集的 schemas
 * 用于确保状态隔离
 */
export function clearSchemas() {
  CollectionSchemas.length = 0
}

/**
 * 获取收集的 schemas 数量
 * @returns Schema 数量
 */
export function getSchemasCount(): number {
  return CollectionSchemas.length
}

/**
 * 获取收集的 schemas 列表
 * @returns Schema 列表的副本
 */
export function getSchemasList(): SchemaMeta[] {
  return [...CollectionSchemas]
}
