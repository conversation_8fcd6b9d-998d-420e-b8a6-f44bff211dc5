import { firstUpperCase } from './common'
import { Container } from './container'

export const CONFIG_SYMBOL = Symbol.for('CONFIG')
export const DECORATOR_REQUEST = Symbol.for('DECORATOR_REQUEST')
export const DECORATOR_SCHEMAS = Symbol.for('DECORATOR_SCHEMAS')
Container.set(CONFIG_SYMBOL, {})

export function getIdentifier(target: any, methodName?: string) {
  const className = target.constructor.name
  methodName = firstUpperCase(methodName || '')
  const identifier = `${className}${methodName ? `-${methodName}` : ''}`
  return identifier
}
export function getIdentifierMethodName(identifier: string) {
  return identifier.split('-')[1]
}

export function convertPath(path: string) {
  // const re = new RegExp('{(.*?)}', 'g')
  const re = /{([^}]+)}/g
  return path.replace(re, ':$1')
}

export const reservedMethodNames = [
  'middlewares',
  'name',
  'constructor',
  'length',
  'prototype',
  'parameters',
  'prefix',
]

export type Method =
  | 'get'
  | 'post'
  | 'put'
  | 'delete'
  | 'patch'
  | 'head'
  | 'options'
  | 'trace'
