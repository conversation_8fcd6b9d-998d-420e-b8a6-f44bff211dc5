import { extendZodWithOpenApi } from '@asteasolutions/zod-to-openapi'
import { z } from 'zod'
import type { Method } from './utils/constant'
import { registry } from './registry'
import type { ItemMeta } from './swagger-router'

extendZodWithOpenApi(z)
console.log(`=============extendZodWithOpenApi==================`)

export declare type ArgsKey = 'query' | 'params' | 'body'

export declare type ParsedKeyArgs<K extends ArgsKey, T> = {
  [key in K]?: T
}
export declare type ParsedArgs<T> = ParsedKeyArgs<'query' | 'params' | 'body', T>

export { z, registry }

export * from './type'
export * from './decorator'
export * from './swagger-router'
export * from './global'
export * from './utils'

export type { Method, ItemMeta }
