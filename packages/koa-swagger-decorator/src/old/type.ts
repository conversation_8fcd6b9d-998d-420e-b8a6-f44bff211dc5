/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-12 10:36:29
 * @LastEditTime: 2024-04-12 15:40:58
 * @LastEditors: shaojun
 * @Description:
 */
import type { ComponentsObject, ParameterObject, ReferenceObject } from 'openapi3-ts/oas30'

export interface ControllerConfig {
  paths?: {
    parameters?: (ParameterObject | ReferenceObject)[]
  }
  components?: ComponentsObject
  tags?: string[]
}

import type { RouteConfig } from '@asteasolutions/zod-to-openapi'

export declare type StrongRouteConfig = RouteConfig & RouteConfigDecorator

export interface RouteConfigDecorator {
  [key: string]: any
  className?: string
  methodName?: string
  identifier?: string
}
