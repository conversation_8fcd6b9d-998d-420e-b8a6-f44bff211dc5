import type { ZodO<PERSON>, ZodString } from 'zod'
import type { RouteConfig } from '@asteasolutions/zod-to-openapi'

import type { ReferenceObject, SchemaObject } from 'openapi3-ts/oas30'
import { Container } from './utils/container'
import { DECORATOR_REQUEST, getIdentifier } from './utils/constant'
import type { ControllerConfig, RouteConfigDecorator, StrongRouteConfig } from './type'

const TAG = 'swagger-decorator===>'

/**
 * 装饰器函数 @controller
 * @param v
 * @returns (target: any, name: string, descriptor: PropertyDescriptor) => PropertyDescriptor
 * @description swagger common components['schemas' | 'responses' | 'parameters' | 'examples' | 'requestBodies' | 'headers' | 'securitySchemes' | 'links' | 'callbacks']
 */
const controller
= (v: ControllerConfig) =>
  (target: any) => {
    const className = target.name
    const containerPropertyKey = `DECORATOR_CONTROLLER_${className}`
    console.log(`${TAG} @controller: ${containerPropertyKey}`)
    target.prototype.containerConfig = v
    Container.set(containerPropertyKey, v)
    return target
  }

/**
 * 装饰器函数 @routeConfig
 * @param v Partial<RouteConfig>
 * @returns (target: any, methodName: string, descriptor: PropertyDescriptor) => PropertyDescriptor
 */
const routeConfig
  = (v: Partial<StrongRouteConfig>) =>
    (target: any, methodName: string, descriptor: PropertyDescriptor) => {
      if (!v.method || !v.path) {
        throw new Error(`missing [method] and [path] fields for routeConfig`)
      }
      const className = target.constructor.name
      const identifier = getIdentifier(target, methodName)
      const containerPropertyKey = `DECORATOR_ROUTE_${identifier}`
      console.log(`${TAG} @routeConfig: ${containerPropertyKey} - ${v.path}`)
      Container.set(`DECORATOR_ROUTE_${identifier}`, v)
      const { method, path, operationId } = v
      // set default operationId
      if (!operationId) {
        v.operationId = methodName
      }
      descriptor.value.routeConfig = v
      const apiList: Partial<StrongRouteConfig>[] = Container.get(DECORATOR_REQUEST) || []
      const routerConfig: Partial<RouteConfig> = { method, path, operationId }
      const decorator: RouteConfigDecorator = { className, methodName, identifier }
      const api: Partial<StrongRouteConfig> = { ...routerConfig, ...decorator }
      apiList.push(api)
      Container.set(DECORATOR_REQUEST, apiList)
      return descriptor
    }

/**
 * 装饰器函数 @body
 * @param v ZodObject<any>
 * @returns (target: any, name: string, descriptor: PropertyDescriptor) => PropertyDescriptor
 */
const body
  = (v: ZodObject<any>) =>
    (target: any, methodName: string, descriptor: PropertyDescriptor) => {
      const containerPropertyKey = `DECORATOR_BODY_${getIdentifier(target, methodName)}`
      console.log(`${TAG} @body: ${containerPropertyKey}`)
      descriptor.value.bodySchema = v
      Container.set(containerPropertyKey, v)
      return descriptor
    }

/**
 * 装饰器函数 @responses
 * @param v ZodObject<any>
 * @returns (target: any, name: string, descriptor: PropertyDescriptor) => PropertyDescriptor
 */
const responses
  = (v: ZodObject<any> | ZodString | string | SchemaObject | ReferenceObject) =>
    (target: any, methodName: string, descriptor: PropertyDescriptor) => {
      const containerPropertyKey = `DECORATOR_RESPONSES_${getIdentifier(target, methodName)}`
      console.log(`${TAG} @responses: ${containerPropertyKey}`)
      descriptor.value.responsesSchema = v
      Container.set(containerPropertyKey, v)
      return descriptor
    }
/**
 * 中间件 @middlewares
 * @param v ZodObject<any>
 * @returns (target: any, name: string, descriptor: PropertyDescriptor) => PropertyDescriptor
 */
const middlewares
  = (middlewares: Function[]) =>
    (target: any, name: string, descriptor: PropertyDescriptor) => {
      Container.set(
      `DECORATOR_MIDDLEWARES_${getIdentifier(target, name)}`,
      middlewares,
      )
      descriptor.value.middlewares = middlewares
      return descriptor
    }

export { controller, routeConfig, body, responses, middlewares }
