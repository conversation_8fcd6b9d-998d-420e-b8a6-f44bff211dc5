import process from 'node:process'
import { existsSync, mkdirSync, writeFileSync } from 'node:fs'
import path from 'node:path'
import type { OpenAPIObjectConfig } from '@asteasolutions/zod-to-openapi/dist/v3.0/openapi-generator'
import type { RouterOptions } from '@koa/router'
import type { OpenAPIObject } from 'openapi3-ts/oas30'

import Router from '@koa/router'
import is from 'is-type-of'
import type {
  OpenAPIRegistry,
  RouteConfig,
} from '@asteasolutions/zod-to-openapi'
import type { Context, Middleware } from 'koa'
import type { ZodTypeAny } from 'zod'
import { Container } from './utils/container'
import {
  CONFIG_SYMBOL,
  convertPath,
  reservedMethodNames,
} from './utils/constant'
import swaggerHTML from './swagger-html'
import { prepareDocs } from './swagger-builder'
import { registry } from './registry'
import { z } from '.'

export interface ItemMeta {
  controllerConfig: RouteConfig
  bodySchema?: ZodTypeAny
  responsesSchema?: ZodTypeAny
  routeConfig: RouteConfig
  middlewares?: Middleware[]
}
export interface SwaggerRouterConfig {
  swaggerJsonEndpoint?: string
  swaggerHtmlEndpoint?: string
  validateResponse?: boolean
  validateRequest?: boolean
  spec?: Partial<OpenAPIObjectConfig>
  registryOpenApi?: (registry: OpenAPIRegistry, zod: typeof z) => void
  globalSchemas?: Record<string, ZodTypeAny>
}
export interface DumpOptions {
  dir?: string
  fileName?: string
}
const TAG = 'swagger-router===>'

class SwaggerRouter<StateT = any, CustomT = Record<string, any>> extends Router<
  StateT,
  CustomT
> {
  config: SwaggerRouterConfig
  registry: OpenAPIRegistry
  DEFAULT_SWAGGER_JSON_ENDPOINT = '/swagger.json'
  DEFAULT_SWAGGER_HTML_ENDPOINT = '/swagger.html'
  docs: { object: OpenAPIObject, json: string }
  constructor(config: SwaggerRouterConfig = {}, opts: RouterOptions = {}) {
    super(opts)
    config.swaggerJsonEndpoint = config.swaggerJsonEndpoint ?? this.DEFAULT_SWAGGER_JSON_ENDPOINT
    config.swaggerHtmlEndpoint = config.swaggerHtmlEndpoint ?? this.DEFAULT_SWAGGER_HTML_ENDPOINT
    config.validateRequest = true

    this.config = config
    this.registry = registry
    console.log(`Container size => ${Container.size}`)
    Container.set(CONFIG_SYMBOL, config)
  }

  clear() {
    Container.clear()
  }

  applyRoute(SwaggerClass: any) {
    const SwaggerClassPrototype = SwaggerClass.prototype
    const methods = Object.getOwnPropertyNames(SwaggerClassPrototype)
      .filter((method) => !reservedMethodNames.includes(method))
      .map((method) => {
        console.log(TAG, 'applyRoute method', method)
        const wrapperMethod = async (ctx, ...args) => {
          const c = new SwaggerClass(ctx)
          await c[method](ctx, ...args)
        }
        // 添加了一层 wrapper 之后，需要把原函数的名称暴露出来 fnName
        // wrapperMethod 继承原函数的 descriptors
        const descriptors = Object.getOwnPropertyDescriptors(
          SwaggerClassPrototype[method],
        )
        Object.defineProperties(wrapperMethod, {
          fnName: {
            value: method,
            enumerable: true,
            writable: true,
            configurable: true,
          },
          ...descriptors,
        })
        return wrapperMethod
      });
    ([...methods] as any)
      // filter methods withour @request decorator
      .filter((item: ItemMeta) => {
        const { routeConfig } = item
        if (!routeConfig) {
          return false
        }
        const { method, path } = routeConfig
        if (!path && !method) {
          return false
        }
        return true
      })
      // add router
      .forEach((item: ItemMeta) => {
        const { routeConfig, bodySchema, responsesSchema } = item
        const { middlewares = [] } = item

        if (!is.array(middlewares)) {
          throw new Error('middlewares params must be an array or function')
        }
        middlewares.forEach((item: Function) => {
          if (!is.function(item)) {
            throw new Error('item in middlewares must be a function')
          }
        })

        const validationMid = async (ctx: Context, next: any) => {
          ctx._swagger_decorator_meta = item
          ctx.parsed = {
            query: ctx.request.query,
            params: (ctx.request as any)?.params,
            body: ctx.request.body,
          }
          if (this.config.validateRequest) {
            if (routeConfig.request?.query) {
              ctx.parsed.query = routeConfig.request?.query.parse(
                ctx.request.query,
              )
            }
            if (routeConfig.request?.params) {
              ctx.parsed.params = routeConfig.request?.params.parse(
                (ctx.request as any).params,
              )
            }

            if (bodySchema) {
              ctx.parsed.body = bodySchema.parse(ctx.request.body)
            }

            // merge body to parsed
            if (ctx.request.body) {
              Object.assign(ctx.parsed.body || {}, ctx.request.body)
            }
          }

          await next()
          // if (this.config.validateResponse) {
          //   if (responsesSchema) {
          //     responsesSchema.parse()
          //   }
          // }
        }

        const chain: [any] = [`${convertPath(`${routeConfig.path}`)}`]
        chain.push(...middlewares)
        chain.push(validationMid)
        chain.push((ctx) => (item as any)(ctx, ctx.parsed))
        this[routeConfig.method](...chain)
      })
    return this
  }

  swagger() {
    // registry openApi
    this.config.registryOpenApi && this.config.registryOpenApi(registry, z)
    // prepare docs
    this.docs = prepareDocs(this.opts.prefix, this.config.globalSchemas)

    // add router-> swagger.json
    this.get(this.config.swaggerJsonEndpoint!, (ctx) => {
      ctx.body = this.docs.object
    })

    // add router-> swagger html(注意这个是自己制作的html,还有一个通过koa-swagger-ui插件创建的html)
    this.get(this.config.swaggerHtmlEndpoint!, (ctx) => {
      const endpoint = this.opts.prefix
        ? `${this.opts.prefix}${this.config.swaggerJsonEndpoint}`
        : this.config.swaggerJsonEndpoint!
      ctx.body = swaggerHTML(endpoint)
    })
  }

  // 导出swagger.json --> file
  exportSwaggerJson({ fileName = this.config.swaggerJsonEndpoint, dir = '' }) {
    this.handleDumpSwaggerJSON({ dir, fileName })
  }

  handleDumpSwaggerJSON = (dumpOptions: DumpOptions) => {
    const { dir = process.cwd(), fileName = this.config.swaggerJsonEndpoint || this.DEFAULT_SWAGGER_JSON_ENDPOINT } = dumpOptions
    const filePath = path.join(dir, fileName)
    console.log(TAG, '导出swagger.json->', filePath)
    // 检查路径是否存在
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true })
    }
    writeFileSync(filePath, this.docs.json)
  }
}

export { SwaggerRouter }
