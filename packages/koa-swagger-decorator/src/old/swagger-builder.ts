import type { RouteConfig } from '@asteasolutions/zod-to-openapi'
import {
  OpenApiGeneratorV3,
} from '@asteasolutions/zod-to-openapi'
import deepmerge from 'deepmerge'
import type { ZodTypeAny } from 'zod'
import { globalSchemas } from './global'
import { registry } from './registry'
import { Container } from './utils/container'
import {
  CONFIG_SYMBOL,
  DECORATOR_REQUEST,
  DECORATOR_SCHEMAS,
} from './utils/constant'
import type { ControllerConfig, RouteConfigDecorator, StrongRouteConfig } from './type'
import { generateReferenceObject, refLink } from './utils/swagger'
import { firstUpperCase } from './utils'

const TAG = 'swagger-builder===>'

function handleRouteConfig(routeConfig: Partial<RouteConfig>, decorator: RouteConfigDecorator) {
  const { identifier } = decorator
  const meta
    = (Container.get(`DECORATOR_ROUTE_${identifier}`) as Partial<RouteConfig>)
    ?? {}

  // 原始的routeConfig
  const obj = {
    ...meta,
  }
  delete obj.path
  delete obj.method
  return deepmerge(routeConfig, obj)
}

function handleBody(routeConfig: Partial<RouteConfig>, decorator: RouteConfigDecorator) {
  const { methodName, identifier } = decorator
  // const containerPropertyKey = `DECORATOR_BODY_${getIdentifier(target, methodName)}`
  const containerPropertyKey = `DECORATOR_BODY_${identifier}`
  console.log(`${TAG} @body: ${containerPropertyKey}`)
  const bodyMeta = Container.get(containerPropertyKey)
  if (bodyMeta) {
    const refId = `${methodName}Request`
    collectSchemas(refId, bodyMeta)
    const bodyConfig = {
      body: {
        content: {
          'application/json': {
            schema: {
              // $ref
              $ref: refLink({ refId }),
            },
          },
        },
        required: true,
      },
    }
    routeConfig.request = {
      ...routeConfig.request,
      ...bodyConfig,
    }
  }
}

function handleResponse(routeConfig: Partial<RouteConfig>, decorator: RouteConfigDecorator) {
  const { methodName, identifier } = decorator
  const responsesMeta = Container.get(`DECORATOR_RESPONSES_${identifier}`)
  if (responsesMeta) {
    let dataRefId: string
    let rRefId: string
    // responsesMeta 如果是 string 则为globalSchemas中的schemas
    if (typeof responsesMeta === 'string') {
      // responsesMeta 为 globalSchemas 中的 key(schema)
      dataRefId = responsesMeta
      rRefId = generateReferenceObject(registry, dataRefId)
    } else {
      // methodName + 'VO' 为自定义的schemas,首字母大写
      dataRefId = `${firstUpperCase(methodName || '')}VO`
      rRefId = generateReferenceObject(registry, dataRefId)
      collectSchemas(dataRefId, responsesMeta)
    }

    // TODO 处理非 200 状态码
    const responsesConfig = {
      responses: {
        200: {
          description: 'success',
          content: {
            '*/*': {
              schema: {
                // $ref
                $ref: refLink({ refId: rRefId }),
              },
            },
          },
        },
      },
    }
    routeConfig.responses = {
      ...routeConfig.responses,
      ...responsesConfig.responses,
    }
  }
}

/**
 * 收集所有的 schemas
 */
function collectSchemas(refId: string, zodSchema: any, force = false) {
  if (!refId) {
    return
  }
  // 是否已经存在function
  function isExist(id: string) {
    return meta.some((o) => o.refId === id)
  }
  const meta = Container.get(DECORATOR_SCHEMAS) ?? []

  // 强制替换
  if (force && isExist(refId)) {
    console.log(TAG, `collectSchemas Schemas ${meta.length}`, refId, '强制替换')
    meta.splice(meta.findIndex((o) => o.refId === refId), 1)
  } else if (!force && isExist(refId)) {
    console.log(TAG, `collectSchemas Schemas ${meta.length}`, refId, '已经存在，则不再添加')
    return
  }
  meta.push({
    refId,
    zodSchema,
  })
  console.log(TAG, `collectSchemas Schemas ${meta.length}`, refId, '已经添加')
  Container.set(DECORATOR_SCHEMAS, meta)
}
function handleGlobalSchemas(globals?: Record<string, ZodTypeAny>) {
  // 内置的 schemas
  for (const key in globalSchemas) {
    if (Object.prototype.hasOwnProperty.call(globalSchemas, key)) {
      const element = globalSchemas[key]
      console.log(TAG, `collectSchemas 1 global Schemas `, key)
      collectSchemas(key, element)
    }
  }
  // 外部传入的 schemas (可以覆盖内置的 schemas)
  if (globals) {
    for (const key in globals) {
      if (Object.prototype.hasOwnProperty.call(globals, key)) {
        const element = globals[key]
        console.log(TAG, `collectSchemas 2 global Schemas `, key)
        collectSchemas(key, element, true) // 强制添加覆盖
      }
    }
  }
}
/**
 * 添加所有的 schemas
 */
function handleSchemas() {
  const meta = Container.get(DECORATOR_SCHEMAS)
  if (meta) {
    console.log(TAG, `register Schemas `, meta.length, JSON.stringify(meta.map((o) => o.refId)))

    for (const o of meta) {
      console.log(TAG, `register Schemas `, o.refId)
      if (o.zodSchema && o.zodSchema.openapi) {
        // register zod schema
        registry.register(o.refId, o.zodSchema)
      } else {
        // register component schemas
        registry.registerComponent('schemas', o.refId, o.zodSchema)
      }
    }
  }
}

export function handlerContainer(className: string, routeConfig: Partial<RouteConfig>) {
  const containerPropertyKey = `DECORATOR_CONTROLLER_${className}`
  const containerConfig: ControllerConfig = Container.get(containerPropertyKey) || {
    paths: {},
    components: {},
    tags: [],
  }
  console.log(TAG, `@containerConfig: ${className}`)

  // container paths 参数
  routeConfig.parameters = containerConfig.paths?.parameters || []

  // 全局的参数
  const commonParameters = containerConfig.components?.parameters || {}
  const routeConfigParameters = Object.entries(commonParameters || {}).map(([key, value]) => {
    return value
  })

  // 合并
  return deepmerge(routeConfig, {
    //
    parameters: routeConfigParameters,
    tags: containerConfig.tags,
  })
}

/**
 * 解析所有的 api
 * @param prefix
 * @param globals
 * @returns {Promise<{json: string; object: OpenAPIObject}>}
 */
export function prepareDocs(prefix?: string, globals?: Record<string, ZodTypeAny>) {
  console.log(TAG, `prepareDocs start `)
  const apiList: Partial<StrongRouteConfig>[] = Container.has(DECORATOR_REQUEST)
    ? Container.get(DECORATOR_REQUEST)
    : []

  // 添加全局公共的Schemas
  handleGlobalSchemas(globals)
  // 处理所有的 api
  for (const api of apiList) {
    const { method, path, identifier } = api
    console.log(TAG, `prepareDocs api ${method}-[${identifier}]`, api)
    const routePath = prefix ? `${prefix}${path}` : path
    let routeConfig: Partial<RouteConfig> = {
      path: routePath,
      method,
      request: {},
      responses: {
        200: {
          description: '成功响应',
        },
      },
    }
    // 处理container装饰器接受的公共配置
    api.className && (routeConfig = handlerContainer(api.className, routeConfig))
    const decorator: RouteConfigDecorator = {
      className: api.className,
      methodName: firstUpperCase(api.methodName || ''),
      identifier: api.identifier,
    }

    // 处理 body
    handleBody(routeConfig, decorator)
    // 处理 responses
    handleResponse(routeConfig, decorator)
    // 注册 swagger 路由 https://github.com/asteasolutions/zod-to-openapi
    registry.registerPath(handleRouteConfig(routeConfig, decorator))
  }
  // 注册所有的 schemas
  handleSchemas()

  const g = new OpenApiGeneratorV3(registry.definitions)
  const spec = Container.get(CONFIG_SYMBOL).spec ?? {}
  const object = g.generateDocument({
    openapi: '3.0.0',
    info: {
      version: '1.0',
      title: 'Swagger OpenAPI',
    },
    ...spec,
  })
  const json = JSON.stringify(object, null, 2)
  return {
    json,
    object,
  }
}
