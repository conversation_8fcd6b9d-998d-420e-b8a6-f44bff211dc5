/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-09 19:26:09
 * @LastEditTime: 2024-04-22 14:44:54
 * @LastEditors: shao<PERSON>
 * @Description:
 */
import type { ZodTypeAny } from 'zod'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-09 11:34:14
 * @LastEditTime: 2024-04-09 19:01:18
 * @LastEditors: shao<PERSON>
 * @Description: 全局公用的类型定义
 */

export interface SchemaMeta {
  refId: string
  zodSchema: ZodTypeAny
}

export const globalSchemas: Record<string, ZodTypeAny> = {
}
