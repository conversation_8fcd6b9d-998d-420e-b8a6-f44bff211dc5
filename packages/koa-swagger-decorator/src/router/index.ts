import type { RouterOptions } from '@koa/router'
import type { Middleware } from 'koa'
import type { OpenAPIObject } from 'openapi3-ts/oas30'
import type {
  ControllerClass,
  DumpOptions,
  HttpMethod,
  SwaggerRouterConfig,
  SwaggerUIConfig,
} from '../types'
import { existsSync, mkdirSync, writeFileSync } from 'node:fs'
import path from 'node:path'
import process from 'node:process'
import Router from '@koa/router'
import deepmerge from 'deepmerge'
import { z } from '../'
import { generateSwaggerDoc } from '../builder'
import { useRegistry } from '../registry'
import { METADATA_KEYS } from '../types'
import { createCustomSwaggerUI, createSwaggerUI } from '../ui'
import { mergeSwaggerRouterConfig, mergeSwaggerUIConfig } from '../utils/config-merger'
import { logger } from '../utils/logger'
/*
 * @Author: shaojun
 * @Date: 2024-12-25 13:12:53
 * @LastEditTime: 2024-12-25 13:24:12
 * @LastEditors: shaojun
 * @Description:
 */
// 使用从 types 导入的接口，移除重复定义
const { getRegistry, resetRegistry } = useRegistry()

export class SwaggerRouter<
  StateT = any,
  CustomT = Record<string, any>,
> extends Router<StateT, CustomT> {
  controllerClasses: ControllerClass[] = []
  config: SwaggerRouterConfig
  DEFAULT_SWAGGER_JSON_ENDPOINT = '/swagger.json'
  DEFAULT_SWAGGER_HTML_ENDPOINT = '/swagger'
  docs: { object: OpenAPIObject, json: string } | undefined
  constructor(config: SwaggerRouterConfig = {}, opts: RouterOptions = {}) {
    super(opts)

    // 使用配置合并策略
    this.config = mergeSwaggerRouterConfig(config)
  }

  applyRoute(SwaggerControllerClass: ControllerClass) {
    // 验证传入的控制器类
    if (!SwaggerControllerClass) {
      logger.error(`❌ applyRoute: 控制器类为空或未定义`)
      return
    }

    if (typeof SwaggerControllerClass !== 'function') {
      logger.error(`❌ applyRoute: 传入的不是一个构造函数，类型为: ${typeof SwaggerControllerClass}`, SwaggerControllerClass)
      return
    }

    const controllerName = SwaggerControllerClass.name || 'Unknown'
    logger.debug(`📝 应用控制器: ${controllerName}`)

    // 检查控制器是否有装饰器元数据
    const controllerMeta = Reflect.getMetadata(METADATA_KEYS.CONTROLLER, SwaggerControllerClass)
    if (controllerMeta) {
      logger.debug(`✅ 控制器 ${controllerName} 有装饰器元数据:`, controllerMeta)
    } else {
      logger.warn(`⚠️ 控制器 ${controllerName} 没有 @controller 装饰器元数据`)
    }

    // 检查原型链中的方法元数据
    const methodNames = Object.getOwnPropertyNames(SwaggerControllerClass.prototype)
    const routeMethods = methodNames.filter((name) => {
      if (name === 'constructor') {
        return false
      }
      const routeMeta = Reflect.getMetadata(METADATA_KEYS.ROUTE_CONFIG, SwaggerControllerClass.prototype, name)
      return !!routeMeta
    })

    logger.debug(`📋 控制器 ${controllerName} 发现 ${routeMethods.length} 个路由方法: ${routeMethods.join(', ')}`)
    logger.debug('🔍 SwaggerControllerClass---------------->', SwaggerControllerClass, JSON.stringify(routeMethods, null, 2))
    // 输出元数据
    routeMethods.forEach((methodName) => {
      const routeMeta = Reflect.getMetadata(METADATA_KEYS.ROUTE_CONFIG, SwaggerControllerClass.prototype, methodName)
      logger.debug(`📋 控制器 ${controllerName} 方法 ${methodName} 的元数据:`, routeMeta)
    })

    logger.debug('🔍 SwaggerControllerClass----------------> end')

    this.controllerClasses.push(SwaggerControllerClass)
  }

  clear() {
    resetRegistry()
  }

  /**
   * 注册控制器路由到Koa Router
   */
  private registerControllerRoutes() {
    this.controllerClasses.forEach((controllerClass) => {
      this.registerControllerMethods(controllerClass)
    })
  }

  /**
   * 注册单个控制器的所有方法
   */
  private registerControllerMethods(ControllerClass: ControllerClass) {
    // 验证controllerClass是否为有效的构造函数
    if (!ControllerClass) {
      logger.error(`❌ 控制器类为空或未定义`)
      return
    }

    if (typeof ControllerClass !== 'function') {
      logger.error(`❌ 控制器不是一个构造函数，类型为: ${typeof ControllerClass}`)
      return
    }

    const controllerName = ControllerClass.name || 'Unknown'
    logger.verbose(`📂 注册控制器: ${controllerName}`)

    try {
      // 创建控制器实例
      const controllerInstance = new ControllerClass()

      // 遍历控制器的所有方法
      Object.getOwnPropertyNames(ControllerClass.prototype).forEach((methodName) => {
        if (methodName === 'constructor') {
          return
        }

        // 获取路由配置元数据
        const routeConfigMeta = Reflect.getMetadata(
          METADATA_KEYS.ROUTE_CONFIG,
          ControllerClass.prototype,
          methodName,
        )

        if (!routeConfigMeta) {
          return
        }

        // 构建完整路径（Koa Router 的 prefix 机制 会自动包含前缀）
        const fullPath = routeConfigMeta.path

        // 获取控制器方法
        const handler = controllerInstance[methodName].bind(controllerInstance)

        // 注册路由到Koa Router
        const method = routeConfigMeta.method.toLowerCase() as HttpMethod
        switch (method) {
          case 'get':
            this.get(fullPath, handler)
            break
          case 'post':
            this.post(fullPath, handler)
            break
          case 'put':
            this.put(fullPath, handler)
            break
          case 'patch':
            this.patch(fullPath, handler)
            break
          case 'delete':
            this.delete(fullPath, handler)
            break
          case 'options':
            this.options(fullPath, handler)
            break
          case 'head':
            this.head(fullPath, handler)
            break
          default:
            logger.warn(`❌ 不支持的HTTP方法: ${method}`)
            return
        }

        logger.verbose(`  ├─ ${method.toUpperCase().padEnd(6)} ${fullPath}`)
      })
    } catch (error) {
      logger.error(`❌ 注册控制器 ${controllerName} 时发生错误:`, error)
    }
  }

  swagger() {
    // 创建新的registry确保状态隔离
    resetRegistry()

    // registry openApi
    this.config.registryOpenApi
    && this.config.registryOpenApi(getRegistry(), z)
    // prepare docs
    this.docs = generateSwaggerDoc(
      getRegistry(),
      this.controllerClasses,
      this.opts.prefix,
      this.config.globalSchemas,
      this.config.spec,
    )

    // 注册实际的API路由
    logger.start('注册 Koa 路由...')
    this.registerControllerRoutes()

    // add router-> swagger.json
    this.get(this.config.swaggerJsonEndpoint!, (ctx) => {
      ctx.body = this.docs?.object
    })

    // 显示友好的访问提示
    const jsonEndpoint = this.opts.prefix
      ? `${this.opts.prefix}${this.config.swaggerJsonEndpoint}`
      : this.config.swaggerJsonEndpoint!
    const htmlEndpoint = this.config.swaggerHtmlEndpoint!

    logger.success('📖 Swagger 文档已就绪')
    logger.info(`   JSON规范: ${jsonEndpoint}`)
    logger.info(`   UI界面: ${htmlEndpoint}; 需要手动调用 createSwaggerUI() 开启UI`)
    logger.ready('🎉 SwaggerRouter 初始化完成！')
  }

  // 导出swagger.json --> file
  exportSwaggerJson({ fileName = this.config.swaggerJsonEndpoint, dir = '' }) {
    this.handleDumpSwaggerJSON({ dir, fileName })
  }

  handleDumpSwaggerJSON = (dumpOptions: DumpOptions) => {
    const {
      dir = process.cwd(),
      fileName = this.config.swaggerJsonEndpoint
        || this.DEFAULT_SWAGGER_JSON_ENDPOINT,
    } = dumpOptions
    const filePath = path.join(dir, fileName)
    logger.info(`Exporting swagger.json to: ${filePath}`)
    // 检查路径是否存在
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true })
    }
    writeFileSync(filePath, this.docs?.json || '')
  }

  /**
   * 创建 Swagger UI 中间件
   * @param customConfig 自定义 SwaggerUI 配置（可选）
   * @returns Koa 中间件
   */
  createSwaggerUI(customConfig?: Partial<SwaggerUIConfig>): Middleware {
    // 计算 JSON 端点的完整路径
    const jsonEndpoint = this.opts.prefix
      ? `${this.opts.prefix}${this.config.swaggerJsonEndpoint}`
      : this.config.swaggerJsonEndpoint!

    // 使用配置合并策略
    const baseConfig = this.config.swaggerUIConfig || {}
    const mergedConfig = mergeSwaggerUIConfig(customConfig || {})

    // 确保 URL 设置正确
    const finalConfig = {
      ...mergedConfig,
      swaggerOptions: {
        ...mergedConfig.swaggerOptions,
        url: customConfig?.swaggerOptions?.url || jsonEndpoint,
      },
    }

    const swaggerUIMiddleware = createSwaggerUI(finalConfig, jsonEndpoint)

    this.get(this.config.swaggerHtmlEndpoint!, swaggerUIMiddleware)

    return swaggerUIMiddleware
  }

  /**
   * 创建自定义 Swagger UI 中间件
   * @param options 完整的 koa2-swagger-ui 配置
   * @returns Koa 中间件
   */
  createCustomSwaggerUI(options: any): Middleware {
    return createCustomSwaggerUI(options)
  }

  /**
   * 获取 Swagger UI 配置信息
   * @returns 配置信息对象
   */
  getSwaggerUIInfo() {
    const jsonEndpoint = this.opts.prefix
      ? `${this.opts.prefix}${this.config.swaggerJsonEndpoint}`
      : this.config.swaggerJsonEndpoint!

    const htmlEndpoint = this.config.swaggerHtmlEndpoint!
    const uiEndpoint = this.config.swaggerUIConfig?.routePrefix || '/swagger'

    return {
      jsonEndpoint,
      htmlEndpoint,
      uiEndpoint,
      enabledUI: !!this.config.swaggerUIConfig?.routePrefix,
      config: this.config.swaggerUIConfig,
    }
  }
}
