# Koa-Swagger-Decorator 配置指南

## 目录

- [SwaggerRouter 配置](#swaggerrouter-配置)
- [控制器配置](#控制器配置)
- [路由配置](#路由配置)
- [响应配置](#响应配置)
- [Swagger UI 配置](#swagger-ui-配置)
- [配置最佳实践](#配置最佳实践)

## SwaggerRouter 配置

`SwaggerRouter` 是 koa-swagger-decorator 的核心组件，负责注册控制器、生成 Swagger 文档和提供 Swagger UI。

### 基础配置

| 配置项 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| `swaggerJsonEndpoint` | `string` | `/swagger.json` | Swagger JSON 文档的访问路径 |
| `swaggerHtmlEndpoint` | `string` | `/swagger` | Swagger UI 界面的访问路径 |
| `validateRequest` | `boolean` | `true` | 是否验证请求参数 |
| `validateResponse` | `boolean` | `false` | 是否验证响应数据 |

### OpenAPI 规范配置

通过 `spec` 配置项可以自定义 OpenAPI 文档的基本信息：

```typescript
const router = new SwaggerRouter({
  spec: {
    info: {
      title: 'My API',
      version: '1.0.0',
      description: 'API 文档描述'
    },
    servers: [
      { url: 'https://api.example.com', description: '生产环境' },
      { url: 'https://staging.example.com', description: '测试环境' }
    ]
  }
});
```

### 全局 Schema 配置

通过 `globalSchemas` 配置项可以定义全局可复用的 Schema：

```typescript
import { z } from 'zod';

const UserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email()
});

const ErrorSchema = z.object({
  code: z.number(),
  message: z.string()
});

const router = new SwaggerRouter({
  globalSchemas: {
    User: UserSchema,
    Error: ErrorSchema
  }
});
```

### 完整配置示例

```typescript
import { SwaggerRouter } from 'koa-swagger-decorator';

const router = new SwaggerRouter({
  // Swagger 文档端点
  swaggerJsonEndpoint: '/api/docs.json',
  swaggerHtmlEndpoint: '/api/docs',
  
  // 验证选项
  validateRequest: true,
  validateResponse: true,
  
  // OpenAPI 规范配置
  spec: {
    info: {
      title: 'My API',
      version: '1.0.0',
      description: 'API 文档描述'
    },
    servers: [
      { url: 'https://api.example.com', description: '生产环境' }
    ]
  },
  
  // Swagger UI 配置
  swaggerUIConfig: {
    routePrefix: '/api/docs',
    title: 'API 文档',
    hideTopbar: true,
    swaggerOptions: {
      docExpansion: 'list',
      persistAuthorization: true
    }
  },
  
  // 全局 Schema
  globalSchemas: {
    User: UserSchema,
    Error: ErrorSchema
  }
}, {
  // Koa Router 选项
  prefix: '/api/v1'
});
```

## 控制器配置

控制器装饰器 `@Controller` 用于标记一个类为 API 控制器，并提供控制器级别的配置。

### 配置选项

| 配置项 | 类型 | 描述 |
|-------|------|------|
| `tags` | `string[]` | API 标签数组，用于 Swagger 分组 |
| `paths.parameters` | `ParameterObject[]` | 应用于所有路由的参数 |
| `components` | `ComponentsObject` | OpenAPI 组件定义 |

### 示例

```typescript
import { Controller } from 'koa-swagger-decorator';

@Controller({
  tags: ['用户管理'],
  paths: {
    parameters: [{
      name: 'Authorization',
      in: 'header',
      required: true,
      schema: { type: 'string' },
      description: '认证令牌'
    }]
  }
})
export class UserController {
  // 控制器方法...
}
```

## 路由配置

路由装饰器 `@RouteConfig` 用于定义 API 路由的基本信息。

### 配置选项

| 配置项 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| `method` | `HttpMethod` | 是 | HTTP 方法 |
| `path` | `string` | 是 | 路由路径 |
| `summary` | `string` | 否 | 接口摘要 |
| `description` | `string` | 否 | 接口详细描述 |
| `tags` | `string[]` | 否 | 接口标签 |
| `deprecated` | `boolean` | 否 | 是否已弃用 |

### 示例

```typescript
import { Controller, RouteConfig } from 'koa-swagger-decorator';

@Controller({ tags: ['用户管理'] })
export class UserController {
  @RouteConfig({
    method: 'post',
    path: '/users',
    summary: '创建用户',
    description: '创建一个新用户并返回用户信息'
  })
  async createUser(ctx) {
    // 实现...
  }
  
  @RouteConfig({
    method: 'get',
    path: '/users/:id',
    summary: '获取用户信息',
    deprecated: false
  })
  async getUser(ctx) {
    // 实现...
  }
}
```

## 响应配置

响应装饰器 `@Responses` 用于定义 API 的响应格式。

### 单一响应格式

```typescript
import { Controller, RouteConfig, Responses, z } from 'koa-swagger-decorator';

@Controller({})
export class UserController {
  @RouteConfig({
    method: 'get',
    path: '/users/:id'
  })
  @Responses(z.object({
    id: z.number(),
    name: z.string(),
    email: z.string().email()
  }))
  async getUser(ctx) {
    // 实现...
  }
}
```

### 多状态码响应格式

```typescript
import { Controller, RouteConfig, Responses, z } from 'koa-swagger-decorator';

@Controller({})
export class UserController {
  @RouteConfig({
    method: 'get',
    path: '/users/:id'
  })
  @Responses({
    '200': {
      description: '成功获取用户信息',
      schema: z.object({
        id: z.number(),
        name: z.string(),
        email: z.string().email()
      })
    },
    '404': {
      description: '用户不存在',
      schema: z.object({
        code: z.number(),
        message: z.string()
      })
    },
    '500': {
      description: '服务器错误'
    }
  })
  async getUser(ctx) {
    // 实现...
  }
}
```

## Swagger UI 配置

Swagger UI 配置用于自定义 API 文档界面的外观和行为。

### 配置选项

| 配置项 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| `routePrefix` | `string` | `/swagger` | Swagger UI 的访问路径 |
| `title` | `string` | `API Documentation` | 页面标题 |
| `hideTopbar` | `boolean` | `false` | 是否隐藏顶部栏 |
| `swaggerOptions.docExpansion` | `'list' \| 'full' \| 'none'` | `'list'` | 文档展开方式 |
| `swaggerOptions.persistAuthorization` | `boolean` | `true` | 是否持久化授权信息 |
| `swaggerOptions.supportedSubmitMethods` | `HttpMethod[]` | 所有方法 | 支持的提交方法 |
| `swaggerOptions.defaultModelRendering` | `'example' \| 'model' \| 'schema'` | `'schema'` | 默认模型渲染方式 |

### 示例

```typescript
import { SwaggerRouter } from 'koa-swagger-decorator';

const router = new SwaggerRouter({
  swaggerUIConfig: {
    routePrefix: '/api/docs',
    title: '我的 API 文档',
    hideTopbar: true,
    swaggerOptions: {
      docExpansion: 'list',
      persistAuthorization: true,
      supportedSubmitMethods: ['get', 'post', 'put', 'delete'],
      defaultModelRendering: 'model'
    }
  }
});

// 创建 Swagger UI
app.use(router.createSwaggerUI());
```

## 配置最佳实践

### 1. 使用类型系统

利用 TypeScript 类型系统确保配置的正确性：

```typescript
import { SwaggerRouter, SwaggerRouterConfig } from 'koa-swagger-decorator';

const config: SwaggerRouterConfig = {
  // 配置项...
};

const router = new SwaggerRouter(config);
```

### 2. 组织 Schema 定义

将常用的 Schema 定义抽取为单独的模块：

```typescript
// schemas.ts
import { z } from 'zod';

export const UserSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email()
});

export const ErrorSchema = z.object({
  code: z.number(),
  message: z.string()
});

// router.ts
import { SwaggerRouter } from 'koa-swagger-decorator';
import * as schemas from './schemas';

const router = new SwaggerRouter({
  globalSchemas: {
    User: schemas.UserSchema,
    Error: schemas.ErrorSchema
  }
});
```

### 3. 环境特定配置

根据不同环境使用不同的配置：

```typescript
import { SwaggerRouter } from 'koa-swagger-decorator';

const isDev = process.env.NODE_ENV === 'development';

const router = new SwaggerRouter({
  validateResponse: isDev, // 仅在开发环境验证响应
  swaggerUIConfig: {
    hideTopbar: !isDev // 在生产环境隐藏顶部栏
  }
});
```

### 4. 路由前缀与 Swagger 路径

确保 Swagger 路径与路由前缀保持一致：

```typescript
import { SwaggerRouter } from 'koa-swagger-decorator';

const API_PREFIX = '/api/v1';

const router = new SwaggerRouter({
  swaggerJsonEndpoint: `${API_PREFIX}/swagger.json`,
  swaggerHtmlEndpoint: `${API_PREFIX}/docs`,
  swaggerUIConfig: {
    routePrefix: `${API_PREFIX}/docs`
  }
}, {
  prefix: API_PREFIX
});
```

### 5. 配置验证

使用库提供的验证函数确保配置正确：

```typescript
import { SwaggerRouter } from 'koa-swagger-decorator';
import { validateSwaggerRouterConfig } from 'koa-swagger-decorator/config';

const config = {
  // 配置项...
};

const validationResult = validateSwaggerRouterConfig(config);
if (!validationResult.valid) {
  console.error('配置错误:', validationResult.errors);
  process.exit(1);
}

const router = new SwaggerRouter(config);
```