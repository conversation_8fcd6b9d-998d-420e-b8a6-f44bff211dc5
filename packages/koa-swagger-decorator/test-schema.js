import { extendZodWithOpenApi } from '@asteasolutions/zod-to-openapi';
import { z } from 'zod';

// 扩展 zod
extendZodWithOpenApi(z);

// 测试不同类型的 schema
console.log('=== 测试 Schema 检查逻辑 ===');

// 1. 普通的 Zod Schema（没有 openapi 扩展）
const plainSchema = z.object({
  name: z.string(),
  age: z.number(),
});

console.log('1. 普通 Schema:');
console.log('  - hasOpenapi:', !!(plainSchema.openapi));
console.log('  - hasOpenapiMethod:', typeof plainSchema.openapi === 'function');
console.log('  - hasZodDef:', !!(plainSchema._def));
console.log('  - zodType:', plainSchema._def?.typeName);

// 2. 带有 openapi 扩展的 Schema
const openapiSchema = z.object({
  name: z.string().openapi({ description: '姓名' }),
  age: z.number().openapi({ description: '年龄' }),
}).openapi({ description: '用户信息' });

console.log('\n2. 带 openapi 扩展的 Schema:');
console.log('  - hasOpenapi:', !!(openapiSchema.openapi));
console.log('  - hasOpenapiMethod:', typeof openapiSchema.openapi === 'function');
console.log('  - hasZodDef:', !!(openapiSchema._def));
console.log('  - zodType:', openapiSchema._def?.typeName);

// 3. 只在字段级别使用 openapi 的 Schema
const fieldOpenapiSchema = z.object({
  name: z.string().openapi({ description: '姓名' }),
  age: z.number().openapi({ description: '年龄' }),
});

console.log('\n3. 字段级 openapi 扩展的 Schema:');
console.log('  - hasOpenapi:', !!(fieldOpenapiSchema.openapi));
console.log('  - hasOpenapiMethod:', typeof fieldOpenapiSchema.openapi === 'function');
console.log('  - hasZodDef:', !!(fieldOpenapiSchema._def));
console.log('  - zodType:', fieldOpenapiSchema._def?.typeName);

// 4. 测试检查函数
function isZodSchemaWithOpenApi(schema) {
  // 检查是否有 _def 属性（Zod schema 的标识）
  if (!schema || !schema._def) {
    return false;
  }

  // 检查是否有 openapi 方法或属性
  const hasOpenapiMethod = typeof schema.openapi === 'function';
  const hasOpenapiProperty = schema.openapi && typeof schema.openapi === 'object';
  
  return hasOpenapiMethod || hasOpenapiProperty;
}

console.log('\n=== 检查函数测试结果 ===');
console.log('1. 普通 Schema:', isZodSchemaWithOpenApi(plainSchema));
console.log('2. 带 openapi 扩展的 Schema:', isZodSchemaWithOpenApi(openapiSchema));
console.log('3. 字段级 openapi 扩展的 Schema:', isZodSchemaWithOpenApi(fieldOpenapiSchema));

// 5. 测试实际的 AppInfo schema 类型
console.log('\n=== 模拟 AppInfo Schema ===');
const AppInfoLikeSchema = z.object({
  id: z.number().openapi({ description: '应用ID' }),
  platform: z.string().openapi({
    description: '平台类型',
    example: 'iOS',
  }),
  bundleId: z.string().openapi({
    description: '包标识符',
    example: 'com.example.app',
  }),
});

console.log('AppInfo 类似的 Schema:');
console.log('  - hasOpenapi:', !!(AppInfoLikeSchema.openapi));
console.log('  - hasOpenapiMethod:', typeof AppInfoLikeSchema.openapi === 'function');
console.log('  - hasZodDef:', !!(AppInfoLikeSchema._def));
console.log('  - zodType:', AppInfoLikeSchema._def?.typeName);
console.log('  - isZodSchemaWithOpenApi:', isZodSchemaWithOpenApi(AppInfoLikeSchema));
