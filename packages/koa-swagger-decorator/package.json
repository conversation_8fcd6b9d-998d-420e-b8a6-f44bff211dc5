{"name": "@cfe-node/koa-swagger-decorator", "type": "module", "version": "1.0.0", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./cfe.config.schema.json": {"default": "./dist/cfe.config.schema.json"}, "./dist/*": "./dist/*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["LICENSE", "README.md", "dist"], "scripts": {"dev": "pnpm run stub", "stub": "unbuild --stub", "build": "unbuild", "build:lib": "unbuild", "build:unbuild": "unbuild", "test": "vitest run tests/ --watch", "client:typegen": "typegen http://localhost:3000/api/swagger.json > example/client/openapi.d.ts", "lint": "eslint src/**/*.ts", "lint-fix": "pnpm lint --fix", "beta": "pnpm publish --tag beta", "latest": "pnpm publish"}, "dependencies": {"@asteasolutions/zod-to-openapi": "^7.0.0", "@koa/router": "^12.0.1", "@types/koa__router": "^12.0.4", "consola": "^3.4.2", "deepmerge": "^4.3.1", "koa": "^2.15.2", "koa2-swagger-ui": "^5.10.0", "reflect-metadata": "^0.2.2", "zod": "^3.22.4"}, "devDependencies": {"@koa/bodyparser": "^5.0.0", "@koa/cors": "^5.0.0", "@types/deepmerge": "^2.2.0", "@types/koa": "^2.15.0", "@types/koa__cors": "^5.0.0", "axios": "^1.6.8", "jsdom": "^24.0.0", "openapi-client-axios": "^7.5.4", "openapi-client-axios-typegen": "^7.6.0", "tsx": "^4.7.1", "typescript": "^5.2.2"}, "import": "./dist/index.ts"}