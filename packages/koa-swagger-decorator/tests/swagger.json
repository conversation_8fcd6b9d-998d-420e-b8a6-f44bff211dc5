{"openapi": "3.0.0", "info": {"title": "Example API Server", "version": "v1.0"}, "components": {"schemas": {"R_UserInfo_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/UserInfo"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_List_UserInfo__": {"type": "object", "properties": {"data": {"type": "array", "description": "数据载荷", "items": {"$ref": "#/components/schemas/UserInfo"}}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "R_StringData_": {"type": "object", "properties": {"resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}, "data": {"type": "string", "example": ""}}, "required": ["resultCode", "resultMessage", "data"]}, "R_SearchDetailVO_": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/SearchDetailVO"}, "resultCode": {"type": "number", "example": 200}, "resultMessage": {"type": "string", "example": "success"}}, "required": ["resultCode", "resultMessage", "data"]}, "SearchDetailVO": {"properties": {"user": {"$ref": "#/components/schemas/UserInfo"}, "app": {"$ref": "#/components/schemas/AppInfo"}}}, "StringData": {"type": "string", "example": ""}, "UserInfo": {"type": "object", "properties": {"id": {"type": "string", "example": "123"}, "username": {"type": "string", "example": "<PERSON>"}, "nickname": {"type": "string", "example": "<PERSON>"}, "avatar": {"type": "string", "example": ""}, "token": {"type": "string", "example": "123456"}}, "required": ["id", "username", "token"]}, "AppInfo": {"type": "object", "properties": {"id": {"type": "string"}, "platform": {"type": "string"}, "bundleId": {"type": "string"}, "bundleName": {"type": "string"}, "appName": {"type": "string"}, "currentVersion": {"type": "string"}}, "required": ["id", "platform", "bundleId", "bundleName", "appName", "currentVersion"]}, "UserLoginRequest": {"type": "object", "properties": {"username": {"type": "string", "example": "<PERSON>"}, "password": {"type": "string", "example": "abcdefg"}}, "required": ["username", "password"]}}, "parameters": {}}, "paths": {"/api/user/getUser": {"post": {"parameters": [{"name": "X-USER-AURH", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "ALL-PARAMS", "in": "header", "required": true, "schema": {"type": "string"}}], "tags": ["用户管理模块"], "summary": "create a user", "operationId": "GetUser", "responses": {"200": {"description": "success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R_UserInfo_"}}}}}}}, "/api/user/getUserList": {"post": {"parameters": [{"name": "X-USER-AURH", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "ALL-PARAMS", "in": "header", "required": true, "schema": {"type": "string"}}], "tags": ["用户管理模块", "USER"], "summary": "create user list", "operationId": "GetUserList", "responses": {"200": {"description": "success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R_List_UserInfo__"}}}}}}}, "/api/user/login": {"post": {"parameters": [{"name": "X-USER-AURH", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "ALL-PARAMS", "in": "header", "required": true, "schema": {"type": "string"}}], "tags": ["用户管理模块", "USER", "LOGIN"], "summary": "login a user", "operationId": "UserLogin", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}}}, "responses": {"200": {"description": "success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R_UserInfo_"}}}}}}}, "/api/user/getVerificationCode/{email}/{password}": {"get": {"parameters": [{"name": "X-USER-AURH", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "ALL-PARAMS", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "email", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "password", "in": "path", "required": true, "schema": {"type": "string"}}], "tags": ["用户管理模块"], "summary": "get verification code", "operationId": "GetVerificationCode", "responses": {"200": {"description": "success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}}}}, "/api/user/getApiToken": {"post": {"parameters": [{"name": "X-USER-AURH", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "ALL-PARAMS", "in": "header", "required": true, "schema": {"type": "string"}}], "tags": ["用户管理模块"], "summary": "create a user", "operationId": "GetApiToken", "responses": {"200": {"description": "success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R_StringData_"}}}}}}}, "/api/user/searchDetail": {"post": {"parameters": [{"name": "X-USER-AURH", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "ALL-PARAMS", "in": "header", "required": true, "schema": {"type": "string"}}], "tags": ["用户管理模块"], "summary": "create a user", "operationId": "searchDetail", "responses": {"200": {"description": "success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R_SearchDetailVO_"}}}}}}}}}