/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-04-12 16:30:05
 * @LastEditTime: 2024-04-22 14:46:09
 * @LastEditors: shao<PERSON>
 * @Description:
 */
import { z } from '../../src/index'
// 泛型函数R
export function R<T extends z.ZodTypeAny>(dataSchema: T) {
  return z.object({
    resultCode: z.number().openapi({ example: 200 }),
    resultMessage: z.string().openapi({ example: 'success' }),
    data: dataSchema,
  })
}
// 泛型函数LIST
export function LIST<T extends z.ZodTypeAny>(dataSchema: T) {
  return z.array(dataSchema)
}

// 泛型函数RList
export function RList<T extends z.ZodTypeAny>(dataSchema: T) {
  return z.object({
    resultCode: z.number().openapi({ example: 200 }),
    resultMessage: z.string().openapi({ example: 'success' }),
    data: LIST(dataSchema),
  })
}
// 定义泛型类型
export type RType<T extends z.ZodTypeAny> = z.infer<ReturnType<typeof R<T>>>
export type ListType<T extends z.ZodTypeAny> = z.infer<ReturnType<typeof LIST<T>>>
export type RListType<T extends z.ZodTypeAny> = z.infer<ReturnType<typeof RList<T>>>
