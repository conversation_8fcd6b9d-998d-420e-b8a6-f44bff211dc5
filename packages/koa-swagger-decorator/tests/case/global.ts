import type { SchemaObject } from 'openapi3-ts/oas30'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-09 11:34:14
 * @LastEditTime: 2025-07-09 19:47:16
 * @LastEditors: shaojun
 * @Description: 全局公用的类型定义
 */
import type { ZodTypeAny } from 'zod'

import { refLink, z } from '../../src/index'

// 定义全局的schema>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
export const StringData = z.string().openapi({ example: '' })

export const R_StringData_ = z.object({
  resultCode: z.number().openapi({ example: 200 }),
  resultMessage: z.string().openapi({ example: 'success' }),
  data: z.string().openapi({ example: '' }),
})

export type IRStringData = Zod.infer<typeof R_StringData_>

export const UserInfo = z.object({
  id: z.string().openapi({ example: '123' }),
  username: z.string().openapi({ example: '<PERSON>' }),
  nickname: z.string().optional().openapi({ example: '<PERSON> Doe' }),
  avatar: z.string().optional().openapi({ example: '' }),
  token: z.string().openapi({ example: '123456' }),
})

export const AppInfo = z.object({
  id: z.string(),
  platform: z.string(),
  bundleId: z.string(),
  bundleName: z.string(),
  appName: z.string(),
  currentVersion: z.string(),
})

export const DetailInfo = z.object({
  user: UserInfo,
  app: AppInfo,
})

export const DetailInfoSchema: SchemaObject = {
  properties: {
    user: {
      $ref: refLink({ refId: 'UserInfo' }),
    },
    app: {
      $ref: refLink({ refId: 'AppInfo' }),
    },
  },
}

// 枚举所有全局schema的key
export enum GlobalSchemasKeyEnum {
  R_StringData_ = 'R_StringData_',
  StringData = 'StringData',
  UserInfo = 'UserInfo',
  List_UserInfo_ = 'List_UserInfo_', // List<UserInfo> 不需要定义globalSchemas
  AppInfo = 'AppInfo',
  List_AppInfo = 'List_AppInfo',
}
export const globalSchemas: Partial<Record<GlobalSchemasKeyEnum, ZodTypeAny>> = {
  R_StringData_,
  StringData,
  UserInfo,
  AppInfo,
}

export type GlobalSchemasKeysType = keyof typeof globalSchemas
export type GlobalSchemasValueType<K extends keyof typeof globalSchemas> = typeof globalSchemas[K]
