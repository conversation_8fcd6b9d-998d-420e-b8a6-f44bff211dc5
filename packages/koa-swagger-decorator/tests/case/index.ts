/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-03-21 18:41:57
 * @LastEditTime: 2024-12-24 14:38:14
 * @LastEditors: shaojun
 * @Description:
 */

// file -> ./controller/user.ts
import type { Context } from 'koa'

import type { ControllerConfig, ParsedArgs, z } from '../../src'
import type { StringData, UserInfo } from './global'

import type { RListType, RType } from './tools'
import { Body, Controller, Responses, RouteConfig } from '../../src'
import { DetailInfoSchema, GlobalSchemasKeyEnum } from './global'
import {
  UserLoginReq,
} from './type'

const controllerConfig: ControllerConfig = {
  paths: {
    // 全局参数
    parameters: [
      {
        name: 'X-USER-AURH',
        in: 'header',
        required: true,
        schema: {
          type: 'string',
        },
      },
    ],
  },
  components: {
    // 全局参数
    parameters: {
      'ALL-PARAMS': {
        name: 'ALL-PARAMS',
        in: 'header',
        required: true,
        schema: {
          type: 'string',
        },
      },
    },
  },
  tags: ['用户管理模块'],
  // define your API route info using @routeConfig decorator
}

@Controller(controllerConfig)
class UserController {
  @RouteConfig({
    // define your API route info using @routeConfig decorator
    method: 'post',
    path: '/user/getUser',
    summary: 'create a user',
    operationId: 'GetUser',
  })
  @Responses(GlobalSchemasKeyEnum.UserInfo)
  async GetUser(ctx: Context, args) {
    console.log(args, args.body?.uid, args.body?.name)
    ctx.body = {
      id: '1',
      username: 'abc',
      nickname: 'abc',
      avatar: '',
      token: '123456',
    } as z.infer<typeof UserInfo>
  }

  @RouteConfig({
    // define your API route info using @routeConfig decorator
    method: 'post',
    path: '/user/getUserList',
    summary: 'create user list',
    tags: ['USER'],
    operationId: 'GetUserList',
  })
  @Responses(GlobalSchemasKeyEnum.List_UserInfo_)
  async GetUserList(ctx: Context, args) {
    console.log(args, args.body?.uid, args.body?.name)
    ctx.body = {
      resultCode: 200,
      resultMessage: '',
      data: [
        {
          id: '1',
          username: 'abc',
          nickname: 'abc',
          avatar: '',
          token: '123456',
        },
      ],
    } as RListType<typeof UserInfo>
  }

  @RouteConfig({
    method: 'post',
    path: '/user/login',
    summary: 'login a user',
    tags: ['USER', 'LOGIN'],
    operationId: 'UserLogin',
  })
  @Body(UserLoginReq)
  @Responses(GlobalSchemasKeyEnum.UserInfo)
  async UserLogin(ctx: Context, args: ParsedArgs<z.infer<typeof UserLoginReq>>) {
    console.log(args, args.body?.username, args.body?.password)
    const values = UserLoginReq.parse(ctx.request.body)
    ctx.body = {
      resultCode: 200,
      resultMessage: '',
      data: {
        id: '1',
        username: 'abc',
        nickname: 'abc',
        avatar: '',
        token: '123456',
      },
    } as RType<typeof UserInfo>
  }

  @RouteConfig({
    method: 'get',
    path: '/user/getVerificationCode/{email}/{password}',
    summary: 'get verification code',
    parameters: [
      {
        name: 'email',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
        },
      },
      {
        name: 'password',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
        },
      },
    ],
    operationId: 'GetVerificationCode',
  })
  @Responses(GlobalSchemasKeyEnum.StringData)
  async GetVerificationCode(ctx: Context, args) {
    console.log('args', args)
    // const values = GetVerificationCodeRes.parse(ctx.request.body)
    ctx.body = { resultCode: 200, resultMessage: '', data: '123456' } as RType<typeof StringData>
  }

  @RouteConfig({
    // define your API route info using @routeConfig decorator
    method: 'post',
    path: '/user/getApiToken',
    summary: 'create a user',
    operationId: 'GetApiToken',
  })
  @Responses(GlobalSchemasKeyEnum.StringData)
  async GetApiToken(ctx: Context, args) {
    console.log(args, args.body?.uid, args.body?.name)
    ctx.body = { resultCode: 200, resultMessage: '', data: '123456' } as RType<typeof StringData>
  }

  @RouteConfig({
    // define your API route info using @routeConfig decorator
    method: 'post',
    path: '/user/searchDetail',
    summary: 'create a user',
  })
  @Responses(DetailInfoSchema)
  async searchDetail(ctx: Context, args) {
    console.log(args, args.body?.uid, args.body?.name)
    // ctx.body = { user: {}, app: {} } as z.infer<typeof DetailInfo>
  }
}

export { UserController }
export * from './global'
export * from './type'
