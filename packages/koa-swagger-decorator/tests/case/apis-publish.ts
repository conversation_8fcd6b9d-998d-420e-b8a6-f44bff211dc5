import type { CJiaAxiosRequestConfig } from '@cfe-components/global'
import { BaseApi } from '@cfe-components/global'

/**
 *
 * @export
 * @interface AppInfo
 */
export interface AppInfo {
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  platform: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  bundleId: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  bundleName: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  appName: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  currentVersion: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  creatorId?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  creator?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  createAt?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  icon?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  describe?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  updateAt?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  shortUrl?: string
  /**
   *
   * @type {boolean}
   * @memberof AppInfo
   */
  autoPublish?: boolean
  /**
   *
   * @type {boolean}
   * @memberof AppInfo
   */
  installWithPwd?: boolean
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  installPwd?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  appLevel: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  ownerId?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  changelog?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  updateMode?: AppInfoUpdateModeEnum
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  releaseVersionCode?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  releaseVersionId?: string
  /**
   *
   * @type {string}
   * @memberof AppInfo
   */
  grayReleaseVersionId?: string
  /**
   *
   * @type {number}
   * @memberof AppInfo
   */
  totalDownloadCount?: number
  /**
   *
   * @type {AppInfoTodayDownloadCount}
   * @memberof AppInfo
   */
  todayDownloadCount?: AppInfoTodayDownloadCount
  /**
   *
   * @type {AppInfoGrayStrategy}
   * @memberof AppInfo
   */
  grayStrategy?: AppInfoGrayStrategy
}

/**
 * @export
 * @enum {string}
 */
export enum AppInfoUpdateModeEnum {
  Silent = 'silent',
  Normal = 'normal',
  Force = 'force',
}

/**
 *
 * @export
 * @interface AppInfoGrayStrategy
 */
export interface AppInfoGrayStrategy {
  /**
   *
   * @type {string}
   * @memberof AppInfoGrayStrategy
   */
  ipType?: AppInfoGrayStrategyIpTypeEnum
  /**
   *
   * @type {number}
   * @memberof AppInfoGrayStrategy
   */
  count?: number
  /**
   *
   * @type {Array<string>}
   * @memberof AppInfoGrayStrategy
   */
  ipList?: Array<string>
  /**
   *
   * @type {number}
   * @memberof AppInfoGrayStrategy
   */
  downloadCountLimit?: number
  /**
   *
   * @type {string}
   * @memberof AppInfoGrayStrategy
   */
  updateMode?: AppInfoGrayStrategyUpdateModeEnum
}

/**
 * @export
 * @enum {string}
 */
export enum AppInfoGrayStrategyIpTypeEnum {
  White = 'white',
  Black = 'black',
}
/**
 * @export
 * @enum {string}
 */
export enum AppInfoGrayStrategyUpdateModeEnum {
  Silent = 'silent',
  Normal = 'normal',
  Force = 'force',
}

/**
 *
 * @export
 * @interface AppInfoTodayDownloadCount
 */
export interface AppInfoTodayDownloadCount {
  /**
   *
   * @type {string}
   * @memberof AppInfoTodayDownloadCount
   */
  date: string
  /**
   *
   * @type {number}
   * @memberof AppInfoTodayDownloadCount
   */
  count: number
}
/**
 *
 * @export
 * @interface BuildInfo
 */
export interface BuildInfo {
  /**
   *
   * @type {string}
   * @memberof BuildInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof BuildInfo
   */
  platform: string
  /**
   *
   * @type {string}
   * @memberof BuildInfo
   */
  bundleId: string
  /**
   *
   * @type {number}
   * @memberof BuildInfo
   */
  buildNo: number
  /**
   *
   * @type {string}
   * @memberof BuildInfo
   */
  uploadAt?: string
}
/**
 *
 * @export
 * @interface DownloadInfo
 */
export interface DownloadInfo {
  /**
   *
   * @type {string}
   * @memberof DownloadInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof DownloadInfo
   */
  remark: string
  /**
   *
   * @type {string}
   * @memberof DownloadInfo
   */
  image: string
  /**
   *
   * @type {string}
   * @memberof DownloadInfo
   */
  param: string
  /**
   *
   * @type {string}
   * @memberof DownloadInfo
   */
  page: string
}
/**
 * 上传文件
 * @export
 * @interface InlineObject
 */
export interface InlineObject {
  /**
   * file content
   * @type {any}
   * @memberof InlineObject
   */
  uploadFile: any
}
/**
 * 上传文件
 * @export
 * @interface InlineObject1
 */
export interface InlineObject1 {
  /**
   * file content
   * @type {any}
   * @memberof InlineObject1
   */
  uploadFile: any
  /**
   * file分类，子目录；不传文件直接存放当前用户id文件夹根目录；
   * @type {string}
   * @memberof InlineObject1
   */
  classify?: string
}
/**
 *
 * @export
 * @interface InviteInfo
 */
export interface InviteInfo {
  /**
   *
   * @type {string}
   * @memberof InviteInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof InviteInfo
   */
  userName: string
  /**
   *
   * @type {string}
   * @memberof InviteInfo
   */
  teamId: string
  /**
   *
   * @type {Array<InviteInfoEmails>}
   * @memberof InviteInfo
   */
  emails: Array<InviteInfoEmails>
  /**
   *
   * @type {string}
   * @memberof InviteInfo
   */
  type: InviteInfoTypeEnum
  /**
   *
   * @type {string}
   * @memberof InviteInfo
   */
  status: InviteInfoStatusEnum
  /**
   *
   * @type {string}
   * @memberof InviteInfo
   */
  createAt?: string
}

/**
 * @export
 * @enum {string}
 */
export enum InviteInfoTypeEnum {
  TEAM = 'TEAM',
}
/**
 * @export
 * @enum {string}
 */
export enum InviteInfoStatusEnum {
  EBL = 'EBL',
  DBL = 'DBL',
}

/**
 *
 * @export
 * @interface InviteInfoEmails
 */
export interface InviteInfoEmails {
  /**
   *
   * @type {string}
   * @memberof InviteInfoEmails
   */
  email: string
  /**
   *
   * @type {string}
   * @memberof InviteInfoEmails
   */
  status: InviteInfoEmailsStatusEnum
}

/**
 * @export
 * @enum {string}
 */
export enum InviteInfoEmailsStatusEnum {
  EBL = 'EBL',
  DBL = 'DBL',
}

/**
 *
 * @export
 * @interface MessageInfo
 */
export interface MessageInfo {
  /**
   *
   * @type {string}
   * @memberof MessageInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof MessageInfo
   */
  category?: string
  /**
   *
   * @type {string}
   * @memberof MessageInfo
   */
  content?: string
  /**
   *
   * @type {string}
   * @memberof MessageInfo
   */
  sender?: string
  /**
   *
   * @type {string}
   * @memberof MessageInfo
   */
  receiver?: string
  /**
   *
   * @type {string}
   * @memberof MessageInfo
   */
  sendAt?: string
  /**
   *
   * @type {string}
   * @memberof MessageInfo
   */
  status?: string
  /**
   *
   * @type {string}
   * @memberof MessageInfo
   */
  data?: string
}
/**
 *
 * @export
 * @interface MiniappInfo
 */
export interface MiniappInfo {
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  appName: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  appId: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  platform: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  pagePath: string
  /**
   *
   * @type {boolean}
   * @memberof MiniappInfo
   */
  appEnv: boolean
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  ownerId: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  creatorId: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  creator: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  appSecret?: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  createAt?: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  icon?: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  describe?: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  updateAt?: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfo
   */
  changelog?: string
  /**
   *
   * @type {Array<MiniappInfoDownloadCodeImage>}
   * @memberof MiniappInfo
   */
  downloadCodeImage: Array<MiniappInfoDownloadCodeImage>
}
/**
 *
 * @export
 * @interface MiniappInfoDownloadCodeImage
 */
export interface MiniappInfoDownloadCodeImage {
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  type: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  image: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  env: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  createAt: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  developer: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  version: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  desc: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  pagePath: string
  /**
   *
   * @type {string}
   * @memberof MiniappInfoDownloadCodeImage
   */
  searchQuery: string
}
/**
 *
 * @export
 * @interface RListAppInfo
 */
export interface RListAppInfo {
  /**
   * 数据载荷
   * @type {Array<AppInfo>}
   * @memberof RListAppInfo
   */
  data: Array<AppInfo>
  /**
   *
   * @type {number}
   * @memberof RListAppInfo
   */
  resultCode: number
  /**
   *
   * @type {string}
   * @memberof RListAppInfo
   */
  resultMessage: string
}
/**
 *
 * @export
 * @interface RListUserInfo
 */
export interface RListUserInfo {
  /**
   * 数据载荷
   * @type {Array<UserInfo>}
   * @memberof RListUserInfo
   */
  data: Array<UserInfo>
  /**
   *
   * @type {number}
   * @memberof RListUserInfo
   */
  resultCode: number
  /**
   *
   * @type {string}
   * @memberof RListUserInfo
   */
  resultMessage: string
}
/**
 *
 * @export
 * @interface RStringData
 */
export interface RStringData {
  /**
   *
   * @type {number}
   * @memberof RStringData
   */
  resultCode: number
  /**
   *
   * @type {string}
   * @memberof RStringData
   */
  resultMessage: string
  /**
   *
   * @type {string}
   * @memberof RStringData
   */
  data: string
}
/**
 *
 * @export
 * @interface RUploadAppVO
 */
export interface RUploadAppVO {
  /**
   *
   * @type {UploadAppVO}
   * @memberof RUploadAppVO
   */
  data: UploadAppVO
  /**
   *
   * @type {number}
   * @memberof RUploadAppVO
   */
  resultCode: number
  /**
   *
   * @type {string}
   * @memberof RUploadAppVO
   */
  resultMessage: string
}
/**
 *
 * @export
 * @interface RUploadFileVO
 */
export interface RUploadFileVO {
  /**
   *
   * @type {UploadFileVO}
   * @memberof RUploadFileVO
   */
  data: UploadFileVO
  /**
   *
   * @type {number}
   * @memberof RUploadFileVO
   */
  resultCode: number
  /**
   *
   * @type {string}
   * @memberof RUploadFileVO
   */
  resultMessage: string
}
/**
 *
 * @export
 * @interface RUserInfo
 */
export interface RUserInfo {
  /**
   *
   * @type {UserInfo}
   * @memberof RUserInfo
   */
  data: UserInfo
  /**
   *
   * @type {number}
   * @memberof RUserInfo
   */
  resultCode: number
  /**
   *
   * @type {string}
   * @memberof RUserInfo
   */
  resultMessage: string
}
/**
 *
 * @export
 * @interface TeamInfo
 */
export interface TeamInfo {
  /**
   *
   * @type {string}
   * @memberof TeamInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof TeamInfo
   */
  name?: string
  /**
   *
   * @type {string}
   * @memberof TeamInfo
   */
  icon?: string
  /**
   *
   * @type {boolean}
   * @memberof TeamInfo
   */
  isDefault?: boolean
  /**
   *
   * @type {string}
   * @memberof TeamInfo
   */
  creatorId?: string
  /**
   *
   * @type {string}
   * @memberof TeamInfo
   */
  createAt?: string
  /**
   *
   * @type {Array<TeamInfoMembers>}
   * @memberof TeamInfo
   */
  members?: Array<TeamInfoMembers>
}
/**
 *
 * @export
 * @interface TeamInfoMembers
 */
export interface TeamInfoMembers {
  /**
   *
   * @type {string}
   * @memberof TeamInfoMembers
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof TeamInfoMembers
   */
  username?: string
  /**
   *
   * @type {string}
   * @memberof TeamInfoMembers
   */
  email?: string
  /**
   *
   * @type {string}
   * @memberof TeamInfoMembers
   */
  role?: TeamInfoMembersRoleEnum
  /**
   *
   * @type {string}
   * @memberof TeamInfoMembers
   */
  userAvatar?: string
}

/**
 * @export
 * @enum {string}
 */
export enum TeamInfoMembersRoleEnum {
  Owner = 'owner',
  Manager = 'manager',
  Guest = 'guest',
}

/**
 *
 * @export
 * @interface UploadAppVO
 */
export interface UploadAppVO {
  /**
   *
   * @type {UploadAppVOApp}
   * @memberof UploadAppVO
   */
  app: UploadAppVOApp
  /**
   *
   * @type {UploadAppVOVersion}
   * @memberof UploadAppVO
   */
  version: UploadAppVOVersion
}
/**
 *
 * @export
 * @interface UploadAppVOApp
 */
export interface UploadAppVOApp {
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  platform: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  bundleId: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  bundleName: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  appName: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  currentVersion: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  creatorId?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  creator?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  createAt?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  icon?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  describe?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  updateAt?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  shortUrl?: string
  /**
   *
   * @type {boolean}
   * @memberof UploadAppVOApp
   */
  autoPublish?: boolean
  /**
   *
   * @type {boolean}
   * @memberof UploadAppVOApp
   */
  installWithPwd?: boolean
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  installPwd?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  appLevel: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  ownerId?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  changelog?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  updateMode?: UploadAppVOAppUpdateModeEnum
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  releaseVersionCode?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  releaseVersionId?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOApp
   */
  grayReleaseVersionId?: string
  /**
   *
   * @type {number}
   * @memberof UploadAppVOApp
   */
  totalDownloadCount?: number
  /**
   *
   * @type {AppInfoTodayDownloadCount}
   * @memberof UploadAppVOApp
   */
  todayDownloadCount?: AppInfoTodayDownloadCount
  /**
   *
   * @type {AppInfoGrayStrategy}
   * @memberof UploadAppVOApp
   */
  grayStrategy?: AppInfoGrayStrategy
}

/**
 * @export
 * @enum {string}
 */
export enum UploadAppVOAppUpdateModeEnum {
  Silent = 'silent',
  Normal = 'normal',
  Force = 'force',
}

/**
 *
 * @export
 * @interface UploadAppVOVersion
 */
export interface UploadAppVOVersion {
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  appId?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  bundleId?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  icon?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  versionStr?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  versionCode?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  uploadAt?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  createAt?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  uploader?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  uploaderId?: string
  /**
   *
   * @type {number}
   * @memberof UploadAppVOVersion
   */
  size?: number
  /**
   *
   * @type {boolean}
   * @memberof UploadAppVOVersion
   */
  active?: boolean
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  downloadUrl?: string
  /**
   *
   * @type {number}
   * @memberof UploadAppVOVersion
   */
  downloadCount?: number
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  fileDownloadUrl?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  installUrl?: string
  /**
   *
   * @type {boolean}
   * @memberof UploadAppVOVersion
   */
  showOnDownloadPage?: boolean
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  appLevel?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  changelog?: string
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  md5?: string
  /**
   *
   * @type {boolean}
   * @memberof UploadAppVOVersion
   */
  hidden?: boolean
  /**
   *
   * @type {string}
   * @memberof UploadAppVOVersion
   */
  updateMode?: UploadAppVOVersionUpdateModeEnum
  /**
   *
   * @type {boolean}
   * @memberof UploadAppVOVersion
   */
  released?: boolean
}

/**
 * @export
 * @enum {string}
 */
export enum UploadAppVOVersionUpdateModeEnum {
  Silent = 'silent',
  Normal = 'normal',
  Force = 'force',
}

/**
 *
 * @export
 * @interface UploadFileVO
 */
export interface UploadFileVO {
  /**
   *
   * @type {string}
   * @memberof UploadFileVO
   */
  httpUrl: string
  /**
   *
   * @type {string}
   * @memberof UploadFileVO
   */
  relativeUrl: string
}
/**
 *
 * @export
 * @interface UserInfo
 */
export interface UserInfo {
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  username: string
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  password: string
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  userAvatar: string
  /**
   *
   * @type {Array<string>}
   * @memberof UserInfo
   */
  userAvatarHistory?: Array<string>
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  email: string
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  token: string
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  apiToken: string | null
  /**
   *
   * @type {Array<UserInfoTeams>}
   * @memberof UserInfo
   */
  teams: Array<UserInfoTeams>
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  mobile: string
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  qq: string
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  company: string
  /**
   *
   * @type {string}
   * @memberof UserInfo
   */
  career: string
}
/**
 *
 * @export
 * @interface UserInfoTeams
 */
export interface UserInfoTeams {
  /**
   *
   * @type {string}
   * @memberof UserInfoTeams
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof UserInfoTeams
   */
  name?: string
  /**
   *
   * @type {string}
   * @memberof UserInfoTeams
   */
  icon?: string
  /**
   *
   * @type {string}
   * @memberof UserInfoTeams
   */
  role?: UserInfoTeamsRoleEnum
}

/**
 * @export
 * @enum {string}
 */
export enum UserInfoTeamsRoleEnum {
  Owner = 'owner',
  Manager = 'manager',
  Guest = 'guest',
}

/**
 *
 * @export
 * @interface UserJiarLoginRequest
 */
export interface UserJiarLoginRequest {
  /**
   *
   * @type {string}
   * @memberof UserJiarLoginRequest
   */
  username: string
  /**
   *
   * @type {string}
   * @memberof UserJiarLoginRequest
   */
  password: string
}
/**
 *
 * @export
 * @interface UserLoginRequest
 */
export interface UserLoginRequest {
  /**
   *
   * @type {string}
   * @memberof UserLoginRequest
   */
  username: string
  /**
   *
   * @type {string}
   * @memberof UserLoginRequest
   */
  password: string
}
/**
 *
 * @export
 * @interface UserRegisterRequest
 */
export interface UserRegisterRequest {
  /**
   *
   * @type {string}
   * @memberof UserRegisterRequest
   */
  username: string
  /**
   *
   * @type {string}
   * @memberof UserRegisterRequest
   */
  password: string
  /**
   *
   * @type {string}
   * @memberof UserRegisterRequest
   */
  email: string
}
/**
 *
 * @export
 * @interface VersionInfo
 */
export interface VersionInfo {
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  id: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  appId?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  bundleId?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  icon?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  versionStr?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  versionCode?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  uploadAt?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  createAt?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  uploader?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  uploaderId?: string
  /**
   *
   * @type {number}
   * @memberof VersionInfo
   */
  size?: number
  /**
   *
   * @type {boolean}
   * @memberof VersionInfo
   */
  active?: boolean
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  downloadUrl?: string
  /**
   *
   * @type {number}
   * @memberof VersionInfo
   */
  downloadCount?: number
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  fileDownloadUrl?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  installUrl?: string
  /**
   *
   * @type {boolean}
   * @memberof VersionInfo
   */
  showOnDownloadPage?: boolean
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  appLevel?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  changelog?: string
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  md5?: string
  /**
   *
   * @type {boolean}
   * @memberof VersionInfo
   */
  hidden?: boolean
  /**
   *
   * @type {string}
   * @memberof VersionInfo
   */
  updateMode?: VersionInfoUpdateModeEnum
  /**
   *
   * @type {boolean}
   * @memberof VersionInfo
   */
  released?: boolean
}

/**
 * @export
 * @enum {string}
 */
export enum VersionInfoUpdateModeEnum {
  Silent = 'silent',
  Normal = 'normal',
  Force = 'force',
}

/**
 * 生成apitoken
 *
 * @param attachInfo  (required)
 * @param authorization  (required)
 * @return RStringData
 * @throws ApiException if fails to make API call
 */
export const apitoken = async (

  customConfig?: CJiaAxiosRequestConfig,
): Promise<RStringData> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/user/apitoken`
  return baseRequest.POST<RStringData>({
    url: `${localVarPath}`,
    data: {},
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
        authorization: '*',
      },
    }),
  })
}
/**
 * 获取某个应用详情
 *
 * @param attachInfo  (required)
 * @param authorization  (required)
 * @param teamId  (required)
 * @param appId  (required)
 * @return RListAppInfo
 * @throws ApiException if fails to make API call
 */
export const getAppDetail = async (
  teamId: string,
  appId: string,
  customConfig?: CJiaAxiosRequestConfig,
): Promise<RListAppInfo> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/apps/{teamId}/{appId}`
    .replace(`{${'teamId'}}`, encodeURIComponent(String(teamId)))
    .replace(`{${'appId'}}`, encodeURIComponent(String(appId)))
  return baseRequest.GET<RListAppInfo>({
    url: `${localVarPath}`,
    data: {},
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
        authorization: '*',
      },
    }),
  })
}
/**
 * 获取团队下App列表
 *
 * @param attachInfo  (required)
 * @param authorization  (required)
 * @param teamId  (required)
 * @return RListAppInfo
 * @throws ApiException if fails to make API call
 */
export const getApps = async (
  teamId: string,
  customConfig?: CJiaAxiosRequestConfig,
): Promise<RListAppInfo> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/apps/{teamId}`
    .replace(`{${'teamId'}}`, encodeURIComponent(String(teamId)))
  return baseRequest.GET<RListAppInfo>({
    url: `${localVarPath}`,
    data: {},
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
        authorization: '*',
      },
    }),
  })
}
/**
 * 获取用户信息
 *
 * @param attachInfo  (required)
 * @param authorization  (required)
 * @return RUserInfo
 * @throws ApiException if fails to make API call
 */
export const getUserInfo = async (

  customConfig?: CJiaAxiosRequestConfig,
): Promise<RUserInfo> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/user/info`
  return baseRequest.GET<RUserInfo>({
    url: `${localVarPath}`,
    data: {},
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
        authorization: '*',
      },
    }),
  })
}
/**
 * 获取所有用户
 *
 * @param attachInfo  (required)
 * @param authorization  (required)
 * @param type  (optional, default to undefined)
 * @return RListUserInfo
 * @throws ApiException if fails to make API call
 */
export const getUsers = async (
  type?: number,
  customConfig?: CJiaAxiosRequestConfig,
): Promise<RListUserInfo> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/users`
  return baseRequest.GET<RListUserInfo>({
    url: `${localVarPath}`,
    data: {},
    config: Object.assign(customConfig || {}, {
      params: { type },
      headers: {
        attachInfo: '*',
        authorization: '*',
      },
    }),
  })
}
/**
 * 上传apk或者ipa文件到服务器
 *
 * @param attachInfo  (required)
 * @param teamId  (required)
 * @param authorization  (required)
 * @param uploadFile file content (required)
 * @return RUploadAppVO
 * @throws ApiException if fails to make API call
 */
export const uploadApp = async (
  teamId: string,
  uploadFile: any,
  customConfig?: CJiaAxiosRequestConfig,
): Promise<RUploadAppVO> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/upload/{teamId}/uploadApp`
    .replace(`{${'teamId'}}`, encodeURIComponent(String(teamId)))
  return baseRequest.POST<RUploadAppVO>({
    url: `${localVarPath}`,
    data: {},
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
        authorization: '*',
      },
    }),
  })
}
/**
 * 上传文件
 *
 * @param attachInfo  (required)
 * @param authorization  (required)
 * @param uploadFile file content (required)
 * @param classify file分类，子目录；不传文件直接存放当前用户id文件夹根目录； (optional, default to undefined)
 * @return RUploadFileVO
 * @throws ApiException if fails to make API call
 */
export const uploadFile = async (
  uploadFile: any,
  classify?: string,
  customConfig?: CJiaAxiosRequestConfig,
): Promise<RUploadFileVO> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/upload/uploadFile`
  return baseRequest.POST<RUploadFileVO>({
    url: `${localVarPath}`,
    data: {},
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
        authorization: '*',
      },
    }),
  })
}
/**
 * jiar登录
 *
 * @param attachInfo  (required)
 * @param userJiarLoginRequest  (optional)
 * @return RUserInfo
 * @throws ApiException if fails to make API call
 */
export const userJiarLogin = async (
  userJiarLoginRequest?: UserJiarLoginRequest,
  customConfig?: CJiaAxiosRequestConfig,
): Promise<RUserInfo> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/user/jiarLogin`
  return baseRequest.POST<RUserInfo>({
    url: `${localVarPath}`,
    data: userJiarLoginRequest,
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
      },
    }),
  })
}
/**
 * 登录
 *
 * @param attachInfo  (required)
 * @param userLoginRequest  (optional)
 * @return RUserInfo
 * @throws ApiException if fails to make API call
 */
export const userLogin = async (
  userLoginRequest?: UserLoginRequest,
  customConfig?: CJiaAxiosRequestConfig,
): Promise<RUserInfo> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/user/login`
  return baseRequest.POST<RUserInfo>({
    url: `${localVarPath}`,
    data: userLoginRequest,
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
      },
    }),
  })
}
/**
 * 注册
 *
 * @param attachInfo  (required)
 * @param userRegisterRequest  (optional)
 * @return RStringData
 * @throws ApiException if fails to make API call
 */
export const userRegister = async (
  userRegisterRequest?: UserRegisterRequest,
  customConfig?: CJiaAxiosRequestConfig,
): Promise<RStringData> => {
  const baseRequest = await BaseApi.getInstance().createCommonRequestAsync()
  const localVarPath = `/api/user/register`
  return baseRequest.POST<RStringData>({
    url: `${localVarPath}`,
    data: userRegisterRequest,
    config: Object.assign(customConfig || {}, {
      params: { },
      headers: {
        attachInfo: '*',
      },
    }),
  })
}
