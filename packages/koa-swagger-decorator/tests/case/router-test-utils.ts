/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-01-09 19:45:00
 * @LastEditTime: 2025-07-11 08:34:00
 * @LastEditors: shaojun
 * @Description: SwaggerRouter测试状态隔离工具
 */

import type { SwaggerRouterConfig } from '../../src/router'
import { existsSync, mkdirSync, writeFileSync } from 'node:fs'
import { vi } from 'vitest'
import { CollectionSchemas } from '../../src/builder'
import { useRegistry } from '../../src/registry'
import { SwaggerRouter } from '../../src/router'

const { resetRegistry } = useRegistry()

/**
 * 测试状态管理器
 */
export class RouterTestStateManager {
  private static instance: RouterTestStateManager

  static getInstance(): RouterTestStateManager {
    if (!RouterTestStateManager.instance) {
      RouterTestStateManager.instance = new RouterTestStateManager()
    }
    return RouterTestStateManager.instance
  }

  /**
   * 完全重置所有状态
   */
  resetAllState(): void {
    // 1. 重置registry
    resetRegistry()

    // 2. 清空CollectionSchemas
    CollectionSchemas.length = 0

    // 3. 清理所有mock
    vi.clearAllMocks()

    // 4. 重置文件系统mock
    this.resetFileSystemMocks()

    // 5. 清理Reflect元数据缓存
    this.clearReflectMetadata()
  }

  /**
   * 重置文件系统mock
   */
  private resetFileSystemMocks(): void {
    try {
      (existsSync as any).mockReturnValue?.(true);
      (mkdirSync as any).mockImplementation?.(() => '');
      (writeFileSync as any).mockImplementation?.(() => {})
    } catch (error) {
      // Mock可能未正确设置，忽略错误
    }
  }

  /**
   * 清理Reflect元数据缓存
   */
  private clearReflectMetadata(): void {
    try {
      // 清理Reflect的内部元数据缓存
      if (typeof Reflect !== 'undefined' && (Reflect as any).deleteMetadata) {
        // 如果有deleteMetadata方法，尝试清理
      }

      // 清理全局变量中可能存在的缓存
      if (typeof globalThis !== 'undefined') {
        delete (globalThis as any).__REFLECT_METADATA__
      }

      if (typeof globalThis !== 'undefined') {
        delete (globalThis as any).__REFLECT_METADATA__
      }
    } catch (error) {
      // 忽略清理错误
    }
  }

  /**
   * 创建隔离的router实例
   */
  createIsolatedRouter(
    config: SwaggerRouterConfig = {},
    routerOptions: any = {},
  ): SwaggerRouter {
    // 确保状态已重置
    this.resetAllState()

    return new SwaggerRouter(config, routerOptions)
  }

  /**
   * 验证状态是否已清理
   */
  verifyStateClean(): boolean {
    return (
      CollectionSchemas.length === 0
      // 可以添加更多状态验证
      && true
    )
  }
}

/**
 * 测试装饰器：自动处理状态隔离
 */
export function withIsolatedState() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = function (...args: any[]) {
      const stateManager = RouterTestStateManager.getInstance()

      // 测试前重置状态
      stateManager.resetAllState()

      try {
        // 执行原测试方法
        const result = originalMethod.apply(this, args)

        // 如果是Promise，处理异步清理
        if (result && typeof result.then === 'function') {
          return result.finally(() => {
            stateManager.resetAllState()
          })
        }

        return result
      } finally {
        // 测试后清理状态
        stateManager.resetAllState()
      }
    }

    return descriptor
  }
}

/**
 * 创建测试专用的router配置
 */
export class RouterConfigBuilder {
  private config: SwaggerRouterConfig = {}

  withJsonEndpoint(endpoint: string): RouterConfigBuilder {
    this.config.swaggerJsonEndpoint = endpoint
    return this
  }

  withHtmlEndpoint(endpoint: string): RouterConfigBuilder {
    this.config.swaggerHtmlEndpoint = endpoint
    return this
  }

  withValidation(request = true, response = false): RouterConfigBuilder {
    this.config.validateRequest = request
    this.config.validateResponse = response
    return this
  }

  withGlobalSchemas(schemas: Record<string, any>): RouterConfigBuilder {
    this.config.globalSchemas = schemas
    return this
  }

  withSpec(spec: any): RouterConfigBuilder {
    this.config.spec = spec
    return this
  }

  withRegistryCallback(callback: (registry: any, zod: any) => void): RouterConfigBuilder {
    this.config.registryOpenApi = callback
    return this
  }

  build(): SwaggerRouterConfig {
    return { ...this.config }
  }

  reset(): RouterConfigBuilder {
    this.config = {}
    return this
  }
}

/**
 * 文件系统Mock助手
 */
export class FileSystemMockHelper {
  static mockExistingDirectory(): void {
    try {
      (existsSync as any).mockReturnValue?.(true);
      (mkdirSync as any).mockImplementation?.(() => '');
      (writeFileSync as any).mockImplementation?.(() => {})
    } catch (error) {
      // Mock可能未正确设置，忽略错误
    }
  }

  static mockNonExistingDirectory(): void {
    try {
      (existsSync as any).mockReturnValue?.(false);
      (mkdirSync as any).mockImplementation?.(() => '');
      (writeFileSync as any).mockImplementation?.(() => {})
    } catch (error) {
      // Mock可能未正确设置，忽略错误
    }
  }

  static verifyDirectoryCreated(expectedPath: string): void {
    try {
      expect(mkdirSync as any).toHaveBeenCalledWith?.(
        expectedPath,
        { recursive: true },
      )
    } catch (error) {
      // Mock验证失败，跳过
    }
  }

  static verifyFileWritten(expectedPath: string, expectedContent?: string): void {
    try {
      expect(writeFileSync as any).toHaveBeenCalledWith?.(
        expectedPath,
        expectedContent || expect.any(String),
      )
    } catch (error) {
      // Mock验证失败，跳过
    }
  }
}

/**
 * 断言助手
 */
export class RouterAssertHelper {
  static verifyRouterHasRoute(
    router: SwaggerRouter,
    path: string,
    method: string = 'GET',
  ): boolean {
    return router.stack.some((layer) =>
      layer.path === path
      && layer.methods.includes(method.toUpperCase()),
    )
  }

  static verifyDocumentGenerated(router: SwaggerRouter): void {
    expect(router.docs).toBeDefined()
    expect(router.docs?.object).toBeDefined()
    expect(router.docs?.json).toBeDefined()
    expect(router.docs?.object.openapi).toBe('3.0.0')
  }

  static verifyPathExists(router: SwaggerRouter, path: string): void {
    expect(router.docs?.object.paths[path]).toBeDefined()
  }

  static verifyPathMethod(
    router: SwaggerRouter,
    path: string,
    method: string,
  ): void {
    const pathObj = router.docs?.object.paths[path]
    expect(pathObj).toBeDefined()
    expect(pathObj[method.toLowerCase()]).toBeDefined()
  }
}

/**
 * 测试数据生成器
 */
export class TestDataGenerator {
  static createSimpleSpec(title: string = 'Test API'): any {
    return {
      info: {
        title,
        version: '1.0.0',
        description: 'Generated test API documentation',
      },
    }
  }

  static createGlobalSchemas(): Record<string, any> {
    return {
      TestSchema: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
        },
      },
    }
  }
}
