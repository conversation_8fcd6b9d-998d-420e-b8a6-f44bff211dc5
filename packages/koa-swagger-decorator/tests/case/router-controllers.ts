/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-01-09 19:45:00
 * @LastEditTime: 2025-07-09 19:49:38
 * @LastEditors: s<PERSON><PERSON>
 * @Description: SwaggerRouter测试专用控制器
 */

import { z } from 'zod'
import { Body, Controller, Responses, RouteConfig } from '../../src/decorators'

// ======================== 测试Schema定义 ========================

export const BasicUserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
})

export const UserCreateSchema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  age: z.number().min(0).max(120).optional(),
})

export const UserUpdateSchema = z.object({
  id: z.string(),
  name: z.string().min(1).optional(),
  email: z.string().email().optional(),
  age: z.number().min(0).max(120).optional(),
})

export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  total: z.number(),
})

export const UserListSchema = z.object({
  users: z.array(BasicUserSchema),
  pagination: PaginationSchema,
})

export const ErrorResponseSchema = z.object({
  code: z.number(),
  message: z.string(),
  details: z.any().optional(),
})

// ======================== 基础测试控制器 ========================

@Controller({
  tags: ['基础测试'],
})
export class BasicController {
  @RouteConfig({
    method: 'get',
    path: '/basic',
    summary: '基础测试接口',
    description: '最简单的测试接口',
  })
  async basic() {}
}

// ======================== 完整功能测试控制器 ========================

@Controller({
  tags: ['用户管理'],
  paths: {
    parameters: [
      {
        name: 'Authorization',
        in: 'header',
        required: true,
        schema: { type: 'string' },
        description: '认证令牌',
      },
    ],
  },
  components: {
    parameters: {
      CommonAuth: {
        name: 'X-Auth-Token',
        in: 'header',
        required: true,
        schema: { type: 'string' },
        description: '通用认证令牌',
      },
    },
  },
})
export class UserController {
  @RouteConfig({
    method: 'get',
    path: '/users',
    summary: '获取用户列表',
    description: '分页获取用户列表',
    parameters: {
      page: {
        name: 'page',
        in: 'query',
        required: false,
        schema: { type: 'number', minimum: 1, default: 1 },
        description: '页码',
      },
      limit: {
        name: 'limit',
        in: 'query',
        required: false,
        schema: { type: 'number', minimum: 1, maximum: 100, default: 10 },
        description: '每页数量',
      },
    },
  })
  @Responses(UserListSchema)
  async getUsers() {}

  @RouteConfig({
    method: 'get',
    path: '/users/{id}',
    summary: '获取用户详情',
    parameters: {
      id: {
        name: 'id',
        in: 'path',
        required: true,
        schema: { type: 'string' },
        description: '用户ID',
      },
    },
  })
  @Responses(BasicUserSchema)
  async getUserById() {}

  @RouteConfig({
    method: 'post',
    path: '/users',
    summary: '创建用户',
    description: '创建新用户',
  })
  @Body(UserCreateSchema)
  @Responses(BasicUserSchema)
  async createUser() {}

  @RouteConfig({
    method: 'put',
    path: '/users/{id}',
    summary: '更新用户',
    parameters: {
      id: {
        name: 'id',
        in: 'path',
        required: true,
        schema: { type: 'string' },
        description: '用户ID',
      },
    },
  })
  @Body(UserUpdateSchema)
  @Responses(BasicUserSchema)
  async updateUser() {}

  @RouteConfig({
    method: 'delete',
    path: '/users/{id}',
    summary: '删除用户',
    parameters: {
      id: {
        name: 'id',
        in: 'path',
        required: true,
        schema: { type: 'string' },
        description: '用户ID',
      },
    },
    responses: {
      204: { description: '删除成功' },
      404: { description: '用户不存在' },
    },
  })
  async deleteUser() {}
}

// ======================== 高级配置测试控制器 ========================

@Controller({
  tags: ['高级配置测试'],
})
export class AdvancedController {
  @RouteConfig({
    method: 'patch',
    path: '/advanced/{id}',
    summary: '高级配置接口',
    description: '包含所有高级配置选项的测试接口',
    operationId: 'advancedOperation',
    deprecated: true,
    tags: ['高级', '实验性'],
    parameters: {
      id: {
        name: 'id',
        in: 'path',
        required: true,
        schema: {
          type: 'string',
          pattern: '^[a-zA-Z0-9-]+$',
        },
        description: '资源标识符',
      },
      version: {
        name: 'api-version',
        in: 'header',
        required: false,
        schema: {
          type: 'string',
          enum: ['v1', 'v2', 'v3'],
          default: 'v1',
        },
        description: 'API版本',
      },
    },
    security: [
      { Bearer: [] },
      { ApiKey: [] },
    ],
    servers: [
      {
        url: 'https://api.test.com',
        description: '测试服务器',
      },
      {
        url: 'https://api.prod.com',
        description: '生产服务器',
      },
    ],
    externalDocs: {
      description: '详细文档',
      url: 'https://docs.test.com',
    },
    responses: {
      200: { description: '操作成功' },
      400: { description: '请求错误' },
      401: { description: '未授权' },
      404: { description: '资源不存在' },
      500: { description: '服务器错误' },
    },
  })
  @Body(z.object({
    operation: z.string(),
    data: z.any(),
  }))
  @Responses(z.object({
    success: z.boolean(),
    result: z.any().optional(),
  }))
  async advancedOperation() {}
}

// ======================== 错误响应测试控制器 ========================

@Controller({
  tags: ['错误处理'],
})
export class ErrorController {
  @RouteConfig({
    method: 'post',
    path: '/error-test',
    summary: '错误响应测试',
    responses: {
      400: {
        description: '请求参数错误',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      422: {
        description: '数据验证失败',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
      500: {
        description: '服务器内部错误',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse',
            },
          },
        },
      },
    },
  })
  @Body(z.object({
    shouldFail: z.boolean().default(false),
  }))
  @Responses(ErrorResponseSchema)
  async errorTest() {}
}

// ======================== 空控制器（边界测试） ========================

@Controller({
  tags: ['边界测试'],
})
export class EmptyController {
  // 没有任何路由方法
  private helper() {
    return '这不是路由方法'
  }
}

// ======================== 无装饰器控制器（边界测试） ========================

export class PlainController {
  async plainMethod() {
    return '没有装饰器'
  }
}

// ======================== 复杂Schema测试控制器 ========================

const NestedSchema = z.object({
  user: z.object({
    profile: z.object({
      personal: z.object({
        firstName: z.string(),
        lastName: z.string(),
        birthDate: z.string(),
        gender: z.enum(['male', 'female', 'other']),
      }),
      contact: z.object({
        email: z.string().email(),
        phone: z.string().optional(),
        address: z.object({
          street: z.string(),
          city: z.string(),
          zipCode: z.string(),
          country: z.string(),
        }),
      }),
    }),
    preferences: z.object({
      language: z.string().default('zh-CN'),
      timezone: z.string().default('Asia/Shanghai'),
      notifications: z.object({
        email: z.boolean().default(true),
        sms: z.boolean().default(false),
        push: z.boolean().default(true),
      }),
    }),
    permissions: z.array(z.string()),
    roles: z.array(z.object({
      id: z.string(),
      name: z.string(),
      description: z.string().optional(),
    })),
  }),
  metadata: z.record(z.any()),
  createdAt: z.string(),
  updatedAt: z.string(),
})

@Controller({
  tags: ['复杂Schema'],
})
export class ComplexSchemaController {
  @RouteConfig({
    method: 'post',
    path: '/complex',
    summary: '复杂嵌套Schema测试',
  })
  @Body(NestedSchema)
  @Responses(NestedSchema)
  async complexOperation() {}

  @RouteConfig({
    method: 'get',
    path: '/users/search',
    summary: '用户搜索',
    parameters: {
      q: {
        name: 'q',
        in: 'query',
        required: true,
        schema: { type: 'string', minLength: 1 },
        description: '搜索关键词',
      },
      filters: {
        name: 'filters',
        in: 'query',
        required: false,
        schema: {
          type: 'array',
          items: { type: 'string' },
        },
        description: '过滤条件',
      },
    },
  })
  @Responses(z.object({
    query: z.string(),
    results: z.array(BasicUserSchema),
    total: z.number(),
    facets: z.record(z.array(z.object({
      value: z.string(),
      count: z.number(),
    }))),
  }))
  async searchUsers() {}
}

// ======================== 导出所有控制器 ========================

export const TestControllers = {
  Basic: BasicController,
  User: UserController,
  Advanced: AdvancedController,
  Error: ErrorController,
  Empty: EmptyController,
  Plain: PlainController,
  ComplexSchema: ComplexSchemaController,
}

export const TestSchemas = {
  BasicUser: BasicUserSchema,
  UserCreate: UserCreateSchema,
  UserUpdate: UserUpdateSchema,
  UserList: UserListSchema,
  Pagination: PaginationSchema,
  ErrorResponse: ErrorResponseSchema,
  Nested: NestedSchema,
}
