/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-12-29 16:25:31
 * @LastEditTime: 2024-04-22 14:46:12
 * @LastEditors: shaojun
 * @Description:
 */

import { z } from '../../src/index'
import { UserInfo } from './global'
import { LIST, R } from './tools'

export const UserLoginReq = z
  .object({
    username: z.string().openapi({ example: '<PERSON>' }),
    password: z.string().openapi({ example: 'abcdefg' }),
  })
  .openapi('UserLoginReq')

export const UserInfoList = LIST(UserInfo)
export const GetUserListRes = R(UserInfoList)

export type IGetUserListRes = z.infer<typeof GetUserListRes>

export type IUserLoginReq = z.infer<typeof UserLoginReq>

// 获取验证码
export const GetVerificationCodeRes = z.string().openapi({ example: '123456' })
export type IGetVerificationCodeRes = z.infer<typeof GetVerificationCodeRes>
// 获取验证码
export const GetApiTokenRes = z.string().openapi({ example: '123456' })
export type IGetApiTokenRes = z.infer<typeof GetApiTokenRes>
