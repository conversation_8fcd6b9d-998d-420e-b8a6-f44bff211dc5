/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-20 21:16:23
 * @LastEditTime: 2025-07-20 21:24:16
 * @LastEditors: shaojun
 * @Description: Schema 收集器测试
 */

import { extendZodWithOpenApi, OpenApiGeneratorV3, OpenAPIRegistry } from '@asteasolutions/zod-to-openapi'
import { beforeEach, describe, expect, it } from 'vitest'
import { z } from 'zod'
import {
  addGlobalSchemas,
  clearSchemas,
  collectSchemas,
  getSchemasCount,
  getSchemasList,
  registerSchemas,
} from '../../src/builder/schema-collector'

// 扩展 zod 以支持 openapi
extendZodWithOpenApi(z)

describe('schema-collector', () => {
  let testRegistry: OpenAPIRegistry

  beforeEach(() => {
    // 每次测试前清空 schemas 和创建新的 registry
    clearSchemas()
    testRegistry = new OpenAPIRegistry()
  })

  describe('基础功能测试', () => {
    it('应该能够收集普通的 Zod Schema', () => {
      const plainSchema = z.object({
        name: z.string(),
        age: z.number(),
      })

      collectSchemas('PlainSchema', plainSchema)

      expect(getSchemasCount()).toBe(1)
      const schemas = getSchemasList()
      expect(schemas[0].refId).toBe('PlainSchema')
      expect(schemas[0].zodSchema).toBe(plainSchema)
    })

    it('应该能够收集带有 .openapi() 扩展的 Schema', () => {
      const openapiSchema = z.object({
        name: z.string().openapi({ description: '姓名' }),
        age: z.number().openapi({ description: '年龄' }),
      }).openapi({ description: '用户信息' })

      collectSchemas('OpenapiSchema', openapiSchema)

      expect(getSchemasCount()).toBe(1)
      const schemas = getSchemasList()
      expect(schemas[0].refId).toBe('OpenapiSchema')
      expect(schemas[0].zodSchema).toBe(openapiSchema)
    })

    it('应该能够收集只在字段级别使用 .openapi() 的 Schema', () => {
      const fieldOpenapiSchema = z.object({
        name: z.string().openapi({ description: '姓名' }),
        age: z.number().openapi({ description: '年龄' }),
      })

      collectSchemas('FieldOpenapiSchema', fieldOpenapiSchema)

      expect(getSchemasCount()).toBe(1)
      const schemas = getSchemasList()
      expect(schemas[0].refId).toBe('FieldOpenapiSchema')
      expect(schemas[0].zodSchema).toBe(fieldOpenapiSchema)
    })

    it('应该支持强制覆盖已存在的 Schema', () => {
      const schema1 = z.object({ name: z.string() })
      const schema2 = z.object({ name: z.string(), age: z.number() })

      collectSchemas('TestSchema', schema1)
      expect(getSchemasCount()).toBe(1)

      // 不强制覆盖，应该保持原有的
      collectSchemas('TestSchema', schema2, false)
      expect(getSchemasCount()).toBe(1)
      expect(getSchemasList()[0].zodSchema).toBe(schema1)

      // 强制覆盖
      collectSchemas('TestSchema', schema2, true)
      expect(getSchemasCount()).toBe(1)
      expect(getSchemasList()[0].zodSchema).toBe(schema2)
    })

    it('应该能够清空所有收集的 schemas', () => {
      collectSchemas('Schema1', z.object({ name: z.string() }))
      collectSchemas('Schema2', z.object({ age: z.number() }))
      expect(getSchemasCount()).toBe(2)

      clearSchemas()
      expect(getSchemasCount()).toBe(0)
      expect(getSchemasList()).toEqual([])
    })
  })

  describe('全局 Schema 处理测试', () => {
    it('应该能够处理全局 schemas', () => {
      const globalSchemas = {
        UserInfo: z.object({
          id: z.number(),
          name: z.string(),
        }),
        AppInfo: z.object({
          id: z.number().openapi({ description: '应用ID' }),
          name: z.string().openapi({ description: '应用名称' }),
        }),
      }

      addGlobalSchemas(globalSchemas)

      expect(getSchemasCount()).toBe(2)
      const schemas = getSchemasList()
      expect(schemas.map((s) => s.refId)).toContain('UserInfo')
      expect(schemas.map((s) => s.refId)).toContain('AppInfo')
    })

    it('应该能够强制覆盖全局 schemas', () => {
      const initialGlobals = {
        TestSchema: z.object({ name: z.string() }),
      }

      const overrideGlobals = {
        TestSchema: z.object({
          name: z.string(),
          age: z.number().openapi({ description: '年龄' }),
        }),
      }

      addGlobalSchemas(initialGlobals)
      expect(getSchemasCount()).toBe(1)

      addGlobalSchemas(overrideGlobals)
      expect(getSchemasCount()).toBe(1)

      const schema = getSchemasList()[0]
      expect(schema.refId).toBe('TestSchema')
      // 应该是被覆盖后的 schema
      expect((schema.zodSchema as any)._def.shape().age).toBeDefined()
    })
  })

  describe('schema 注册测试', () => {
    it('应该正确注册普通的 Zod Schema', () => {
      const plainSchema = z.object({
        name: z.string(),
        age: z.number(),
      })

      collectSchemas('PlainSchema', plainSchema)
      registerSchemas(testRegistry)

      // 生成文档验证注册结果
      const generator = new OpenApiGeneratorV3(testRegistry.definitions)
      const doc = generator.generateDocument({
        openapi: '3.0.0',
        info: { title: 'Test API', version: '1.0.0' },
      })

      expect(doc.components?.schemas?.PlainSchema).toBeDefined()
      expect(doc.components?.schemas?.PlainSchema.type).toBe('object')
      expect(doc.components?.schemas?.PlainSchema.properties?.name).toBeDefined()
      expect(doc.components?.schemas?.PlainSchema.properties?.age).toBeDefined()
    })

    it('应该正确注册带有 .openapi() 扩展的 Schema', () => {
      const openapiSchema = z.object({
        id: z.number().openapi({ description: '用户ID' }),
        name: z.string().openapi({ description: '用户名称' }),
        email: z.string().email().openapi({ description: '邮箱地址' }),
      }).openapi({ description: '用户信息' })

      collectSchemas('UserInfo', openapiSchema)
      registerSchemas(testRegistry)

      // 生成文档验证注册结果
      const generator = new OpenApiGeneratorV3(testRegistry.definitions)
      const doc = generator.generateDocument({
        openapi: '3.0.0',
        info: { title: 'Test API', version: '1.0.0' },
      })

      const userInfoSchema = doc.components?.schemas?.UserInfo
      expect(userInfoSchema).toBeDefined()
      expect(userInfoSchema.type).toBe('object')
      expect(userInfoSchema.description).toBe('用户信息')

      // 验证字段描述
      expect(userInfoSchema.properties?.id?.description).toBe('用户ID')
      expect(userInfoSchema.properties?.name?.description).toBe('用户名称')
      expect(userInfoSchema.properties?.email?.description).toBe('邮箱地址')
      expect(userInfoSchema.properties?.email?.format).toBe('email')
    })

    it('应该正确处理复杂的 Schema 结构', () => {
      const appInfoSchema = z.object({
        id: z.number().openapi({ description: '应用ID' }),
        platform: z.string().openapi({
          description: '平台类型',
          example: 'iOS',
        }),
        bundleId: z.string().openapi({
          description: '包标识符',
          example: 'com.example.app',
        }),
        autoPublish: z.boolean().openapi({ description: '是否自动发布' }),
        tags: z.array(z.string()).optional().openapi({ description: '标签列表' }),
      })

      collectSchemas('AppInfo', appInfoSchema)
      registerSchemas(testRegistry)

      // 生成文档验证注册结果
      const generator = new OpenApiGeneratorV3(testRegistry.definitions)
      const doc = generator.generateDocument({
        openapi: '3.0.0',
        info: { title: 'Test API', version: '1.0.0' },
      })

      const appInfoSchemaResult = doc.components?.schemas?.AppInfo
      expect(appInfoSchemaResult).toBeDefined()
      expect(appInfoSchemaResult.type).toBe('object')

      // 验证字段类型和描述
      expect(appInfoSchemaResult.properties?.id?.type).toBe('number')
      expect(appInfoSchemaResult.properties?.id?.description).toBe('应用ID')
      expect(appInfoSchemaResult.properties?.platform?.example).toBe('iOS')
      expect(appInfoSchemaResult.properties?.bundleId?.example).toBe('com.example.app')
      expect(appInfoSchemaResult.properties?.autoPublish?.type).toBe('boolean')
      expect(appInfoSchemaResult.properties?.tags?.type).toBe('array')
      expect(appInfoSchemaResult.properties?.tags?.items?.type).toBe('string')
    })

    it('不应该生成 Zod 内部结构', () => {
      const testSchema = z.object({
        name: z.string().openapi({ description: '名称' }),
        value: z.number().openapi({ description: '数值' }),
      })

      collectSchemas('TestSchema', testSchema)
      registerSchemas(testRegistry)

      // 生成文档验证注册结果
      const generator = new OpenApiGeneratorV3(testRegistry.definitions)
      const doc = generator.generateDocument({
        openapi: '3.0.0',
        info: { title: 'Test API', version: '1.0.0' },
      })

      const schema = doc.components?.schemas?.TestSchema
      expect(schema).toBeDefined()

      // 确保不包含 Zod 内部结构
      expect(schema._def).toBeUndefined()
      expect(schema.typeName).toBeUndefined()
      expect(schema._cached).toBeUndefined()
      expect(schema['~standard']).toBeUndefined()

      // 确保是正确的 OpenAPI schema
      expect(schema.type).toBe('object')
      expect(schema.properties).toBeDefined()
    })
  })

  describe('全局 Schema 问题重现测试', () => {
    it('应该能够处理没有 .openapi() 扩展的全局 Schema', () => {
      // 模拟测试中的情况：普通的 Zod Schema 没有 .openapi() 扩展
      const plainGlobalSchema = z.object({
        id: z.string(),
        name: z.string(),
        email: z.string().email(),
      })

      const globalSchemas = {
        GlobalUser: plainGlobalSchema,
      }

      addGlobalSchemas(globalSchemas)
      registerSchemas(testRegistry)

      // 生成文档验证注册结果
      const generator = new OpenApiGeneratorV3(testRegistry.definitions)
      const doc = generator.generateDocument({
        openapi: '3.0.0',
        info: { title: 'Test API', version: '1.0.0' },
      })

      const globalUserSchema = doc.components?.schemas?.GlobalUser
      expect(globalUserSchema).toBeDefined()
      expect(globalUserSchema.type).toBe('object')
      expect(globalUserSchema.properties?.id).toBeDefined()
      expect(globalUserSchema.properties?.name).toBeDefined()
      expect(globalUserSchema.properties?.email).toBeDefined()
    })
  })
})
