/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-01-09 19:57:00
 * @LastEditTime: 2025-07-10 12:07:31
 * @LastEditors: shaojun
 * @Description: Debug测试 - 调试状态隔离问题
 */

import { beforeEach, describe, expect, it } from 'vitest'
import { TestControllers } from '../case/router-controllers'
import { RouterTestStateManager } from '../case/router-test-utils'

describe('debug - 状态隔离问题调试', () => {
  let stateManager: RouterTestStateManager

  beforeEach(() => {
    stateManager = RouterTestStateManager.getInstance()
    stateManager.resetAllState()
  })

  it('dEBUG 1: 基础测试 - 无prefix', () => {
    const router = stateManager.createIsolatedRouter({}, {})

    router.applyRoute(TestControllers.Basic)
    router.swagger()

    const paths = Object.keys(router.docs!.object.paths)
    console.log('DEBUG 1 - 无prefix路径:', paths)
    console.log('DEBUG 1 - router.opts.prefix:', router.opts.prefix)

    expect(paths).toContain('/basic')
    expect(paths).not.toContain('/api/v1/basic')
  })

  it('dEBUG 2: 带prefix测试', () => {
    const router = stateManager.createIsolatedRouter({}, { prefix: '/api/v1' })

    router.applyRoute(TestControllers.Basic)
    router.swagger()

    const paths = Object.keys(router.docs!.object.paths)
    console.log('DEBUG 2 - 带prefix路径:', paths)
    console.log('DEBUG 2 - router.opts.prefix:', router.opts.prefix)

    expect(paths).toContain('/api/v1/basic')
    expect(paths).not.toContain('/basic')
  })

  it('dEBUG 3: 重置后无prefix测试', () => {
    // 强制重置状态
    stateManager.resetAllState()

    const router = stateManager.createIsolatedRouter({}, {})

    console.log('DEBUG 3 - 重置后router.opts.prefix:', router.opts.prefix)

    router.applyRoute(TestControllers.Basic)
    router.swagger()

    const paths = Object.keys(router.docs!.object.paths)
    console.log('DEBUG 3 - 重置后无prefix路径:', paths)

    expect(paths).toContain('/basic')
    expect(paths).not.toContain('/api/v1/basic')
  })

  it('dEBUG 4: 独立实例测试', () => {
    // 创建两个独立实例
    const router1 = stateManager.createIsolatedRouter({}, { prefix: '/api/v1' })
    const router2 = stateManager.createIsolatedRouter({}, {})

    console.log('DEBUG 4 - router1 prefix:', router1.opts.prefix)
    console.log('DEBUG 4 - router2 prefix:', router2.opts.prefix)

    router1.applyRoute(TestControllers.Basic)
    router2.applyRoute(TestControllers.Basic)

    router1.swagger()
    router2.swagger()

    const paths1 = Object.keys(router1.docs!.object.paths)
    const paths2 = Object.keys(router2.docs!.object.paths)

    console.log('DEBUG 4 - router1 paths:', paths1)
    console.log('DEBUG 4 - router2 paths:', paths2)

    expect(paths1).toContain('/api/v1/basic')
    expect(paths2).toContain('/basic')
  })
})
