/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-01-09 19:55:00
 * @LastEditTime: 2025-07-09 19:51:58
 * @LastEditors: shaojun
 * @Description: SwaggerRouter简化测试 - 专注于核心功能和状态隔离
 */

import { beforeEach, describe, expect, it } from 'vitest'
import { TestControllers } from '../case/router-controllers'
import { RouterTestStateManager } from '../case/router-test-utils'

describe('swaggerRouter - 简化测试', () => {
  let stateManager: RouterTestStateManager

  beforeEach(() => {
    stateManager = RouterTestStateManager.getInstance()
    stateManager.resetAllState()
  })

  describe('🎯 基础功能验证', () => {
    it('应该创建独立的router实例', () => {
      const router1 = stateManager.createIsolatedRouter()
      const router2 = stateManager.createIsolatedRouter()

      expect(router1).not.toBe(router2)
      expect(router1.controllerClasses).toHaveLength(0)
      expect(router2.controllerClasses).toHaveLength(0)
    })

    it('应该正确注册控制器', () => {
      const router = stateManager.createIsolatedRouter()

      router.applyRoute(TestControllers.Basic)
      expect(router.controllerClasses).toHaveLength(1)

      router.applyRoute(TestControllers.User)
      expect(router.controllerClasses).toHaveLength(2)
    })

    it('应该生成Swagger文档', () => {
      const router = stateManager.createIsolatedRouter()

      router.applyRoute(TestControllers.Basic)
      router.swagger()

      expect(router.docs).toBeDefined()
      expect(router.docs!.object).toBeDefined()
      expect(router.docs!.object.openapi).toBe('3.0.0')
      expect(router.docs!.object.paths['/basic']).toBeDefined()
    })
  })

  describe('🧼 状态隔离验证', () => {
    it('测试间应该有干净的状态', () => {
      // 第一个测试场景
      {
        const router1 = stateManager.createIsolatedRouter()
        router1.applyRoute(TestControllers.Basic)
        router1.swagger()

        expect(router1.controllerClasses).toHaveLength(1)
        expect(Object.keys(router1.docs!.object.paths)).toContain('/basic')
      }

      // 重置状态
      stateManager.resetAllState()

      // 第二个测试场景 - 应该是干净的
      {
        const router2 = stateManager.createIsolatedRouter()

        expect(router2.controllerClasses).toHaveLength(0)
        expect(stateManager.verifyStateClean()).toBe(true)

        // 生成空文档
        router2.swagger()
        expect(Object.keys(router2.docs!.object.paths)).toHaveLength(0)
      }
    })

    it('不同prefix设置应该独立', () => {
      // 第一个router带prefix
      {
        const router1 = stateManager.createIsolatedRouter({}, { prefix: '/api/v1' })
        router1.applyRoute(TestControllers.Basic)
        router1.swagger()

        expect(Object.keys(router1.docs!.object.paths)).toContain('/api/v1/basic')
      }

      // 重置状态
      stateManager.resetAllState()

      // 第二个router不带prefix
      {
        const router2 = stateManager.createIsolatedRouter({}, {})
        router2.applyRoute(TestControllers.Basic)
        router2.swagger()

        const paths = Object.keys(router2.docs!.object.paths)
        expect(paths).toContain('/basic')
        expect(paths).not.toContain('/api/v1/basic')
      }
    })

    it('多控制器状态应该独立', () => {
      // 第一个router使用UserController
      {
        const router1 = stateManager.createIsolatedRouter()
        router1.applyRoute(TestControllers.User)
        router1.swagger()

        const paths = Object.keys(router1.docs!.object.paths)
        expect(paths).toContain('/users')
        expect(paths).not.toContain('/basic')
      }

      // 重置状态
      stateManager.resetAllState()

      // 第二个router使用BasicController
      {
        const router2 = stateManager.createIsolatedRouter()
        router2.applyRoute(TestControllers.Basic)
        router2.swagger()

        const paths = Object.keys(router2.docs!.object.paths)
        expect(paths).toContain('/basic')
        expect(paths).not.toContain('/users')
      }
    })
  })

  describe('🔧 复杂场景', () => {
    it('应该正确处理多控制器', () => {
      const router = stateManager.createIsolatedRouter()

      router.applyRoute(TestControllers.Basic)
      router.applyRoute(TestControllers.User)
      router.swagger()

      const paths = Object.keys(router.docs!.object.paths)
      expect(paths).toContain('/basic')
      expect(paths).toContain('/users')
      expect(paths).toContain('/users/{id}')

      expect(router.controllerClasses).toHaveLength(2)
    })

    it('应该正确处理空控制器', () => {
      const router = stateManager.createIsolatedRouter()

      router.applyRoute(TestControllers.Empty)
      router.swagger()

      const paths = Object.keys(router.docs!.object.paths)
      expect(paths).toHaveLength(0)
    })

    it('应该正确处理无装饰器控制器', () => {
      const router = stateManager.createIsolatedRouter()

      router.applyRoute(TestControllers.Plain)
      router.swagger()

      const paths = Object.keys(router.docs!.object.paths)
      expect(paths).toHaveLength(0)
    })
  })

  describe('⚙️ 配置验证', () => {
    it('应该正确应用自定义配置', () => {
      const config = {
        swaggerJsonEndpoint: '/api/docs.json',
        swaggerHtmlEndpoint: '/api/docs-ui',
        validateRequest: true,
        validateResponse: true,
      }

      const router = stateManager.createIsolatedRouter(config)

      expect(router.config.swaggerJsonEndpoint).toBe('/api/docs.json')
      expect(router.config.swaggerHtmlEndpoint).toBe('/api/docs-ui')
      expect(router.config.validateRequest).toBe(true)
      expect(router.config.validateResponse).toBe(true)
    })

    it('应该正确添加swagger端点', () => {
      const router = stateManager.createIsolatedRouter()

      router.applyRoute(TestControllers.Basic)
      router.swagger()

      // 检查是否添加了swagger JSON端点路由
      const hasJsonRoute = router.stack.some((layer) =>
        layer.path === '/swagger.json' && layer.methods.includes('GET'),
      )

      expect(hasJsonRoute).toBe(true)
      expect(router.config.swaggerHtmlEndpoint).toBe('/swagger')
    })

    it('应该清理注册表状态', () => {
      const router = stateManager.createIsolatedRouter()

      router.applyRoute(TestControllers.User)
      expect(router.controllerClasses).toHaveLength(1)

      router.clear()
      expect(stateManager.verifyStateClean()).toBe(true)
    })
  })
})
