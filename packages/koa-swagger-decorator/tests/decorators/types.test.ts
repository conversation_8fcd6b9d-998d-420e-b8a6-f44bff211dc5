/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-16 16:46:39
 * @LastEditTime: 2025-07-16 16:46:39
 * @LastEditors: shaojun
 * @Description: 装饰器类型测试
 */
import { describe, it, expect } from 'vitest'
import type { ControllerConfig } from '../../src/types'
import type { RouteConfigDecorator, StrongRouteConfig } from '../../src/decorators'

describe('装饰器类型测试', () => {
  describe('ControllerConfig', () => {
    it('应该支持基本的控制器配置', () => {
      const config: ControllerConfig = {
        tags: ['test'],
        paths: {
          parameters: []
        }
      }
      expect(config.tags).toEqual(['test'])
      expect(config.paths?.parameters).toEqual([])
    })

    it('应该支持可选的配置项', () => {
      const config: ControllerConfig = {}
      expect(config.tags).toBeUndefined()
      expect(config.paths).toBeUndefined()
      expect(config.components).toBeUndefined()
    })
  })

  describe('RouteConfigDecorator', () => {
    it('应该支持动态属性', () => {
      const config: RouteConfigDecorator = {
        className: 'TestController',
        methodName: 'testMethod',
        identifier: 'test-id',
        customProp: 'custom-value'
      }
      expect(config.className).toBe('TestController')
      expect(config.methodName).toBe('testMethod')
      expect(config.identifier).toBe('test-id')
      expect(config.customProp).toBe('custom-value')
    })

    it('应该支持空配置', () => {
      const config: RouteConfigDecorator = {}
      expect(config.className).toBeUndefined()
      expect(config.methodName).toBeUndefined()
      expect(config.identifier).toBeUndefined()
    })
  })

  describe('StrongRouteConfig', () => {
    it('应该继承 ZodRouteConfig 和 RouteConfigDecorator', () => {
      const config: StrongRouteConfig = {
        method: 'get',
        path: '/test',
        className: 'TestController',
        methodName: 'testMethod'
      }
      expect(config.method).toBe('get')
      expect(config.path).toBe('/test')
      expect(config.className).toBe('TestController')
      expect(config.methodName).toBe('testMethod')
    })

    it('应该支持完整的路由配置', () => {
      const config: StrongRouteConfig = {
        method: 'post',
        path: '/api/test',
        summary: 'Test API',
        description: 'Test API description',
        operationId: 'testOperation',
        tags: ['test'],
        className: 'TestController',
        methodName: 'testMethod',
        identifier: 'test-api'
      }
      expect(config.method).toBe('post')
      expect(config.path).toBe('/api/test')
      expect(config.summary).toBe('Test API')
      expect(config.description).toBe('Test API description')
      expect(config.operationId).toBe('testOperation')
      expect(config.tags).toEqual(['test'])
      expect(config.className).toBe('TestController')
      expect(config.methodName).toBe('testMethod')
      expect(config.identifier).toBe('test-api')
    })
  })
})
