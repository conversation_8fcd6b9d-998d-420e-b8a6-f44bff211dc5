/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-25 16:10:00
 * @LastEditTime: 2025-07-11 08:33:46
 * @LastEditors: shaojun
 * @Description: Middlewares装饰器测试
 */

import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, Middlewares, RouteConfig } from '../../src/decorators'

// 模拟Koa中间件类型
interface Context {
  request: any
  response: any
  state: any
  [key: string]: any
}

type Next = () => Promise<void>
type KoaMiddleware = (ctx: Context, next: Next) => Promise<void> | void

describe('middlewares装饰器测试', () => {
  describe('@Middlewares装饰器基础功能', () => {
    it('应该正确存储中间件元数据', () => {
      const authMiddleware: KoaMiddleware = async (ctx, next) => {
        // 模拟认证逻辑
        ctx.user = { id: '123', name: 'test' }
        await next()
      }

      @Controller({ tags: ['test'] })
      class TestController {
        @RouteConfig({
          method: 'get',
          path: '/protected',
          summary: '受保护的路由',
        })
        @Middlewares(authMiddleware)
        getProtectedResource() {
          return 'protected data'
        }
      }

      // 验证元数据存储
      const metadataKeys = Reflect.getMetadataKeys(TestController.prototype, 'getProtectedResource')
      expect(metadataKeys).toContain('swagger:middlewares')

      // 验证存储的中间件数组
      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        TestController.prototype,
        'getProtectedResource',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(authMiddleware)
    })

    it('应该支持简单的同步中间件', () => {
      const logMiddleware: KoaMiddleware = (ctx, next) => {
        console.log(`Request: ${ctx.method} ${ctx.path}`)
        return next()
      }

      @Controller({ tags: ['日志'] })
      class LogController {
        @RouteConfig({ method: 'get', path: '/log-test' })
        @Middlewares(logMiddleware)
        testLogging() {
          return 'logged'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        LogController.prototype,
        'testLogging',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(logMiddleware)
      expect(typeof storedMiddlewares[0]).toBe('function')
    })

    it('应该支持异步中间件', () => {
      const asyncMiddleware: KoaMiddleware = async (ctx, next) => {
        // 模拟异步操作
        await new Promise((resolve) => setTimeout(resolve, 10))
        ctx.timestamp = Date.now()
        await next()
      }

      @Controller({ tags: ['异步'] })
      class AsyncController {
        @RouteConfig({ method: 'post', path: '/async-test' })
        @Middlewares(asyncMiddleware)
        testAsync() {
          return 'async completed'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        AsyncController.prototype,
        'testAsync',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(asyncMiddleware)
    })
  })

  describe('@Middlewares装饰器复杂场景', () => {
    it('应该支持多个中间件的顺序执行', () => {
      const middleware1: KoaMiddleware = async (ctx, next) => {
        ctx.step1 = true
        await next()
      }

      const middleware2: KoaMiddleware = async (ctx, next) => {
        ctx.step2 = true
        await next()
      }

      const middleware3: KoaMiddleware = async (ctx, next) => {
        ctx.step3 = true
        await next()
      }

      @Controller({ tags: ['多中间件'] })
      class MultiMiddlewareController {
        @RouteConfig({ method: 'get', path: '/multi-step' })
        @Middlewares(middleware1)
        @Middlewares(middleware2)
        @Middlewares(middleware3)
        multiStepProcess() {
          return 'all steps completed'
        }
      }

      // 验证所有中间件都被存储
      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        MultiMiddlewareController.prototype,
        'multiStepProcess',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(3)
      expect(storedMiddlewares).toContain(middleware1)
      expect(storedMiddlewares).toContain(middleware2)
      expect(storedMiddlewares).toContain(middleware3)
    })

    it('应该支持带参数的中间件工厂函数', () => {
      const rateLimitFactory = (limit: number) => {
        return async (ctx: Context, next: Next) => {
          // 模拟限流逻辑
          ctx.rateLimit = { limit, remaining: limit - 1 }
          await next()
        }
      }

      const rateLimitMiddleware = rateLimitFactory(100)

      @Controller({ tags: ['限流'] })
      class RateLimitController {
        @RouteConfig({ method: 'post', path: '/limited' })
        @Middlewares(rateLimitMiddleware)
        limitedEndpoint() {
          return 'rate limited'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        RateLimitController.prototype,
        'limitedEndpoint',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(rateLimitMiddleware)
    })

    it('应该支持条件中间件', () => {
      const conditionalMiddleware: KoaMiddleware = async (ctx, next) => {
        if (ctx.headers.authorization) {
          ctx.authenticated = true
        } else {
          ctx.authenticated = false
        }
        await next()
      }

      @Controller({ tags: ['条件中间件'] })
      class ConditionalController {
        @RouteConfig({ method: 'get', path: '/conditional' })
        @Middlewares(conditionalMiddleware)
        conditionalAccess() {
          return 'conditional access'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        ConditionalController.prototype,
        'conditionalAccess',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(conditionalMiddleware)
    })

    it('应该支持错误处理中间件', () => {
      const errorHandlerMiddleware: KoaMiddleware = async (ctx, next) => {
        try {
          await next()
        } catch (error) {
          ctx.status = 500
          ctx.body = { error: 'Internal Server Error' }
        }
      }

      @Controller({ tags: ['错误处理'] })
      class ErrorHandlerController {
        @RouteConfig({ method: 'get', path: '/error-prone' })
        @Middlewares(errorHandlerMiddleware)
        errorProneEndpoint() {
          throw new Error('Something went wrong')
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        ErrorHandlerController.prototype,
        'errorProneEndpoint',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(errorHandlerMiddleware)
    })
  })

  describe('@Middlewares装饰器错误处理', () => {
    it('应该在传入null中间件时抛出错误（新的错误处理系统）', () => {
      // Middlewares装饰器现在正确验证参数，null参数会抛出错误
      expect(() => {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({ method: 'get', path: '/test' })
          @Middlewares(null as any)
          testMethod() {}
        }
      }).toThrow('Middleware is required')
    })

    it('应该正确处理装饰器的内部错误检查', () => {
      const testMiddleware = async (ctx: Context, next: Next) => {
        await next()
      }

      // 优化后的装饰器直接实现，不再有复杂的错误检查
      // 测试装饰器能正常工作
      expect(() => {
        @Controller({ tags: ['测试'] })
        class TestController {
          @Middlewares(testMiddleware)
          testMethod() {}
        }
      }).not.toThrow()
    })
  })

  describe('@Middlewares装饰器与其他装饰器组合', () => {
    it('应该与RouteConfig和Body装饰器正确组合', () => {
      const validationMiddleware: KoaMiddleware = async (ctx, next) => {
        // 模拟验证逻辑
        if (!ctx.request.body) {
          ctx.throw(400, 'Request body is required')
        }
        await next()
      }

      const userSchema = z.object({
        name: z.string(),
        email: z.string().email(),
      })

      @Controller({ tags: ['组合测试'] })
      class CombinedController {
        @RouteConfig({
          method: 'post',
          path: '/users',
          summary: '创建用户',
        })
        @Body(userSchema)
        @Middlewares(validationMiddleware)
        createUser() {
          return 'user created'
        }
      }

      // 验证RouteConfig
      const routeConfig = Reflect.getMetadata(
        'swagger:routeConfig',
        CombinedController.prototype,
        'createUser',
      )
      expect(routeConfig.method).toBe('post')
      expect(routeConfig.path).toBe('/users')

      // 验证Body
      const bodyConfig = Reflect.getMetadata(
        'swagger:body',
        CombinedController.prototype,
        'createUser',
      )
      expect(bodyConfig).toBe(userSchema)

      // 验证Middlewares
      const middlewareConfig = Reflect.getMetadata(
        'swagger:middlewares',
        CombinedController.prototype,
        'createUser',
      )
      expect(Array.isArray(middlewareConfig)).toBe(true)
      expect(middlewareConfig).toHaveLength(1)
      expect(middlewareConfig[0]).toBe(validationMiddleware)
    })

    it('应该支持同一控制器中的不同中间件', () => {
      const authMiddleware: KoaMiddleware = async (ctx, next) => {
        ctx.user = { id: '123' }
        await next()
      }

      const cacheMiddleware: KoaMiddleware = async (ctx, next) => {
        ctx.cache = { enabled: true }
        await next()
      }

      const logMiddleware: KoaMiddleware = async (ctx, next) => {
        console.log('Request logged')
        await next()
      }

      @Controller({ tags: ['多中间件控制器'] })
      class MultiMiddlewareController {
        @RouteConfig({ method: 'get', path: '/auth-required' })
        @Middlewares(authMiddleware)
        authRequired() {}

        @RouteConfig({ method: 'get', path: '/cached' })
        @Middlewares(cacheMiddleware)
        cached() {}

        @RouteConfig({ method: 'get', path: '/logged' })
        @Middlewares(logMiddleware)
        logged() {}
      }

      // 验证每个方法都有正确的中间件
      const authMiddlewareStored = Reflect.getMetadata(
        'swagger:middlewares',
        MultiMiddlewareController.prototype,
        'authRequired',
      )
      const cacheMiddlewareStored = Reflect.getMetadata(
        'swagger:middlewares',
        MultiMiddlewareController.prototype,
        'cached',
      )
      const logMiddlewareStored = Reflect.getMetadata(
        'swagger:middlewares',
        MultiMiddlewareController.prototype,
        'logged',
      )

      expect(Array.isArray(authMiddlewareStored)).toBe(true)
      expect(authMiddlewareStored[0]).toBe(authMiddleware)
      expect(Array.isArray(cacheMiddlewareStored)).toBe(true)
      expect(cacheMiddlewareStored[0]).toBe(cacheMiddleware)
      expect(Array.isArray(logMiddlewareStored)).toBe(true)
      expect(logMiddlewareStored[0]).toBe(logMiddleware)
    })
  })

  describe('@Middlewares装饰器实际应用场景', () => {
    it('应该支持JWT认证中间件', () => {
      const jwtAuthMiddleware: KoaMiddleware = async (ctx, next) => {
        const token = ctx.headers.authorization?.replace('Bearer ', '')
        if (!token) {
          ctx.throw(401, 'Token required')
        }

        try {
          // 模拟JWT验证
          ctx.user = { id: 'user123', email: '<EMAIL>' }
          await next()
        } catch (error) {
          ctx.throw(401, 'Invalid token')
        }
      }

      @Controller({ tags: ['JWT认证'] })
      class JWTController {
        @RouteConfig({
          method: 'get',
          path: '/profile',
          summary: '获取用户资料',
        })
        @Middlewares(jwtAuthMiddleware)
        getUserProfile() {
          return 'user profile'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        JWTController.prototype,
        'getUserProfile',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(jwtAuthMiddleware)
    })

    it('应该支持CORS中间件', () => {
      const corsMiddleware: KoaMiddleware = async (ctx, next) => {
        ctx.set('Access-Control-Allow-Origin', '*')
        ctx.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        ctx.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')

        if (ctx.method === 'OPTIONS') {
          ctx.status = 204
          return
        }

        await next()
      }

      @Controller({ tags: ['CORS'] })
      class CORSController {
        @RouteConfig({
          method: 'post',
          path: '/api/data',
          summary: '跨域API',
        })
        @Middlewares(corsMiddleware)
        crossOriginAPI() {
          return 'cors enabled'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        CORSController.prototype,
        'crossOriginAPI',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(corsMiddleware)
    })

    it('应该支持请求限流中间件', () => {
      const createRateLimiter = (windowMs: number, maxRequests: number) => {
        const requests = new Map<string, number[]>()

        return async (ctx: Context, next: Next) => {
          const ip = ctx.ip || 'unknown'
          const now = Date.now()
          const windowStart = now - windowMs

          if (!requests.has(ip)) {
            requests.set(ip, [])
          }

          const ipRequests = requests.get(ip)!
          const validRequests = ipRequests.filter((time) => time > windowStart)

          if (validRequests.length >= maxRequests) {
            ctx.throw(429, 'Too Many Requests')
          }

          validRequests.push(now)
          requests.set(ip, validRequests)

          await next()
        }
      }

      const rateLimitMiddleware = createRateLimiter(60000, 100) // 100 requests per minute

      @Controller({ tags: ['限流'] })
      class RateLimitController {
        @RouteConfig({
          method: 'post',
          path: '/api/action',
          summary: '限流API',
        })
        @Middlewares(rateLimitMiddleware)
        rateLimitedAction() {
          return 'action completed'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        RateLimitController.prototype,
        'rateLimitedAction',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(rateLimitMiddleware)
    })

    it('应该支持请求日志中间件', () => {
      const requestLoggerMiddleware: KoaMiddleware = async (ctx, next) => {
        const start = Date.now()

        console.log(`[${new Date().toISOString()}] ${ctx.method} ${ctx.url} - Start`)

        await next()

        const duration = Date.now() - start
        console.log(`[${new Date().toISOString()}] ${ctx.method} ${ctx.url} - ${ctx.status} - ${duration}ms`)
      }

      @Controller({ tags: ['日志记录'] })
      class LoggerController {
        @RouteConfig({
          method: 'get',
          path: '/api/logged-endpoint',
          summary: '记录日志的端点',
        })
        @Middlewares(requestLoggerMiddleware)
        loggedEndpoint() {
          return 'logged response'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        LoggerController.prototype,
        'loggedEndpoint',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(requestLoggerMiddleware)
    })

    it('应该支持缓存中间件', () => {
      const cacheMiddleware: KoaMiddleware = async (ctx, next) => {
        const cacheKey = `${ctx.method}:${ctx.url}`

        // 模拟缓存检查
        const cached = getCachedResponse(cacheKey)
        if (cached) {
          ctx.body = cached
          ctx.set('X-Cache', 'HIT')
          return
        }

        await next()

        // 模拟缓存存储
        if (ctx.status === 200 && ctx.body) {
          setCachedResponse(cacheKey, ctx.body, 300) // 5分钟缓存
          ctx.set('X-Cache', 'MISS')
        }
      }

      // 模拟缓存函数
      function getCachedResponse(key: string): any {
        return null // 简单模拟
      }

      function setCachedResponse(key: string, value: any, ttl: number): void {
        // 简单模拟
      }

      @Controller({ tags: ['缓存'] })
      class CacheController {
        @RouteConfig({
          method: 'get',
          path: '/api/cached-data',
          summary: '缓存数据',
        })
        @Middlewares(cacheMiddleware)
        getCachedData() {
          return 'cached data'
        }
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        CacheController.prototype,
        'getCachedData',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(cacheMiddleware)
    })
  })

  describe('@Middlewares装饰器类型验证', () => {
    it('应该接受函数类型的中间件', () => {
      const functionMiddleware = (ctx: any, next: any) => {
        return next()
      }

      @Controller({ tags: ['函数类型'] })
      class FunctionController {
        @RouteConfig({ method: 'get', path: '/function' })
        @Middlewares(functionMiddleware)
        functionEndpoint() {}
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        FunctionController.prototype,
        'functionEndpoint',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(typeof storedMiddlewares[0]).toBe('function')
      expect(storedMiddlewares[0]).toBe(functionMiddleware)
    })

    it('应该支持箭头函数中间件', () => {
      const arrowMiddleware = async (ctx: Context, next: Next) => {
        ctx.arrowFunction = true
        await next()
      }

      @Controller({ tags: ['箭头函数'] })
      class ArrowController {
        @RouteConfig({ method: 'post', path: '/arrow' })
        @Middlewares(arrowMiddleware)
        arrowEndpoint() {}
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        ArrowController.prototype,
        'arrowEndpoint',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(arrowMiddleware)
    })

    it('应该支持类方法作为中间件', () => {
      class MiddlewareClass {
        async handle(ctx: Context, next: Next) {
          ctx.classMethod = true
          await next()
        }
      }

      const middlewareInstance = new MiddlewareClass()
      const boundMethod = middlewareInstance.handle.bind(middlewareInstance)

      @Controller({ tags: ['类方法'] })
      class ClassMethodController {
        @RouteConfig({ method: 'put', path: '/class-method' })
        @Middlewares(boundMethod)
        classMethodEndpoint() {}
      }

      const storedMiddlewares = Reflect.getMetadata(
        'swagger:middlewares',
        ClassMethodController.prototype,
        'classMethodEndpoint',
      )
      expect(Array.isArray(storedMiddlewares)).toBe(true)
      expect(storedMiddlewares).toHaveLength(1)
      expect(storedMiddlewares[0]).toBe(boundMethod)
    })
  })
})
