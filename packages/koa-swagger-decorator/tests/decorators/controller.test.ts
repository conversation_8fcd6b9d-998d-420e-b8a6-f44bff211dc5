/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-12-25 09:31:39
 * @LastEditTime: 2025-07-17 15:07:53
 * @LastEditors: shaojun
 * @Description: Controller装饰器测试
 */

import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, RouteConfig } from '../../src/decorators'

describe('controller装饰器测试', () => {
  describe('@Controller装饰器基础功能', () => {
    it('应该正确设置基础控制器配置', () => {
      const config = {
        tags: ['test'],
        paths: {
          parameters: [
            {
              name: 'test-header',
              in: 'header',
              required: true,
              schema: { type: 'string' },
            },
          ],
        },
      }

      @Controller(config)
      class TestController { }

      // 验证元数据正确存储
      const metadataKeys = Reflect.getMetadataKeys(TestController)
      expect(metadataKeys).toContain('swagger:controller')

      const storedConfig = Reflect.getMetadata(
        'swagger:controller',
        TestController,
      )
      expect(storedConfig).toEqual(config)
    })

    it('应该支持复杂的控制器配置', () => {
      const complexConfig = {
        tags: ['用户管理', '权限控制'],
        paths: {
          parameters: [
            {
              name: 'Authorization',
              in: 'header',
              required: true,
              schema: { type: 'string' },
              description: 'JWT令牌',
            },
            {
              name: 'X-API-Version',
              in: 'header',
              required: false,
              schema: {
                type: 'string',
                enum: ['v1', 'v2'],
                default: 'v1',
              },
              description: 'API版本',
            },
          ],
        },
        components: {
          parameters: {
            CommonAuth: {
              name: 'X-Auth-Token',
              in: 'header',
              required: true,
              schema: { type: 'string' },
              description: '通用认证令牌',
            },
            PaginationLimit: {
              name: 'limit',
              in: 'query',
              required: false,
              schema: {
                type: 'number',
                minimum: 1,
                maximum: 100,
                default: 10,
              },
              description: '分页大小',
            },
          },
          schemas: {
            ErrorResponse: {
              type: 'object',
              properties: {
                code: { type: 'number' },
                message: { type: 'string' },
                details: { type: 'object' },
              },
              required: ['code', 'message'],
            },
          },
        },
      }

      @Controller(complexConfig)
      class ComplexController { }

      const storedConfig = Reflect.getMetadata(
        'swagger:controller',
        ComplexController,
      )
      expect(storedConfig).toEqual(complexConfig)
      expect(storedConfig.tags).toHaveLength(2)
      expect(storedConfig.paths.parameters).toHaveLength(2)
      expect(storedConfig.components.parameters).toHaveProperty('CommonAuth')
      expect(storedConfig.components.schemas).toHaveProperty('ErrorResponse')
    })

    it('应该支持最小配置', () => {
      const minimalConfig = {
        tags: ['minimal'],
      }

      @Controller(minimalConfig)
      class MinimalController { }

      const storedConfig = Reflect.getMetadata(
        'swagger:controller',
        MinimalController,
      )
      expect(storedConfig).toEqual(minimalConfig)
    })
  })

  describe('@Controller装饰器错误处理', () => {
    it('应该在配置为空时抛出错误', () => {
      expect(() => {
        @Controller(null as any)
        class InvalidController { }
      }).toThrow('@Controller: Configuration is required')
    })

    it('应该在配置不是对象时抛出错误', () => {
      expect(() => {
        @Controller('invalid' as any)
        class InvalidController { }
      }).toThrow('@Controller: Invalid configuration: Config must be an object')
    })

    it('应该在配置为undefined时抛出错误', () => {
      expect(() => {
        @Controller(undefined as any)
        class InvalidController { }
      }).toThrow('@Controller: Configuration is required')
    })
  })

  describe('@Controller装饰器与其他装饰器组合', () => {
    it('应该与RouteConfig和Body装饰器正确组合', () => {
      const controllerConfig = {
        tags: ['组合测试'],
        paths: {
          parameters: [
            {
              name: 'X-Request-ID',
              in: 'header',
              required: true,
              schema: { type: 'string' },
            },
          ],
        },
      }

      const userSchema = z.object({
        name: z.string(),
        email: z.string().email(),
        age: z.number().min(0).max(120),
      })

      @Controller(controllerConfig)
      class CombinedController {
        @RouteConfig({
          method: 'post',
          path: '/users',
          summary: '创建用户',
        })
        @Body(userSchema)
        createUser() {
          return 'created'
        }

        @RouteConfig({
          method: 'get',
          path: '/users/{id}',
          summary: '获取用户',
        })
        getUser() {
          return 'user'
        }
      }

      // 验证Controller元数据
      const controllerMeta = Reflect.getMetadata(
        'swagger:controller',
        CombinedController,
      )
      expect(controllerMeta).toEqual(controllerConfig)

      // 验证RouteConfig元数据
      const createRouteMeta = Reflect.getMetadata(
        'swagger:routeConfig',
        CombinedController.prototype,
        'createUser',
      )
      expect(createRouteMeta.method).toBe('post')
      expect(createRouteMeta.path).toBe('/users')

      // 验证Body元数据
      const bodyMeta = Reflect.getMetadata(
        'swagger:body',
        CombinedController.prototype,
        'createUser',
      )
      expect(bodyMeta).toBe(userSchema)
    })

    it('应该支持多个控制器独立配置', () => {
      const userConfig = { tags: ['用户'] }
      const postConfig = { tags: ['文章'] }

      @Controller(userConfig)
      class UserController { }

      @Controller(postConfig)
      class PostController { }

      const userMeta = Reflect.getMetadata('swagger:controller', UserController)
      const postMeta = Reflect.getMetadata('swagger:controller', PostController)

      expect(userMeta).toEqual(userConfig)
      expect(postMeta).toEqual(postConfig)
      expect(userMeta).not.toEqual(postMeta)
    })
  })

  describe('@Controller装饰器配置验证', () => {
    it('应该正确处理空数组配置', () => {
      const config = {
        tags: [],
        paths: {
          parameters: [],
        },
      }

      @Controller(config)
      class EmptyArrayController { }

      const storedConfig = Reflect.getMetadata(
        'swagger:controller',
        EmptyArrayController,
      )
      expect(storedConfig).toEqual(config)
      expect(storedConfig.tags).toHaveLength(0)
      expect(storedConfig.paths.parameters).toHaveLength(0)
    })

    it('应该正确处理嵌套对象配置', () => {
      const config = {
        tags: ['嵌套测试'],
        components: {
          parameters: {
            NestedParam: {
              name: 'nested',
              in: 'query',
              required: false,
              schema: {
                type: 'object',
                properties: {
                  filter: {
                    type: 'object',
                    properties: {
                      field: { type: 'string' },
                      value: { type: 'string' },
                    },
                  },
                },
              },
            },
          },
        },
      }

      @Controller(config)
      class NestedController { }

      const storedConfig = Reflect.getMetadata(
        'swagger:controller',
        NestedController,
      )
      expect(storedConfig).toEqual(config)
      expect(storedConfig.components.parameters.NestedParam.schema.properties).toHaveProperty('filter')
    })
  })
})
