import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, generateSwaggerDoc, Responses, RouteConfig } from '../../src'
import { useRegistry } from '../../src/registry'

const { getRegistry, resetRegistry } = useRegistry()

describe('装饰器边界条件测试', () => {
  beforeEach(() => {
    resetRegistry()
  })

  describe('空控制器类测试', () => {
    it('应该处理没有方法的控制器', () => {
      @Controller({ tags: ['空控制器'] })
      class EmptyController {}

      expect(() => {
        generateSwaggerDoc(getRegistry(), [EmptyController])
      }).not.toThrow()
    })

    it('应该处理只有构造函数的控制器', () => {
      @Controller({ tags: ['构造函数控制器'] })
      class ConstructorOnlyController {
        constructor() {
          // 构造函数应该被忽略
        }
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [ConstructorOnlyController])
      }).not.toThrow()
    })

    it('应该处理没有装饰器的方法', () => {
      @Controller({ tags: ['混合控制器'] })
      class MixedController {
        @RouteConfig({ method: 'get', path: '/test' })
        decoratedMethod() {}

        // 这个方法没有装饰器，应该被忽略
        plainMethod() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [MixedController])
      expect(docs.paths['/test']).toBeDefined()
      expect(Object.keys(docs.paths)).toHaveLength(1)
    })
  })

  describe('深度嵌套 Schema 测试', () => {
    it('应该处理深度嵌套的对象 Schema', () => {
      const DeepNestedSchema = z.object({
        level1: z.object({
          level2: z.object({
            level3: z.object({
              level4: z.object({
                level5: z.object({
                  value: z.string(),
                }),
              }),
            }),
          }),
        }),
      })

      @Controller({ tags: ['深度嵌套'] })
      class DeepNestedController {
        @RouteConfig({ method: 'post', path: '/deep' })
        @Body(DeepNestedSchema)
        @Responses(DeepNestedSchema)
        deepMethod() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [DeepNestedController])
      }).not.toThrow()
    })

    it('应该处理复杂的数组嵌套', () => {
      const ComplexArraySchema = z.object({
        items: z.array(
          z.object({
            nested: z.array(
              z.object({
                deepNested: z.array(z.string()),
              }),
            ),
          }),
        ),
      })

      @Controller({ tags: ['复杂数组'] })
      class ComplexArrayController {
        @RouteConfig({ method: 'post', path: '/complex-array' })
        @Body(ComplexArraySchema)
        complexArrayMethod() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [ComplexArrayController])
      }).not.toThrow()
    })

    it('应该处理循环引用的 Schema', () => {
      // 创建一个自引用的 Schema - 使用 z.object 而不是 z.lazy
      const CategorySchema = z.object({
        id: z.string(),
        name: z.string(),
        children: z.array(z.object({
          id: z.string(),
          name: z.string(),
          // 简化循环引用，避免使用 z.lazy
        })).optional(),
      })

      @Controller({ tags: ['循环引用'] })
      class CircularController {
        @RouteConfig({ method: 'post', path: '/circular' })
        @Body(CategorySchema)
        circularMethod() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [CircularController])
      }).not.toThrow()
    })
  })

  describe('特殊字符路径测试', () => {
    it('应该处理包含特殊字符的路径', () => {
      @Controller({ tags: ['特殊字符'] })
      class SpecialCharController {
        @RouteConfig({ method: 'get', path: '/api/v1/users/{user-id}/posts/{post_id}' })
        specialPathMethod() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [SpecialCharController])
      expect(docs.paths['/api/v1/users/{user-id}/posts/{post_id}']).toBeDefined()
    })

    it('应该处理中文路径', () => {
      @Controller({ tags: ['中文路径'] })
      class ChinesePathController {
        @RouteConfig({ method: 'get', path: '/用户/信息' })
        chinesePathMethod() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [ChinesePathController])
      expect(docs.paths['/用户/信息']).toBeDefined()
    })

    it('应该处理 URL 编码的路径', () => {
      @Controller({ tags: ['URL编码'] })
      class EncodedPathController {
        @RouteConfig({ method: 'get', path: '/api/search?q=%E6%B5%8B%E8%AF%95' })
        encodedPathMethod() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [EncodedPathController])
      expect(docs.paths['/api/search?q=%E6%B5%8B%E8%AF%95']).toBeDefined()
    })
  })

  describe('极端数据类型测试', () => {
    it('应该处理基本 Zod 类型', () => {
      // 只测试支持的类型，移除不支持的类型
      const BasicTypesSchema = z.object({
        string: z.string(),
        number: z.number(),
        boolean: z.boolean(),
        date: z.date(),
        // 移除不支持的类型: bigint, null, undefined, void, any, unknown, never
        literal: z.literal('test'),
        enum: z.enum(['A', 'B', 'C']),
        nativeEnum: z.nativeEnum({ A: 'a', B: 'b' }),
        optional: z.string().optional(),
        nullable: z.string().nullable(),
        array: z.array(z.string()),
        tuple: z.tuple([z.string(), z.number()]),
        record: z.record(z.string()),
        // 移除不支持的类型: map, set
        union: z.union([z.string(), z.number()]),
        discriminatedUnion: z.discriminatedUnion('type', [
          z.object({ type: z.literal('A'), value: z.string() }),
          z.object({ type: z.literal('B'), value: z.number() }),
        ]),
        intersection: z.intersection(
          z.object({ a: z.string() }),
          z.object({ b: z.number() }),
        ),
      })

      @Controller({ tags: ['基本类型'] })
      class BasicTypesController {
        @RouteConfig({ method: 'post', path: '/basic-types' })
        @Body(BasicTypesSchema)
        basicTypesMethod() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [BasicTypesController])
      }).not.toThrow()
    })

    it('应该处理带有复杂验证的 Schema', () => {
      const ComplexValidationSchema = z.object({
        email: z.string().email(),
        url: z.string().url(),
        uuid: z.string().uuid(),
        min: z.string().min(5),
        max: z.string().max(10),
        length: z.string().length(8),
        regex: z.string().regex(/^[A-Z]+$/),
        transform: z.string().transform((s) => s.toUpperCase()),
        refine: z.string().refine((s) => s.length > 0, { message: '不能为空' }),
        superRefine: z.string().superRefine((val, ctx) => {
          if (val.length < 3) {
            ctx.addIssue({
              code: z.ZodIssueCode.too_small,
              minimum: 3,
              type: 'string',
              inclusive: true,
              message: '长度必须至少为3',
            })
          }
        }),
      })

      @Controller({ tags: ['复杂验证'] })
      class ComplexValidationController {
        @RouteConfig({ method: 'post', path: '/complex-validation' })
        @Body(ComplexValidationSchema)
        complexValidationMethod() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [ComplexValidationController])
      }).not.toThrow()
    })
  })

  describe('大量数据测试', () => {
    it('应该处理大量控制器', () => {
      const controllers = []

      for (let i = 0; i < 100; i++) {
        @Controller({ tags: [`控制器${i}`] })
        class DynamicController {
          @RouteConfig({ method: 'get', path: `/api/test${i}` })
          testMethod() {}
        }

        Object.defineProperty(DynamicController, 'name', { value: `Controller${i}` })
        controllers.push(DynamicController)
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), controllers)
      }).not.toThrow()
    })

    it('应该处理单个控制器的大量方法', () => {
      @Controller({ tags: ['大量方法'] })
      class ManyMethodsController {}

      // 动态添加大量方法
      for (let i = 0; i < 50; i++) {
        const methodName = `method${i}`
        ManyMethodsController.prototype[methodName] = function () {}

        RouteConfig({ method: 'get', path: `/method${i}` })(
          ManyMethodsController.prototype,
          methodName,
          Object.getOwnPropertyDescriptor(ManyMethodsController.prototype, methodName)!,
        )
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [ManyMethodsController])
      }).not.toThrow()
    })
  })

  describe('内存和性能边界测试', () => {
    it('应该处理非常大的 Schema', () => {
      const largeObject: any = {}

      // 创建一个有1000个属性的对象
      for (let i = 0; i < 1000; i++) {
        largeObject[`field${i}`] = z.string()
      }

      const LargeSchema = z.object(largeObject)

      @Controller({ tags: ['大Schema'] })
      class LargeSchemaController {
        @RouteConfig({ method: 'post', path: '/large-schema' })
        @Body(LargeSchema)
        largeSchemaMethod() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [LargeSchemaController])
      }).not.toThrow()
    })

    it('应该处理深度递归的 Schema', () => {
      let deepSchema = z.string()

      // 创建深度为50的嵌套对象
      for (let i = 0; i < 50; i++) {
        deepSchema = z.object({
          [`level${i}`]: deepSchema,
        })
      }

      @Controller({ tags: ['深度递归'] })
      class DeepRecursionController {
        @RouteConfig({ method: 'post', path: '/deep-recursion' })
        @Body(deepSchema)
        deepRecursionMethod() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [DeepRecursionController])
      }).not.toThrow()
    })
  })
})
