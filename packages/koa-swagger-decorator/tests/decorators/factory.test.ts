/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-12-25 16:15:00
 * @LastEditTime: 2024-12-25 16:15:00
 * @LastEditors: s<PERSON>jun
 * @Description: 装饰器工厂测试
 */

import { describe, expect, it, vi } from 'vitest'
import {
  createDecorator,
  isClassDecorator,
  isDecorator,
  isMethodDecorator,
  isParameterDecorator,
  isPropertyDecorator,
} from '../../src/decorators/factory'

describe('装饰器工厂测试', () => {
  describe('createDecorator函数基础功能', () => {
    it('应该创建类装饰器', () => {
      const metadata = { test: 'class' }
      const handler = vi.fn((target) => target)

      const decorator = createDecorator({
        type: 'class',
        metadata,
        handler,
      })

      // 验证返回的是函数
      expect(typeof decorator).toBe('function')

      // 应用装饰器
      @(decorator as ClassDecorator)
      class TestClass {}

      // 验证handler被调用
      expect(handler).toHaveBeenCalledWith(TestClass)
    })

    it('应该创建方法装饰器', () => {
      const metadata = { test: 'method' }
      const handler = vi.fn((target, propertyKey, descriptor) => descriptor)

      const decorator = createDecorator({
        type: 'method',
        metadata,
        handler,
      })

      expect(typeof decorator).toBe('function')

      class TestClass {
        @(decorator as MethodDecorator)
        testMethod() {}
      }

      // 验证handler被调用
      expect(handler).toHaveBeenCalledWith(
        TestClass.prototype,
        'testMethod',
        expect.any(Object),
      )
    })

    it('应该创建属性装饰器', () => {
      const metadata = { test: 'property' }
      const handler = vi.fn()

      const decorator = createDecorator({
        type: 'property',
        metadata,
        handler,
      })

      expect(typeof decorator).toBe('function')

      class TestClass {
        @(decorator as PropertyDecorator)
        testProperty: string = 'test'
      }

      // 验证handler被调用
      expect(handler).toHaveBeenCalledWith(TestClass.prototype, 'testProperty')
    })

    it('应该创建参数装饰器', () => {
      const metadata = { test: 'parameter' }
      const handler = vi.fn()

      const decorator = createDecorator({
        type: 'parameter',
        metadata,
        handler,
      })

      expect(typeof decorator).toBe('function')

      class TestClass {
        testMethod(@(decorator as ParameterDecorator) param: string) {}
      }

      // 验证handler被调用，参数装饰器会传递 parameterIndex
      expect(handler).toHaveBeenCalledWith(TestClass.prototype, 'testMethod', 0)
    })
  })

  describe('createDecorator函数元数据处理', () => {
    it('应该正确设置类装饰器的自定义元数据', () => {
      const metadata = { className: 'TestClass', version: '1.0' }

      const decorator = createDecorator({
        type: 'class',
        metadata,
      })

      @(decorator as ClassDecorator)
      class TestClass {}

      // 验证自定义元数据被设置
      const storedMetadata = Reflect.getMetadata(
        'custom:class:metadata',
        TestClass.prototype,
        '',
      )
      expect(storedMetadata).toEqual(metadata)
    })

    it('应该正确设置方法装饰器的自定义元数据', () => {
      const metadata = { methodName: 'testMethod', cached: true }

      const decorator = createDecorator({
        type: 'method',
        metadata,
      })

      class TestClass {
        @(decorator as MethodDecorator)
        testMethod() {}
      }

      // 验证自定义元数据被设置
      const storedMetadata = Reflect.getMetadata(
        'custom:metadata',
        TestClass.prototype,
        'testMethod',
      )
      expect(storedMetadata).toEqual(metadata)
    })

    it('应该正确设置属性装饰器的自定义元数据', () => {
      const metadata = { propertyType: 'string', required: true }

      const decorator = createDecorator({
        type: 'property',
        metadata,
      })

      class TestClass {
        @(decorator as PropertyDecorator)
        testProperty: string = 'test'
      }

      // 验证自定义元数据被设置
      const storedMetadata = Reflect.getMetadata(
        'custom:metadata',
        TestClass.prototype,
        'testProperty',
      )
      expect(storedMetadata).toEqual(metadata)
    })

    it('应该正确设置参数装饰器的自定义元数据', () => {
      const metadata = { parameterType: 'string', validation: 'required' }

      const decorator = createDecorator({
        type: 'parameter',
        metadata,
      })

      class TestClass {
        testMethod(@(decorator as ParameterDecorator) param: string) {}
      }

      // 验证自定义元数据被设置
      const storedMetadata = Reflect.getMetadata(
        'custom:metadata',
        TestClass.prototype,
        'testMethod:0',
      )
      expect(storedMetadata).toEqual(metadata)
    })
  })

  describe('createDecorator函数错误处理', () => {
    it('应该在不支持的装饰器类型时抛出错误', () => {
      expect(() => {
        createDecorator({
          type: 'unsupported' as any,
          metadata: {},
        })
      }).toThrow('不支持的装饰器类型: unsupported')
    })

    it('应该在没有type时抛出错误', () => {
      expect(() => {
        createDecorator({
          metadata: {},
        } as any)
      }).toThrow()
    })
  })

  describe('createDecorator函数高级功能', () => {
    it('应该支持无handler的装饰器', () => {
      const decorator = createDecorator({
        type: 'class',
        metadata: { noHandler: true },
      })

      expect(() => {
        @(decorator as ClassDecorator)
        class TestClass {}
      }).not.toThrow()
    })

    it('应该支持无metadata的装饰器', () => {
      const handler = vi.fn((target) => target)

      const decorator = createDecorator({
        type: 'class',
        handler,
      })

      @(decorator as ClassDecorator)
      class TestClass {}

      expect(handler).toHaveBeenCalledWith(TestClass)
    })

    it('应该支持handler修改返回值', () => {
      const handler = vi.fn((target, propertyKey, descriptor) => {
        if (descriptor) {
          const originalMethod = descriptor.value
          descriptor.value = function (...args: any[]) {
            return `decorated: ${originalMethod.apply(this, args)}`
          }
        }
        return descriptor
      })

      const decorator = createDecorator({
        type: 'method',
        handler,
      })

      class TestClass {
        @(decorator as MethodDecorator)
        testMethod() {
          return 'original'
        }
      }

      const instance = new TestClass()
      expect(instance.testMethod()).toBe('decorated: original')
    })

    it('应该支持复杂的元数据结构', () => {
      const complexMetadata = {
        config: {
          validation: {
            rules: ['required', 'string'],
            messages: {
              required: 'Field is required',
              string: 'Must be a string',
            },
          },
          transform: {
            trim: true,
            lowercase: true,
          },
        },
        hooks: {
          beforeValidation: 'trimAndLowercase',
          afterValidation: 'logResult',
        },
        dependencies: ['logger', 'validator'],
      }

      const decorator = createDecorator({
        type: 'property',
        metadata: complexMetadata,
      })

      class TestClass {
        @(decorator as PropertyDecorator)
        complexProperty: string = 'test'
      }

      const storedMetadata = Reflect.getMetadata(
        'custom:metadata',
        TestClass.prototype,
        'complexProperty',
      )
      expect(storedMetadata).toEqual(complexMetadata)
    })
  })

  describe('装饰器类型检查函数', () => {
    describe('isDecorator函数', () => {
      it('应该正确识别装饰器函数', () => {
        const decorator = () => {}
        expect(isDecorator(decorator)).toBe(true)

        expect(isDecorator(null)).toBe(false)
        expect(isDecorator(undefined)).toBe(false)
        expect(isDecorator('string')).toBe(false)
        expect(isDecorator(123)).toBe(false)
        expect(isDecorator({})).toBe(false)
      })
    })

    describe('isClassDecorator函数', () => {
      it('应该正确识别类装饰器', () => {
        const classDecorator = () => {}
        expect(isClassDecorator('class', classDecorator)).toBe(true)
        expect(isClassDecorator('method', classDecorator)).toBe(false)
        expect(isClassDecorator('class', null)).toBe(false)
      })
    })

    describe('isMethodDecorator函数', () => {
      it('应该正确识别方法装饰器', () => {
        const methodDecorator = () => {}
        expect(isMethodDecorator('method', methodDecorator)).toBe(true)
        expect(isMethodDecorator('class', methodDecorator)).toBe(false)
        expect(isMethodDecorator('method', null)).toBe(false)
      })
    })

    describe('isPropertyDecorator函数', () => {
      it('应该正确识别属性装饰器', () => {
        const propertyDecorator = () => {}
        expect(isPropertyDecorator('property', propertyDecorator)).toBe(true)
        expect(isPropertyDecorator('method', propertyDecorator)).toBe(false)
        expect(isPropertyDecorator('property', null)).toBe(false)
      })
    })

    describe('isParameterDecorator函数', () => {
      it('应该正确识别参数装饰器', () => {
        const parameterDecorator = () => {}
        expect(isParameterDecorator('parameter', parameterDecorator)).toBe(true)
        expect(isParameterDecorator('method', parameterDecorator)).toBe(false)
        expect(isParameterDecorator('parameter', null)).toBe(false)
      })
    })
  })

  describe('装饰器组合测试', () => {
    it('应该支持多个装饰器组合', () => {
      const classHandler = vi.fn((target) => target)
      const methodHandler = vi.fn((target, propertyKey, descriptor) => descriptor)

      const classDecorator = createDecorator({
        type: 'class',
        metadata: { type: 'controller' },
        handler: classHandler,
      })

      const methodDecorator = createDecorator({
        type: 'method',
        metadata: { type: 'route' },
        handler: methodHandler,
      })

      @(classDecorator as ClassDecorator)
      class TestController {
        @(methodDecorator as MethodDecorator)
        testRoute() {}
      }

      expect(classHandler).toHaveBeenCalledWith(TestController)
      expect(methodHandler).toHaveBeenCalledWith(
        TestController.prototype,
        'testRoute',
        expect.any(Object),
      )

      // 验证元数据都被正确设置
      const classMetadata = Reflect.getMetadata(
        'custom:class:metadata',
        TestController.prototype,
        '',
      )
      const methodMetadata = Reflect.getMetadata(
        'custom:metadata',
        TestController.prototype,
        'testRoute',
      )

      expect(classMetadata).toEqual({ type: 'controller' })
      expect(methodMetadata).toEqual({ type: 'route' })
    })

    it('应该支持同一方法上的多个装饰器', () => {
      const decorator1 = createDecorator({
        type: 'method',
        metadata: { order: 1 },
      })

      const decorator2 = createDecorator({
        type: 'method',
        metadata: { order: 2 },
      })

      class TestClass {
        @(decorator1 as MethodDecorator)
        @(decorator2 as MethodDecorator)
        testMethod() {}
      }

      // 验证装饰器的元数据
      const metadata1 = Reflect.getMetadata(
        'custom:metadata',
        TestClass.prototype,
        'testMethod',
      )
      // 装饰器按照从下到上的顺序执行，第一个装饰器的元数据被保留
      expect(metadata1).toEqual({ order: 1 })
    })
  })

  describe('装饰器实际应用场景', () => {
    it('应该支持创建验证装饰器', () => {
      const createValidationDecorator = (rules: string[]) => {
        return createDecorator({
          type: 'property',
          metadata: { validation: rules },
          handler: (target, propertyKey) => {
            // 可以在这里添加验证逻辑
          },
        })
      }

      const RequiredString = createValidationDecorator(['required', 'string'])
      const EmailValidation = createValidationDecorator(['required', 'email'])

      class UserModel {
        @(RequiredString as PropertyDecorator)
        name: string = ''

        @(EmailValidation as PropertyDecorator)
        email: string = ''
      }

      const nameMetadata = Reflect.getMetadata(
        'custom:metadata',
        UserModel.prototype,
        'name',
      )
      const emailMetadata = Reflect.getMetadata(
        'custom:metadata',
        UserModel.prototype,
        'email',
      )

      expect(nameMetadata.validation).toEqual(['required', 'string'])
      expect(emailMetadata.validation).toEqual(['required', 'email'])
    })

    it('应该支持创建缓存装饰器', () => {
      const createCacheDecorator = (ttl: number) => {
        return createDecorator({
          type: 'method',
          metadata: { cache: { ttl } },
          handler: (target, propertyKey, descriptor) => {
            if (descriptor) {
              const originalMethod = descriptor.value
              descriptor.value = function (...args: any[]) {
                // 简单的缓存逻辑模拟
                const cacheKey = `${propertyKey}_${JSON.stringify(args)}`
                // 这里可以实现真正的缓存逻辑
                return originalMethod.apply(this, args)
              }
            }
            return descriptor
          },
        })
      }

      const Cache = createCacheDecorator(300) // 5分钟缓存

      class DataService {
        @(Cache as MethodDecorator)
        getData(id: string) {
          return `data for ${id}`
        }
      }

      const cacheMetadata = Reflect.getMetadata(
        'custom:metadata',
        DataService.prototype,
        'getData',
      )
      expect(cacheMetadata.cache.ttl).toBe(300)

      const service = new DataService()
      expect(service.getData('123')).toBe('data for 123')
    })

    it('应该支持创建日志装饰器', () => {
      const LogExecution = createDecorator({
        type: 'method',
        metadata: { logging: { enabled: true, level: 'info' } },
        handler: (target, propertyKey, descriptor) => {
          if (descriptor) {
            const originalMethod = descriptor.value
            descriptor.value = function (...args: any[]) {
              console.log(`Executing ${String(propertyKey)} with args:`, args)
              const result = originalMethod.apply(this, args)
              console.log(`Finished ${String(propertyKey)} with result:`, result)
              return result
            }
          }
          return descriptor
        },
      })

      class BusinessService {
        @(LogExecution as MethodDecorator)
        processOrder(orderId: string) {
          return `processed order ${orderId}`
        }
      }

      const logMetadata = Reflect.getMetadata(
        'custom:metadata',
        BusinessService.prototype,
        'processOrder',
      )
      expect(logMetadata.logging.enabled).toBe(true)
      expect(logMetadata.logging.level).toBe('info')
    })
  })

  describe('装饰器错误恢复测试', () => {
    it('应该在handler抛出错误时正常处理', () => {
      const errorHandler = vi.fn(() => {
        throw new Error('Handler error')
      })

      const decorator = createDecorator({
        type: 'class',
        handler: errorHandler,
      })

      expect(() => {
        @(decorator as ClassDecorator)
        class TestClass {}
      }).toThrow('Handler error')

      expect(errorHandler).toHaveBeenCalled()
    })

    it('应该在方法装饰器handler抛出错误时正常处理', () => {
      const errorHandler = vi.fn(() => {
        throw new Error('Method handler error')
      })

      const decorator = createDecorator({
        type: 'method',
        handler: errorHandler,
      })

      expect(() => {
        class TestClass {
          @(decorator as MethodDecorator)
          testMethod() {}
        }
      }).toThrow('Method handler error')
    })
  })
})
