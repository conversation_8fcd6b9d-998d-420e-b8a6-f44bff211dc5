/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-25 16:00:00
 * @LastEditTime: 2025-07-10 11:51:20
 * @LastEditors: shaojun
 * @Description: RouteConfig装饰器测试
 */

import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, Responses, RouteConfig } from '../../src/decorators'

describe('routeConfig装饰器测试', () => {
  describe('@RouteConfig装饰器基础功能', () => {
    it('应该正确设置基础路由配置', () => {
      const routeConfig = {
        method: 'get',
        path: '/users',
        summary: '获取用户列表',
      }

      @Controller({ tags: ['test'] })
      class TestController {
        @RouteConfig(routeConfig)
        getUsers() {
          return 'users'
        }
      }

      // 验证元数据存储
      const metadataKeys = Reflect.getMetadataKeys(TestController.prototype, 'getUsers')
      expect(metadataKeys).toContain('swagger:routeConfig')

      const storedConfig = Reflect.getMetadata(
        'swagger:routeConfig',
        TestController.prototype,
        'getUsers',
      )
      expect(storedConfig.method).toBe('get')
      expect(storedConfig.path).toBe('/users')
      expect(storedConfig.summary).toBe('获取用户列表')
    })

    it('应该自动设置operationId', () => {
      const routeConfig = {
        method: 'post',
        path: '/users',
        summary: '创建用户',
      }

      @Controller({ tags: ['test'] })
      class TestController {
        @RouteConfig(routeConfig)
        createUser() {
          return 'created'
        }
      }

      const storedConfig = Reflect.getMetadata(
        'swagger:routeConfig',
        TestController.prototype,
        'createUser',
      )
      expect(storedConfig.operationId).toBe('createUser')
    })

    it('应该保留自定义operationId', () => {
      const routeConfig = {
        method: 'put',
        path: '/users/{id}',
        summary: '更新用户',
        operationId: 'customUpdateUser',
      }

      @Controller({ tags: ['test'] })
      class TestController {
        @RouteConfig(routeConfig)
        updateUser() {
          return 'updated'
        }
      }

      const storedConfig = Reflect.getMetadata(
        'swagger:routeConfig',
        TestController.prototype,
        'updateUser',
      )
      expect(storedConfig.operationId).toBe('customUpdateUser')
    })
  })

  describe('@RouteConfig装饰器复杂配置', () => {
    it('应该支持完整的路由配置', () => {
      const complexRouteConfig = {
        method: 'patch',
        path: '/users/{id}/profile',
        summary: '更新用户资料',
        description: '更新指定用户的详细资料信息',
        operationId: 'updateUserProfile',
        tags: ['用户管理', '资料管理'],
        deprecated: false,
        parameters: {
          id: {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: '用户ID',
          },
          include: {
            name: 'include',
            in: 'query',
            required: false,
            schema: {
              type: 'array',
              items: { type: 'string' },
              enum: ['avatar', 'preferences', 'stats'],
            },
            description: '包含的额外信息',
          },
        },
        responses: {
          200: { description: '更新成功' },
          400: { description: '参数错误' },
          404: { description: '用户不存在' },
          500: { description: '服务器错误' },
        },
        security: [{ Bearer: [] }],
        servers: [
          { url: 'https://api.test.com', description: '测试环境' },
          { url: 'https://api.prod.com', description: '生产环境' },
        ],
        externalDocs: {
          description: '用户管理API文档',
          url: 'https://docs.example.com/users',
        },
      }

      @Controller({ tags: ['高级测试'] })
      class AdvancedController {
        @RouteConfig(complexRouteConfig)
        updateUserProfile() {
          return 'profile updated'
        }
      }

      const storedConfig = Reflect.getMetadata(
        'swagger:routeConfig',
        AdvancedController.prototype,
        'updateUserProfile',
      )

      expect(storedConfig.method).toBe('patch')
      expect(storedConfig.path).toBe('/users/{id}/profile')
      expect(storedConfig.tags).toEqual(['用户管理', '资料管理'])
      expect(storedConfig.parameters).toHaveProperty('id')
      expect(storedConfig.parameters).toHaveProperty('include')
      expect(storedConfig.responses).toHaveProperty('200')
      expect(storedConfig.security).toEqual([{ Bearer: [] }])
      expect(storedConfig.servers).toHaveLength(2)
      expect(storedConfig.externalDocs).toHaveProperty('url')
    })

    it('应该支持RESTful路由配置', () => {
      @Controller({ tags: ['RESTful'] })
      class RestController {
        @RouteConfig({
          method: 'get',
          path: '/api/v1/resources',
          summary: '列表查询',
        })
        list() {}

        @RouteConfig({
          method: 'get',
          path: '/api/v1/resources/{id}',
          summary: '详情查询',
        })
        detail() {}

        @RouteConfig({
          method: 'post',
          path: '/api/v1/resources',
          summary: '创建资源',
        })
        create() {}

        @RouteConfig({
          method: 'put',
          path: '/api/v1/resources/{id}',
          summary: '更新资源',
        })
        update() {}

        @RouteConfig({
          method: 'delete',
          path: '/api/v1/resources/{id}',
          summary: '删除资源',
        })
        remove() {}
      }

      // 验证所有路由都正确注册
      const methods = ['list', 'detail', 'create', 'update', 'remove']
      const expectedMethods = ['get', 'get', 'post', 'put', 'delete']

      methods.forEach((methodName, index) => {
        const config = Reflect.getMetadata(
          'swagger:routeConfig',
          RestController.prototype,
          methodName,
        )
        expect(config.method).toBe(expectedMethods[index])
        expect(config.path).toContain('/api/v1/resources')
      })
    })
  })

  describe('@RouteConfig装饰器错误处理', () => {
    it('应该在缺少method时抛出错误', () => {
      expect(() => {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({
            path: '/test',
            summary: '测试接口',
          } as any)
          testMethod() {}
        }
      }).toThrow('@RouteConfig: Invalid configuration: method is required')
    })

    it('应该在缺少path时抛出错误', () => {
      expect(() => {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({
            method: 'get',
            summary: '测试接口',
          } as any)
          testMethod() {}
        }
      }).toThrow('@RouteConfig: Invalid configuration: path is required')
    })

    it('应该在method和path都缺少时抛出错误', () => {
      expect(() => {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({
            summary: '测试接口',
          } as any)
          testMethod() {}
        }
      }).toThrow('@RouteConfig: Invalid configuration: method is required, path is required')
    })

    it('应该在空配置时抛出错误', () => {
      expect(() => {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({} as any)
          testMethod() {}
        }
      }).toThrow('@RouteConfig: Invalid configuration: method is required, path is required')
    })
  })

  describe('@RouteConfig装饰器与其他装饰器组合', () => {
    it('应该与Body和Responses装饰器正确组合', () => {
      const userSchema = z.object({
        name: z.string(),
        email: z.string().email(),
        age: z.number().min(0),
      })

      const responseSchema = z.object({
        id: z.string(),
        name: z.string(),
        email: z.string(),
        createdAt: z.string(),
      })

      @Controller({ tags: ['组合测试'] })
      class CombinedController {
        @RouteConfig({
          method: 'post',
          path: '/users',
          summary: '创建用户',
          description: '创建新用户账户',
        })
        @Body(userSchema)
        @Responses(responseSchema)
        createUser() {
          return 'user created'
        }
      }

      // 验证RouteConfig
      const routeConfig = Reflect.getMetadata(
        'swagger:routeConfig',
        CombinedController.prototype,
        'createUser',
      )
      expect(routeConfig.method).toBe('post')
      expect(routeConfig.path).toBe('/users')

      // 验证Body
      const bodyConfig = Reflect.getMetadata(
        'swagger:body',
        CombinedController.prototype,
        'createUser',
      )
      expect(bodyConfig).toBe(userSchema)

      // 验证Responses
      const responsesConfig = Reflect.getMetadata(
        'swagger:responses',
        CombinedController.prototype,
        'createUser',
      )
      expect(responsesConfig).toEqual({
        200: {
          description: 'Success',
          schema: responseSchema,
        },
      })
    })

    it('应该支持多个路由方法在同一控制器中', () => {
      @Controller({ tags: ['多路由'] })
      class MultiRouteController {
        @RouteConfig({ method: 'get', path: '/items' })
        getItems() {}

        @RouteConfig({ method: 'post', path: '/items' })
        createItem() {}

        @RouteConfig({ method: 'get', path: '/items/{id}' })
        getItem() {}

        @RouteConfig({ method: 'put', path: '/items/{id}' })
        updateItem() {}

        @RouteConfig({ method: 'delete', path: '/items/{id}' })
        deleteItem() {}
      }

      const routes = [
        { method: 'getItems', expectedMethod: 'get', expectedPath: '/items' },
        { method: 'createItem', expectedMethod: 'post', expectedPath: '/items' },
        { method: 'getItem', expectedMethod: 'get', expectedPath: '/items/{id}' },
        { method: 'updateItem', expectedMethod: 'put', expectedPath: '/items/{id}' },
        { method: 'deleteItem', expectedMethod: 'delete', expectedPath: '/items/{id}' },
      ]

      routes.forEach(({ method, expectedMethod, expectedPath }) => {
        const config = Reflect.getMetadata(
          'swagger:routeConfig',
          MultiRouteController.prototype,
          method,
        )
        expect(config.method).toBe(expectedMethod)
        expect(config.path).toBe(expectedPath)
        expect(config.operationId).toBe(method)
      })
    })
  })

  describe('@RouteConfig装饰器类型验证', () => {
    it('应该接受所有有效的HTTP方法', () => {
      const httpMethods = ['get', 'post', 'put', 'patch', 'delete', 'head', 'options']

      @Controller({ tags: ['HTTP方法测试'] })
      class HttpMethodController {
        @RouteConfig({ method: 'get', path: '/get' })
        getMethod() {}

        @RouteConfig({ method: 'post', path: '/post' })
        postMethod() {}

        @RouteConfig({ method: 'put', path: '/put' })
        putMethod() {}

        @RouteConfig({ method: 'patch', path: '/patch' })
        patchMethod() {}

        @RouteConfig({ method: 'delete', path: '/delete' })
        deleteMethod() {}

        @RouteConfig({ method: 'head', path: '/head' })
        headMethod() {}

        @RouteConfig({ method: 'options', path: '/options' })
        optionsMethod() {}
      }

      const methods = ['getMethod', 'postMethod', 'putMethod', 'patchMethod', 'deleteMethod', 'headMethod', 'optionsMethod']

      methods.forEach((methodName, index) => {
        const config = Reflect.getMetadata(
          'swagger:routeConfig',
          HttpMethodController.prototype,
          methodName,
        )
        expect(config.method).toBe(httpMethods[index])
      })
    })

    it('应该正确处理路径参数', () => {
      const pathConfigs = [
        { path: '/users/{id}', description: '单个路径参数' },
        { path: '/users/{userId}/posts/{postId}', description: '多个路径参数' },
        { path: '/api/v{version}/users/{id}', description: '版本和ID参数' },
        { path: '/files/{*filepath}', description: '通配符参数' },
      ]

      @Controller({ tags: ['路径参数测试'] })
      class PathParamController {
        @RouteConfig({ method: 'get', path: '/users/{id}' })
        getUserById() {}

        @RouteConfig({ method: 'get', path: '/users/{userId}/posts/{postId}' })
        getUserPost() {}

        @RouteConfig({ method: 'get', path: '/api/v{version}/users/{id}' })
        getVersionedUser() {}

        @RouteConfig({ method: 'get', path: '/files/{*filepath}' })
        getFile() {}
      }

      const methods = ['getUserById', 'getUserPost', 'getVersionedUser', 'getFile']

      methods.forEach((methodName, index) => {
        const config = Reflect.getMetadata(
          'swagger:routeConfig',
          PathParamController.prototype,
          methodName,
        )
        expect(config.path).toBe(pathConfigs[index].path)
      })
    })
  })
})
