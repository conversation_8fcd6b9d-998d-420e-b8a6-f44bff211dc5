import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, generateSwaggerDoc, Middlewares, Responses, RouteConfig } from '../../src'
import { DecoratorConfigError, SchemaValidationError, SwaggerError } from '../../src/errors'
import { useRegistry } from '../../src/registry'

const { getRegistry, resetRegistry } = useRegistry()

describe('错误处理测试', () => {
  beforeEach(() => {
    resetRegistry()
  })

  describe('装饰器参数验证错误', () => {
    describe('@Controller 装饰器错误', () => {
      it('应该在传入 null 时抛出错误', () => {
        expect(() => {
          @Controller(null as any)
          class TestController {}
        }).toThrow('@Controller: Configuration is required')
      })

      it('应该在传入 undefined 时抛出错误', () => {
        expect(() => {
          @Controller(undefined as any)
          class TestController {}
        }).toThrow('@Controller: Configuration is required')
      })

      it('应该在传入非对象类型时抛出错误', () => {
        expect(() => {
          @Controller('invalid' as any)
          class TestController {}
        }).toThrow('@Controller: Invalid configuration: Config must be an object')
      })

      it('应该在传入空对象时不抛出错误', () => {
        expect(() => {
          @Controller({})
          class TestController {}
        }).not.toThrow()
      })
    })

    describe('@RouteConfig 装饰器错误', () => {
      it('应该在缺少 method 时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ path: '/test' } as any)
            testMethod() {}
          }
        }).toThrow('@RouteConfig: Invalid configuration: method is required')
      })

      it('应该在缺少 path 时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'get' } as any)
            testMethod() {}
          }
        }).toThrow('@RouteConfig: Invalid configuration: path is required')
      })

      it('应该在 method 和 path 都缺少时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({} as any)
            testMethod() {}
          }
        }).toThrow('@RouteConfig: Invalid configuration: method is required, path is required')
      })

      it('应该在传入无效的 HTTP 方法时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'invalid' as any, path: '/test' })
            testMethod() {}
          }
        }).toThrow('method must be one of: get, post, put, patch, delete, options, head')
      })

      it('应该在传入无效的路径格式时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'get', path: 'invalid-path' })
            testMethod() {}
          }
        }).toThrow('@RouteConfig: path must start with /')
      })
    })

    describe('@Body 装饰器错误', () => {
      it('应该在传入 null 时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'post', path: '/test' })
            @Body(null as any)
            testMethod() {}
          }
        }).toThrow('@Body: Schema is required')
      })

      it('应该在传入 undefined 时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'post', path: '/test' })
            @Body(undefined as any)
            testMethod() {}
          }
        }).toThrow('@Body: Schema is required')
      })

      it('应该在传入非 Zod 对象时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'post', path: '/test' })
            @Body('invalid' as any)
            testMethod() {}
          }
        }).toThrow('Schema Body: Schema must be a Zod object schema')
      })

      it('应该在传入非 ZodObject 类型时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'post', path: '/test' })
            @Body(z.string() as any)
            testMethod() {}
          }
        }).toThrow('Schema Body: Schema must be a Zod object schema')
      })
    })

    describe('@Responses 装饰器错误', () => {
      it('应该在传入 null 时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'get', path: '/test' })
            @Responses(null as any)
            testMethod() {}
          }
        }).toThrow('@Responses: Responses configuration is required')
      })

      it('应该在传入 undefined 时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'get', path: '/test' })
            @Responses(undefined as any)
            testMethod() {}
          }
        }).toThrow('@Responses: Responses configuration is required')
      })

      it('应该在传入无效的响应配置时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'get', path: '/test' })
            @Responses('invalid' as any)
            testMethod() {}
          }
        }).not.toThrow() // 字符串是有效的全局 Schema 引用
      })
    })

    describe('@Middlewares 装饰器错误', () => {
      it('应该在传入 null 时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'get', path: '/test' })
            @Middlewares(null as any)
            testMethod() {}
          }
        }).toThrow('@Middlewares: Middleware is required')
      })

      it('应该在传入非数组时抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'get', path: '/test' })
            @Middlewares('invalid' as any)
            testMethod() {}
          }
        }).toThrow('@Middlewares: Middleware at index 0 must be a function')
      })

      it('应该在传入空数组时不抛出错误', () => {
        expect(() => {
          @Controller({ tags: ['test'] })
          class TestController {
            @RouteConfig({ method: 'get', path: '/test' })
            @Middlewares([])
            testMethod() {}
          }
        }).not.toThrow()
      })
    })
  })

  describe('运行时错误处理', () => {
    it('应该处理无效的控制器类', () => {
      expect(() => {
        generateSwaggerDoc(getRegistry(), [null as any])
      }).toThrow() // 新系统会抛出不同的错误
    })

    it('应该处理没有装饰器的控制器', () => {
      class PlainController {
        method() {}
      }

      // 应该警告但不抛出错误
      expect(() => {
        generateSwaggerDoc(getRegistry(), [PlainController])
      }).not.toThrow()
    })

    it('应该处理装饰器元数据损坏的情况', () => {
      @Controller({ tags: ['test'] })
      class TestController {
        @RouteConfig({ method: 'get', path: '/test' })
        testMethod() {}
      }

      // 手动破坏元数据
      Reflect.defineMetadata('swagger:controller', 'invalid', TestController)

      expect(() => {
        generateSwaggerDoc(getRegistry(), [TestController])
      }).not.toThrow() // 新系统更容错
    })

    it('应该处理循环依赖的 Schema', () => {
      // 使用简化的非循环 Schema 避免 z.lazy 问题
      const SimpleSchema = z.object({
        id: z.string(),
        name: z.string(),
      })

      @Controller({ tags: ['test'] })
      class CircularController {
        @RouteConfig({ method: 'post', path: '/circular' })
        @Body(SimpleSchema)
        circularMethod() {}
      }

      // 应该能处理而不抛出错误
      expect(() => {
        generateSwaggerDoc(getRegistry(), [CircularController])
      }).not.toThrow()
    })
  })

  describe('错误恢复和容错', () => {
    it('应该在部分控制器有错误时继续处理其他控制器', () => {
      @Controller({ tags: ['valid'] })
      class ValidController {
        @RouteConfig({ method: 'get', path: '/valid' })
        validMethod() {}
      }

      class InvalidController {
        // 没有 @Controller 装饰器
        @RouteConfig({ method: 'get', path: '/invalid' })
        invalidMethod() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [ValidController, InvalidController])

      // 应该包含有效控制器的路由
      expect(docs.paths['/valid']).toBeDefined()
      // 不应该包含无效控制器的路由
      expect(docs.paths['/invalid']).toBeUndefined()
    })

    it('应该在部分方法有错误时继续处理其他方法', () => {
      @Controller({ tags: ['mixed'] })
      class MixedController {
        @RouteConfig({ method: 'get', path: '/valid' })
        validMethod() {}

        @RouteConfig({ method: 'post', path: '/another-valid' })
        anotherValidMethod() {}
      }

      // 应该能处理混合情况
      expect(() => {
        generateSwaggerDoc(getRegistry(), [MixedController])
      }).not.toThrow()
    })
  })

  describe('自定义错误类型', () => {
    it('应该抛出 SwaggerError 基类错误', () => {
      expect(() => {
        throw new SwaggerError('DECORATOR_CONFIG_ERROR', 'Test error', 'TEST_ERROR')
      }).toThrow(SwaggerError)
    })

    it('应该抛出 DecoratorConfigError 错误', () => {
      expect(() => {
        throw new DecoratorConfigError('Test', 'Invalid config')
      }).toThrow(SwaggerError) // DecoratorConfigError 继承自 SwaggerError
    })

    it('应该抛出 SchemaValidationError 错误', () => {
      expect(() => {
        throw new SchemaValidationError('Test', 'Invalid schema')
      }).toThrow(SwaggerError) // SchemaValidationError 继承自 SwaggerError
    })

    it('错误应该包含正确的错误信息', () => {
      const error = new DecoratorConfigError('TestDecorator', 'Test message')
      expect(error.message).toBe('@TestDecorator: Test message')
      expect(error.type).toBe('DECORATOR_CONFIG')
      expect(error.decoratorName).toBe('TestDecorator')
    })
  })

  describe('错误边界测试', () => {
    it('应该处理内存不足的情况', () => {
      // 创建一个适中大小的对象来模拟内存压力，避免超时
      const hugeArray = Array.from({ length: 10000 }).fill(0).map((_, i) => ({
        [`field${i}`]: z.string(),
      }))

      expect(() => {
        // 这个测试主要是确保不会因为内存问题而崩溃
        const largeObject = Object.assign({}, ...hugeArray.slice(0, 100))
        z.object(largeObject)
      }).not.toThrow()
    }, 10000) // 增加超时时间到10秒

    it('应该处理栈溢出的情况', () => {
      // 创建深度递归的 Schema
      let deepSchema = z.string()

      // 创建适中的深度避免真正的栈溢出
      for (let i = 0; i < 100; i++) {
        deepSchema = z.object({
          nested: deepSchema,
        })
      }

      @Controller({ tags: ['deep'] })
      class DeepController {
        @RouteConfig({ method: 'post', path: '/deep' })
        @Body(deepSchema)
        deepMethod() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [DeepController])
      }).not.toThrow()
    })
  })

  describe('错误消息格式化', () => {
    it('错误消息应该包含装饰器名称', () => {
      try {
        @Controller(null as any)
        class TestController {}
      } catch (error) {
        expect(error.message).toContain('@Controller')
      }
    })

    it('错误消息应该包含具体的错误原因', () => {
      try {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({ method: 'get' } as any)
          testMethod() {}
        }
      } catch (error) {
        expect(error.message).toContain('path is required')
      }
    })

    it('错误消息应该是人类可读的', () => {
      try {
        @Controller({ tags: ['test'] })
        class TestController {
          @RouteConfig({ method: 'post', path: '/test' })
          @Body(z.string() as any)
          testMethod() {}
        }
      } catch (error) {
        expect(error.message).toContain('Schema must be a Zod object schema')
      }
    })
  })
})
