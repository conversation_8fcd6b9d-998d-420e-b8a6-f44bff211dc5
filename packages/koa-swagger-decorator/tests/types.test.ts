import type {
  ControllerClass,
  HttpMethod,
  ResponsesConfig,
  StrongRouteConfig,
} from '../src'
/**
 * 类型测试文件
 * 这个文件测试类型系统和运行时错误处理
 */
import { describe, expect, it } from 'vitest'
import {
  Body,
  Controller,
  isHttpMethod,
  isSchemaObject,
  isZodObject,
  isZodType,
  Middlewares,
  RouteConfig,
  SwaggerRouter,
  z,
} from '../src'

describe('类型系统测试', () => {
  it('类型约束测试', () => {
    // 这个测试不会被实际执行，仅用于验证类型系统

    // 测试 HttpMethod 类型
    const validMethod: HttpMethod = 'get'
    // @ts-expect-error - 无效的 HTTP 方法
    const invalidMethod: HttpMethod = 'invalid'

    // 测试 ControllerClass 类型
    @Controller({})
    class TestController {}

    const validController: ControllerClass = TestController
    // @ts-expect-error - 不是一个有效的控制器类
    const invalidController: ControllerClass = {}

    // 测试 RouteConfig 类型
    const validRouteConfig: RouteConfig = {
      method: 'get',
      path: '/test',
    }

    // @ts-expect-error - 缺少必需的 path 字段
    const invalidRouteConfig: RouteConfig = {
      method: 'get',
    }

    // 测试 StrongRouteConfig 类型
    const validStrongRouteConfig: StrongRouteConfig = {
      method: 'post',
      path: '/test',
      className: 'TestController',
      methodName: 'testMethod',
    }

    // 测试 ResponsesConfig 类型
    const validResponsesConfig: ResponsesConfig = {
      200: {
        description: 'Success',
        schema: z.object({ id: z.number() }),
      },
      400: {
        description: 'Bad Request',
      },
    }

    // @ts-expect-error - 缺少必需的 description 字段
    const invalidResponsesConfig: ResponsesConfig = {
      200: {
        schema: z.object({ id: z.number() }),
      },
    }

    // 测试 TypeGuards
    const isValidMethod = isHttpMethod('get') // true
    const isInvalidMethod = isHttpMethod('invalid') // false

    const isValidZodObject = isZodObject(z.object({ id: z.number() })) // true
    const isInvalidZodObject = isZodObject('not a zod object') // false

    // 测试 SwaggerRouter
    const router = new SwaggerRouter({
      swaggerJsonEndpoint: '/api-docs.json',
      swaggerHtmlEndpoint: '/api-docs',
      validateRequest: true,
    })

    router.applyRoute(TestController)
    // @ts-expect-error - 不是一个有效的控制器类
    router.applyRoute({})
  })
})

// 测试装饰器运行时错误处理
describe('装饰器运行时错误处理测试', () => {
  it('controller 装饰器错误处理', () => {
    // 测试无效的 tags 类型
    expect(() => {
      @Controller({
        tags: 'Invalid' as any,
      })
      class InvalidController {}
    }).toThrow('@Controller: tags must be an array of strings')
  })

  it('routeConfig 装饰器错误处理', () => {
    // 测试无效的 method 类型
    expect(() => {
      class TestController {
        @RouteConfig({
          method: 'invalid' as any,
          path: '/test',
        })
        invalidMethod() {}
      }
    }).toThrow('@RouteConfig: method must be one of: get, post, put, patch, delete, options, head')
  })

  it('body 装饰器错误处理', () => {
    // 测试无效的 Body 参数
    expect(() => {
      class TestController {
        @RouteConfig({
          method: 'post',
          path: '/test',
        })
        @Body(null as any)
        invalidMethod() {}
      }
    }).toThrow('Schema is required')
  })

  it('middlewares 装饰器错误处理', () => {
    // 测试无效的中间件参数
    expect(() => {
      class TestController {
        @RouteConfig({
          method: 'get',
          path: '/test',
        })
        @Middlewares(null as any)
        invalidMethod() {}
      }
    }).toThrow('Middleware is required')
  })
})
