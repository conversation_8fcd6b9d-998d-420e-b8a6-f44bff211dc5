/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-04-28 17:28:16
 * @LastEditTime: 2024-04-28 17:28:17
 * @LastEditors: shaojun
 * @Description:
 */
/*
 * @Author: shao<PERSON>
 * @Date: 2024-03-22 09:07:30
 * @LastEditTime: 2024-03-22 09:19:08
 * @LastEditors: shaojun
 * @Description:
 */
import { describe, expect, it } from 'vitest'

import { firstUpperCase } from '../src/utils'

describe('template', () => {
  it('firstUpperCase', () => {
    const methodName = 'uploadFile'
    expect(firstUpperCase(methodName)).toBe('UploadFile')
  })
})
