import type { SwaggerUIConfig } from '../../src/ui'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { createCustomSwaggerUI, createSwaggerUI } from '../../src/ui'

// Mock koa2-swagger-ui
vi.mock('koa2-swagger-ui', () => ({
  koaSwagger: vi.fn((config) => {
    return (ctx: any, next: any) => {
      ctx.swaggerUIConfig = config
      ctx.body = 'Swagger UI Mock'
      return next()
    }
  }),
}))

describe('swaggerUI模块测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createSwaggerUI', () => {
    it('应该使用默认配置创建SwaggerUI中间件', () => {
      const middleware = createSwaggerUI()
      expect(middleware).toBeDefined()
      expect(typeof middleware).toBe('function')
    })

    it('应该使用自定义配置创建SwaggerUI中间件', () => {
      const customConfig: SwaggerUIConfig = {
        routePrefix: '/custom-swagger',
        title: 'Custom API Docs',
        swaggerOptions: {
          docExpansion: 'full',
          persistAuthorization: false,
        },
      }

      const middleware = createSwaggerUI(customConfig, '/custom/swagger.json')
      expect(middleware).toBeDefined()
      expect(typeof middleware).toBe('function')
    })

    it('应该正确设置JSON端点URL', () => {
      const jsonEndpoint = '/api/v1/swagger.json'
      const middleware = createSwaggerUI({}, jsonEndpoint)

      expect(middleware).toBeDefined()
      expect(typeof middleware).toBe('function')
    })

    it('应该合并默认配置和自定义配置', async () => {
      const customConfig: SwaggerUIConfig = {
        title: 'Custom Title',
        swaggerOptions: {
          supportedSubmitMethods: ['get', 'post'],
        },
      }

      const middleware = createSwaggerUI(customConfig)

      // 创建模拟的 Koa 上下文
      const ctx = {}
      const next = vi.fn()

      await middleware(ctx, next)

      expect(next).toHaveBeenCalled()
    })
  })

  describe('createCustomSwaggerUI', () => {
    it('应该创建自定义SwaggerUI中间件', () => {
      const customOptions = {
        routePrefix: '/docs',
        swaggerOptions: {
          url: '/api/openapi.json',
        },
      }

      const middleware = createCustomSwaggerUI(customOptions)
      expect(middleware).toBeDefined()
      expect(typeof middleware).toBe('function')
    })
  })

  describe('配置验证', () => {
    it('应该处理空配置', () => {
      const middleware = createSwaggerUI(undefined, '/swagger.json')
      expect(middleware).toBeDefined()
    })

    it('应该处理部分配置', () => {
      const partialConfig: Partial<SwaggerUIConfig> = {
        title: 'Partial Config',
      }

      const middleware = createSwaggerUI(partialConfig)
      expect(middleware).toBeDefined()
    })

    it('应该处理嵌套的swaggerOptions配置', () => {
      const config: SwaggerUIConfig = {
        swaggerOptions: {
          docExpansion: 'list',
          persistAuthorization: true,
          supportedSubmitMethods: ['get', 'post', 'put'],
        },
      }

      const middleware = createSwaggerUI(config)
      expect(middleware).toBeDefined()
    })
  })

  describe('错误处理', () => {
    it('应该处理无效配置', () => {
      // 测试传入 null 或 undefined 的情况
      expect(() => createSwaggerUI(null)).not.toThrow()
      expect(() => createCustomSwaggerUI(null)).not.toThrow()
    })
  })
})
