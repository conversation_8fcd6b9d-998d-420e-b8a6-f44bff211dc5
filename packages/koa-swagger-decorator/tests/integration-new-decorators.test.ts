import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, generateSwaggerDoc, Header, Middlewares, Responses, RouteConfig } from '../src'
import { useRegistry } from '../src/registry'

const { getRegistry, resetRegistry } = useRegistry()

describe('新装饰器功能集成测试', () => {
  beforeEach(() => {
    resetRegistry()
  })

  describe('多状态码响应 @Responses 装饰器', () => {
    it('应该正确生成多状态码响应文档', () => {
      const LoginSuccessSchema = z.object({
        token: z.string(),
        user: z.object({
          id: z.number(),
          username: z.string(),
        }),
      })

      const ErrorResponseSchema = z.object({
        code: z.number(),
        message: z.string(),
        details: z.any().optional(),
      })

      @Controller({ tags: ['测试'] })
      class TestController {
        @RouteConfig({
          method: 'post',
          path: '/login',
          summary: '用户登录',
        })
        @Body(z.object({
          username: z.string(),
          password: z.string(),
        }))
        @Responses({
          200: {
            description: '登录成功',
            schema: LoginSuccessSchema,
          },
          400: {
            description: '请求参数错误',
            schema: ErrorResponseSchema,
          },
          401: {
            description: '用户名或密码错误',
            schema: ErrorResponseSchema,
          },
          429: {
            description: '请求过于频繁',
            schema: ErrorResponseSchema,
          },
          500: {
            description: '服务器内部错误',
            schema: ErrorResponseSchema,
          },
        })
        async login() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [TestController])

      const loginRoute = docs.paths['/login'].post
      expect(loginRoute).toBeDefined()

      // 验证所有状态码都存在
      expect(loginRoute.responses['200']).toBeDefined()
      expect(loginRoute.responses['400']).toBeDefined()
      expect(loginRoute.responses['401']).toBeDefined()
      expect(loginRoute.responses['429']).toBeDefined()
      expect(loginRoute.responses['500']).toBeDefined()

      // 验证响应描述
      expect(loginRoute.responses['200'].description).toBe('登录成功')
      expect(loginRoute.responses['400'].description).toBe('请求参数错误')
      expect(loginRoute.responses['401'].description).toBe('用户名或密码错误')
      expect(loginRoute.responses['429'].description).toBe('请求过于频繁')
      expect(loginRoute.responses['500'].description).toBe('服务器内部错误')

      // 验证 Schema 引用 - 200 状态码使用 Schema 引用
      expect(loginRoute.responses['200'].content['application/json'].schema.$ref).toMatch(/R_LoginVO_/)

      // 验证其他状态码使用字符串类型
      expect(loginRoute.responses['400'].content['application/json'].schema.type).toBe('string')
      expect(loginRoute.responses['401'].content['application/json'].schema.type).toBe('string')
      expect(loginRoute.responses['429'].content['application/json'].schema.type).toBe('string')
      expect(loginRoute.responses['500'].content['application/json'].schema.type).toBe('string')
    })
  })

  describe('@Header 装饰器', () => {
    it('应该正确生成请求头参数文档', () => {
      @Controller({ tags: ['测试'] })
      class TestController {
        @RouteConfig({
          method: 'post',
          path: '/test',
          summary: '测试接口',
        })
        @Header({
          'Content-Type': {
            required: true,
            schema: { type: 'string', enum: ['application/json'] },
            description: '请求内容类型',
          },
          'User-Agent': {
            required: false,
            schema: { type: 'string' },
            description: '用户代理信息',
          },
          'X-Request-ID': {
            required: false,
            schema: { type: 'string' },
            description: '请求追踪ID',
          },
        })
        async test() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [TestController])

      const testRoute = docs.paths['/test'].post
      expect(testRoute).toBeDefined()
      expect(testRoute.parameters).toBeDefined()

      // 验证请求头参数
      const parameters = testRoute.parameters
      const contentTypeParam = parameters.find((p) => p.name === 'Content-Type')
      const userAgentParam = parameters.find((p) => p.name === 'User-Agent')
      const requestIdParam = parameters.find((p) => p.name === 'X-Request-ID')

      expect(contentTypeParam).toBeDefined()
      expect(contentTypeParam.required).toBe(true)
      expect(contentTypeParam.in).toBe('header')
      expect(contentTypeParam.schema.enum).toEqual(['application/json'])

      expect(userAgentParam).toBeDefined()
      expect(userAgentParam.required).toBe(false)
      expect(userAgentParam.in).toBe('header')

      expect(requestIdParam).toBeDefined()
      expect(requestIdParam.required).toBe(false)
      expect(requestIdParam.in).toBe('header')
    })
  })

  describe('装饰器组合测试', () => {
    it('应该正确处理所有新装饰器的组合', () => {
      const middleware1 = async (ctx: any, next: any) => await next()
      const middleware2 = async (ctx: any, next: any) => await next()

      @Controller({ tags: ['组合测试'] })
      class CombinedController {
        @RouteConfig({
          method: 'post',
          path: '/combined',
          summary: '组合装饰器测试',
        })
        @Body(z.object({
          name: z.string(),
          email: z.string().email(),
        }))
        @Responses({
          201: {
            description: '创建成功',
            schema: z.object({
              id: z.number(),
              name: z.string(),
              email: z.string(),
            }),
          },
          400: {
            description: '请求参数错误',
          },
          409: {
            description: '资源已存在',
          },
        })
        @Header({
          'Content-Type': {
            required: true,
            schema: { type: 'string', enum: ['application/json'] },
            description: '请求内容类型',
          },
          'Authorization': {
            required: true,
            schema: { type: 'string', pattern: '^Bearer .+' },
            description: 'Bearer token',
          },
        })
        @Middlewares([middleware1, middleware2])
        async createResource() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [CombinedController])

      const route = docs.paths['/combined'].post
      expect(route).toBeDefined()

      // 验证请求体
      expect(route.requestBody).toBeDefined()
      expect(route.requestBody.content['application/json'].schema.$ref).toMatch(/CreateResourceRequest/)

      // 验证多状态码响应
      expect(route.responses['201']).toBeDefined()
      expect(route.responses['400']).toBeDefined()
      expect(route.responses['409']).toBeDefined()
      expect(route.responses['201'].description).toBe('创建成功')

      // 验证请求头
      const parameters = route.parameters
      const contentTypeParam = parameters.find((p) => p.name === 'Content-Type')
      const authParam = parameters.find((p) => p.name === 'Authorization')

      expect(contentTypeParam).toBeDefined()
      expect(authParam).toBeDefined()
      expect(authParam.schema.pattern).toBe('^Bearer .+')
    })
  })

  describe('边界情况测试', () => {
    it('应该处理只有描述没有 schema 的响应', () => {
      @Controller({ tags: ['边界测试'] })
      class EdgeCaseController {
        @RouteConfig({
          method: 'delete',
          path: '/resource/{id}',
          summary: '删除资源',
        })
        @Responses({
          204: {
            description: '删除成功',
          },
          404: {
            description: '资源不存在',
          },
          403: {
            description: '权限不足',
          },
        })
        async deleteResource() {}
      }

      const { object: docs } = generateSwaggerDoc(getRegistry(), [EdgeCaseController])

      const route = docs.paths['/resource/{id}'].delete
      expect(route).toBeDefined()

      // 验证只有描述的响应
      expect(route.responses['204']).toBeDefined()
      expect(route.responses['204'].description).toBe('删除成功')
      expect(route.responses['204'].content).toBeUndefined()

      expect(route.responses['404']).toBeDefined()
      expect(route.responses['404'].description).toBe('资源不存在')
      expect(route.responses['404'].content).toBeUndefined()
    })

    it('应该处理空的 @Header 装饰器', () => {
      @Controller({ tags: ['边界测试'] })
      class EmptyHeaderController {
        @RouteConfig({
          method: 'get',
          path: '/empty-header',
          summary: '空请求头测试',
        })
        @Header({})
        async emptyHeader() {}
      }

      expect(() => {
        generateSwaggerDoc(getRegistry(), [EmptyHeaderController])
      }).not.toThrow()
    })
  })
})
