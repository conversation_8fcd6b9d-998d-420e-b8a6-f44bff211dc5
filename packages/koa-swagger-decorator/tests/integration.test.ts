// 在此测试文件中禁用MSW，允许真实的HTTP请求
process.env.DISABLE_MSW = 'true'

import Koa from 'koa'
import bodyParser from 'koa-bodyparser'
import request from 'supertest'
import { describe, expect, it } from 'vitest'
import { z } from 'zod'
import { Body, Controller, Responses, RouteConfig } from '../src'
import { SwaggerRouter } from '../src/router'

describe('集成测试', () => {
  it('应该正确注册路由到Koa Router', () => {
    const schema = z.object({
      name: z.string(),
    })

    @Controller({
      tags: ['test'],
    })
    class TestController {
      @RouteConfig({
        method: 'post',
        path: '/test',
        summary: 'Test API',
      })
      @Body(schema)
      @Responses(schema)
      async test(ctx: Koa.Context) {
        ctx.body = ctx.request.body
      }
    }

    const router = new SwaggerRouter()
    router.applyRoute(TestController)
    router.swagger()
    router.createSwaggerUI()

    expect(router.stack).toMatchInlineSnapshot(`
      [
        Layer {
          "methods": [
            "POST",
          ],
          "name": null,
          "opts": {
            "end": true,
            "ignoreCaptures": undefined,
            "name": null,
            "prefix": "",
            "sensitive": false,
            "strict": false,
          },
          "paramNames": [],
          "path": "/test",
          "regexp": /\\^\\\\/test\\[\\\\/#\\\\\\?\\]\\?\\$/i,
          "stack": [
            [Function],
          ],
        },
        Layer {
          "methods": [
            "HEAD",
            "GET",
          ],
          "name": null,
          "opts": {
            "end": true,
            "ignoreCaptures": undefined,
            "name": null,
            "prefix": "",
            "sensitive": false,
            "strict": false,
          },
          "paramNames": [],
          "path": "/swagger.json",
          "regexp": /\\^\\\\/swagger\\\\\\.json\\[\\\\/#\\\\\\?\\]\\?\\$/i,
          "stack": [
            [Function],
          ],
        },
        Layer {
          "methods": [
            "HEAD",
            "GET",
          ],
          "name": null,
          "opts": {
            "end": true,
            "ignoreCaptures": undefined,
            "name": null,
            "prefix": "",
            "sensitive": false,
            "strict": false,
          },
          "paramNames": [],
          "path": "/swagger",
          "regexp": /\\^\\\\/swagger\\[\\\\/#\\\\\\?\\]\\?\\$/i,
          "stack": [
            [Function],
          ],
        },
      ]
    `)

    // 验证路由被正确注册
    expect(router.stack).toHaveLength(3) // /test, /swagger.json, /swagger.html

    const testRoute = router.stack.find((layer) => layer.path === '/test')
    expect(testRoute).toBeDefined()
    expect(testRoute?.methods).toContain('POST')

    // 验证Swagger文档包含正确的路径
    expect(router.docs?.object.paths['/test']).toBeDefined()
    expect(router.docs?.object.paths['/test']?.post).toBeDefined()
  })

  it('应该能完整处理一个API流程', async () => {
    const schema = z.object({
      name: z.string(),
    })

    @Controller({
      tags: ['test'],
    })
    class TestController {
      @RouteConfig({
        method: 'post',
        path: '/test',
        summary: 'Test API',
      })
      @Body(schema)
      @Responses(schema)
      async test(ctx: Koa.Context) {
        console.log('Controller method called with:', ctx.request.body)
        ctx.body = ctx.request.body
      }
    }

    const router = new SwaggerRouter()
    router.applyRoute(TestController)
    router.swagger()

    // 添加调试信息
    console.log('Generated routes:', Object.keys(router.docs?.object.paths || {}))
    console.log('Router layers:', router.stack.map((layer) => ({ path: layer.path, methods: layer.methods })))

    const app = new Koa()

    // 添加错误处理中间件
    app.use(async (ctx, next) => {
      try {
        await next()
      } catch (err) {
        console.error('Koa error:', err)
        ctx.status = 500
        ctx.body = { error: err.message }
      }
    })

    app.use(bodyParser())
    app.use(router.routes())
    app.use(router.allowedMethods())

    // 测试Swagger文档端点先
    const swaggerResponse = await request(app.callback())
      .get('/swagger.json')

    expect(swaggerResponse.status).toBe(200)
    expect(swaggerResponse.body.paths['/test']).toBeDefined()

    // 现在可以测试真实的API调用，因为MSW已被禁用
    console.log('🚀 Testing real HTTP API call with MSW disabled')
    const response = await request(app.callback())
      .post('/test')
      .send({ name: 'test' })

    expect(response.status).toBe(200)
    expect(response.body).toEqual({ name: 'test' })
  })
})
