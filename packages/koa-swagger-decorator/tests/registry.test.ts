import { extendZod<PERSON>ith<PERSON>penApi, OpenApiGeneratorV3, OpenAPIRegistry } from '@asteasolutions/zod-to-openapi'
import { beforeEach, describe, expect, it } from 'vitest'
import { z } from 'zod'

// 扩展 zod 以支持 openapi
extendZodWithOpenApi(z)

describe('registry测试', () => {
  let testRegistry: OpenAPIRegistry

  beforeEach(() => {
    testRegistry = new OpenAPIRegistry()
  })

  it('应该能够注册Schema', () => {
    const schema = z.object({
      name: z.string(),
      age: z.number(),
    }).openapi({ ref: 'TestSchema' })

    testRegistry.register('TestSchema', schema)

    // 生成完整的 OpenAPI 文档
    const generator = new OpenApiGeneratorV3(testRegistry.definitions)
    const doc = generator.generateDocument({
      openapi: '3.0.0',
      info: { title: 'Test API', version: '1.0.0' },
    })

    expect(doc.components?.schemas?.TestSchema).toBeDefined()
    expect(doc.components?.schemas?.TestSchema.properties?.name).toBeDefined()
    expect(doc.components?.schemas?.TestSchema.properties?.age).toBeDefined()
  })

  it('应该能够注册路径', () => {
    const responseSchema = z.object({
      message: z.string(),
    }).openapi({ ref: 'TestResponse' })

    const pathConfig = {
      method: 'get',
      path: '/test',
      responses: {
        200: {
          description: 'Success',
          content: {
            'application/json': {
              schema: responseSchema,
            },
          },
        },
      },
    }

    testRegistry.registerPath(pathConfig)

    // 生成完整的 OpenAPI 文档
    const generator = new OpenApiGeneratorV3(testRegistry.definitions)
    const doc = generator.generateDocument({
      openapi: '3.0.0',
      info: { title: 'Test API', version: '1.0.0' },
    })

    expect(doc.paths?.['/test']).toBeDefined()
    expect(doc.paths?.['/test'].get).toBeDefined()
    expect(doc.paths?.['/test'].get?.responses?.[200]?.description).toBe('Success')
  })

  it('应该能够注册组件', () => {
    const component = {
      type: 'object',
      properties: {
        name: { type: 'string' },
      },
      required: ['name'],
    }

    testRegistry.registerComponent('schemas', 'TestComponent', component)

    // 生成完整的 OpenAPI 文档
    const generator = new OpenApiGeneratorV3(testRegistry.definitions)
    const doc = generator.generateDocument({
      openapi: '3.0.0',
      info: { title: 'Test API', version: '1.0.0' },
    })

    expect(doc.components?.schemas?.TestComponent).toBeDefined()
    expect(doc.components?.schemas?.TestComponent).toMatchObject({
      type: 'object',
      properties: {
        name: { type: 'string' },
      },
      required: ['name'],
    })
  })
})
