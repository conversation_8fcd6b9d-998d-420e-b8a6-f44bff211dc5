/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-03-22 08:57:39
 * @LastEditTime: 2024-03-22 09:04:19
 * @LastEditors: shaojun
 * @Description:
 */
import { existsSync, writeFileSync } from 'node:fs'
import { mkdir } from 'node:fs/promises'

export const writeJson = (path: string, data: any, spaces = 0) => {
  writeFileSync(path, JSON.stringify(data, undefined, spaces), 'utf-8')
}

export const ensureDir = async (path: string) => {
  if (!existsSync(path)) {
    await mkdir(path, { recursive: true })
  }
}
