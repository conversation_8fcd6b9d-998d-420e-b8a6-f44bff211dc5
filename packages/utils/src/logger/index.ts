// Winston是Node.js最流行的日志库之一,具有以下优点:
// 1. 支持多种传输方式(文件、控制台、HTTP等)
// 2. 支持日志分级(error、warn、info等)
// 3. 支持日志格式化(JSON、简单文本等)
// 4. 支持元数据添加
// 5. 高度可配置和可扩展
// 6. 社区活跃,生态丰富
// 7. 性能优秀

// 其他选择:
// - Bunyan: 结构化日志,但功能相对简单
// - Pino: 性能极佳,但配置灵活性较低
// - debug: 轻量级,但功能有限

import path from 'node:path'
import process from 'node:process'
import winston from 'winston'

export const createLogger = (options: {
  logDir: string
  serviceName: string
}) => {
  const { logDir, serviceName } = options

  return winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json(),
    ),
    defaultMeta: { service: serviceName },
    transports: [
      new winston.transports.File({
        filename: path.join(logDir, 'error.log'),
        level: 'error',
      }),
      new winston.transports.File({
        filename: path.join(logDir, 'combined.log'),
      }),
    ],
  })
}
