/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-18 15:31:36
 * @LastEditTime: 2025-07-11 22:46:15
 * @LastEditors: shaojun
 * @Description:
 */
import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Paths
export const projRoot = resolve(__dirname, '..', '..', '..')
export const pkgRoot = resolve(projRoot, 'packages')
export const docsRoot = resolve(projRoot, 'docs')
export const serverRoot = resolve(pkgRoot, 'server')
export const utilsRoot = resolve(pkgRoot, 'utils')
export const koaSwaggerDecoratorRoot = resolve(pkgRoot, 'koa-swagger-decorator')

// 包路径
declare type PackagePath
  = | 'root'
    | 'docs'
    | 'server'
    | 'utils'
    | 'koa-swagger-decorator'

// 文件夹路径
declare type FolderPath = PackagePath | 'pkgRoot'

// 项目路径映射
export const FolderPathsMap: Record<FolderPath, string> = {
  'root': projRoot,
  pkgRoot,
  'docs': docsRoot,
  'server': serverRoot,
  'utils': utilsRoot,
  'koa-swagger-decorator': koaSwaggerDecoratorRoot,
}

// server static
export const ServerStaticRoot = resolve(serverRoot, 'static')
// server public
export const ServerPublicRoot = resolve(serverRoot, 'public')

// 相对项目路径
export const relativeProjectPath = (path: string) => {
  return resolve(projRoot, path)
}

// 相对项目packages路径
export const relativePkgPath = (path: string) => {
  return resolve(pkgRoot, path)
}

// 相对项目docs路径
export const relativeDocsPath = (path: string) => {
  return resolve(docsRoot, path)
}

// 相对项目静态资源路径
export const relativeStaticPath = (path: string) => {
  return resolve(ServerStaticRoot, path)
}

// 相对项目公共资源路径
export const relativePublicPath = (path: string) => {
  return resolve(ServerPublicRoot, path)
}

// Package.json
const projPackage = resolve(projRoot, 'package.json')
const docsPackage = resolve(docsRoot, 'package.json')
const serverPackage = resolve(serverRoot, 'package.json')
const utilsPackage = resolve(utilsRoot, 'package.json')
const koaSwaggerDecoratorPackage = resolve(
  koaSwaggerDecoratorRoot,
  'package.json',
)

export const PackageJsonPathMap: Record<PackagePath, string> = {
  'root': projPackage,
  'docs': docsPackage,
  'server': serverPackage,
  'utils': utilsPackage,
  'koa-swagger-decorator': koaSwaggerDecoratorPackage,
}

// All package.json
export const getAllPkgJsonPath = () => {
  return Object.values(PackageJsonPathMap)
}
