import type { ProjectManifest } from '@pnpm/types'
import { readFile } from 'node:fs/promises'
import { FolderPathsMap, getAllPkgJsonPath } from './paths'

// export const getWorkspacePackages = () => findWorkspacePackages(projRoot)

export const findWorkspacePackages = async (): Promise<{ dir: string, manifest: ProjectManifest }[]> => {
  // allPkgPaths
  const allPkgPaths = getAllPkgJsonPath()
  const allPkgs = (await Promise.all(allPkgPaths.map(async (pkg) => {
    const manifest = await getPackageManifest(pkg)
    if (!manifest) {
      return null
    }
    return { dir: pkg, manifest }
  })))
  return allPkgs.filter(Boolean) as { dir: string, manifest: ProjectManifest }[]
}

export const getPackageManifest = async (pkgPath: string): Promise<ProjectManifest | undefined> => {
  try {
    // 读取内容转json
    // return await readFile(pkgPath) as ProjectManifest
    return JSON.parse(await readFile(pkgPath, 'utf-8')) as ProjectManifest
  } catch (error) {
    console.error(`Failed to read package.json at ${pkgPath}`, error)
    return undefined
  }
}

export const getWorkspaceNames = async (dir = FolderPathsMap.root) => {
  const pkgs = await findWorkspacePackages()
  return pkgs
    .filter((pkg) => pkg.dir.startsWith(dir))
    .map((pkg) => pkg.manifest.name)
    .filter((name): name is string => !!name)
}

export const getPackageDependencies = async (
  pkgPath: string,
): Promise<Record<'dependencies' | 'devDependencies' | 'peerDependencies', string[]>> => {
  const manifest = await getPackageManifest(pkgPath)
  const { dependencies = {}, devDependencies = {}, peerDependencies = {} } = manifest ?? {}

  return {
    dependencies: Object.keys(dependencies),
    devDependencies: Object.keys(devDependencies),
    peerDependencies: Object.keys(peerDependencies),
  }
}

export const excludeFiles = (files: string[]) => {
  const excludes = ['node_modules', 'test', 'mock', 'gulpfile', 'dist']
  return files.filter(
    (path) => !excludes.some((exclude) => path.includes(exclude)),
  )
}
