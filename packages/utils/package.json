{"name": "@cfe-node/utils", "type": "module", "version": "1.0.0", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./*": "./*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["LICENSE", "README.md", "dist"], "scripts": {"dev": "pnpm run stub", "stub": "unbuild --stub", "build": "unbuild", "build:lib": "unbuild", "build:unbuild": "unbuild"}, "dependencies": {"winston": "^3.10.0"}}