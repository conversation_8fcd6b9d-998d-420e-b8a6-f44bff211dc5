{"compilerOptions": {"target": "ES2020", "jsx": "preserve", "lib": ["ES2020", "DOM", "DOM.Iterable"], "useDefineForClassFields": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "baseUrl": ".", "rootDir": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@cfe-node/utils/*": ["packages/utils/src/*"], "@cfe-node/utils": ["packages/utils/src/index.ts"], "@cfe-node/koa-swagger-decorator/*": ["packages/koa-swagger-decorator/src/*"], "@cfe-node/koa-swagger-decorator": ["packages/koa-swagger-decorator/src/index.ts"], "@cfe-node/server/*": ["packages/server/src/*"], "@cfe-node/server": ["packages/server/src/index.ts"]}, "resolveJsonModule": true, "typeRoots": ["./node_modules/@types/", "./types"], "types": ["node", "vite/client", "vitest/globals"], "allowJs": false, "strict": true, "strictNullChecks": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "declaration": true, "declarationDir": "dist", "downlevelIteration": true, "emitDeclarationOnly": true, "outDir": "dist", "removeComments": false, "sourceMap": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "skipLibCheck": true}, "references": [{"path": "./tsconfig.node.json"}], "include": ["scripts/**/*.ts", "examples/**/*.ts", "playgrounds/**/*.ts", "packages/**/*.ts", "vitest.workspace.ts"], "exclude": ["*/**/*.md", "**/dist", "**/node_modules/", "**/.pnpm/"]}