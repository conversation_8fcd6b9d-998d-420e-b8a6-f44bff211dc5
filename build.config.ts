/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2024-12-18 13:55:03
 * @LastEditTime: 2024-12-18 14:01:06
 * @LastEditors: shaojun
 * @Description:
 */
import type { BuildConfig } from 'unbuild'
import { defineBuildConfig } from 'unbuild'

export const commonConfig: BuildConfig | BuildConfig[] = {
  entries: [
    { builder: 'mkdist', input: './src', pattern: ['**/*.vue'], loaders: ['vue'] },
    { builder: 'mkdist', input: './src', pattern: ['**/*.ts'], format: 'cjs', ext: 'js', loaders: ['js'] },
    { builder: 'mkdist', input: './src', pattern: ['**/*.ts'], format: 'esm', ext: 'mjs', loaders: ['js'], esbuild: { minify: false } },
    { builder: 'mkdist', input: './src', pattern: ['**/*.scss'], loaders: ['sass'] },
  ],
  clean: true,
  declaration: true,
  failOnWarn: false,
  externals: ['lodash-es', 'vue', 'axios', '@vueuse/core'],
  rollup: {
    emitCJS: true,
  },
  hooks: {
    'rollup:options': function (_ctx, options) {
      console.log(options.plugins)
      if (Array.isArray(options.plugins)) {
        // options.plugins.push(
        //   Babel({
        //     babelHelpers: 'bundled',
        //     exclude: 'node_modules/**'
        //   })
        // )
      }
    },
  },
}

export default defineBuildConfig(commonConfig)
