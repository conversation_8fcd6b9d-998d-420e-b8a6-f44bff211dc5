/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-03-28 09:17:50
 * @LastEditTime: 2025-07-11 15:52:55
 * @LastEditors: shaojun
 * @Description:
 */

import type { BuildConfig } from 'unbuild'
import { resolve } from 'node:path'
import { defineBuildConfig } from 'unbuild'
/**
 * 创建带有别名的标准配置
 * @param packagePath 包的根路径
 * @returns 配置对象
 */
export function createAliasConfig(packagePath: string): Record<string, string> {
  const srcDir = resolve(packagePath, 'src')
  return {
    '@': srcDir,
  }
}

// 共享的基础配置
export const commonConfig: BuildConfig = {
  entries: [
    {
      builder: 'mkdist',
      input: './src',
      pattern: ['**/*.vue'],
      loaders: ['vue'],
    },
    {
      builder: 'mkdist',
      input: './src',
      pattern: ['**/*.ts'],
      format: 'cjs',
      ext: 'cjs',
      loaders: ['js'],
    },
    {
      builder: 'mkdist',
      input: './src',
      pattern: ['**/*.ts'],
      format: 'esm',
      ext: 'mjs',
      loaders: ['js'],
    },
    {
      builder: 'mkdist',
      input: './src',
      pattern: ['**/*.scss'],
      loaders: ['sass'],
    },
  ],
  clean: true,
  declaration: true,
  failOnWarn: false,
  externals: ['vue', '@vueuse/core', 'pinia', 'vue-router', 'element-plus', 'lodash-es'],
  // sourcemap: true,
  rollup: {
    emitCJS: true,
    inlineDependencies: true,
    esbuild: {
      minify: true,
      sourcemap: true,
    },
  },
  // 在 stub 模式下也生成类型定义文件
  stub: true,
}

// 创建库包的unbuild配置
export function createLibConfig(customConfig: Partial<BuildConfig> = {}): BuildConfig {
  // 深度合并配置,优先使用自定义配置
  return {
    ...commonConfig,
    ...customConfig,
    // 单独合并alias配置
    alias: {
      ...(customConfig.alias || {}),
    },
  }
}

// 默认导出基础配置
export default defineBuildConfig(commonConfig)
