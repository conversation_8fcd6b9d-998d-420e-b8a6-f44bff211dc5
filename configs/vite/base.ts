import { resolve } from 'node:path'
import process from 'node:process'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
import { VueMcp } from 'vite-plugin-vue-mcp'
import { createAllAliases } from '../alias'

// 共享的路径解析函数
export function resolvePath(dir: string, root?: string) {
  return resolve(root || process.cwd(), dir)
}

export interface BaseConfigOptions {
  projectRoot?: string
}

// 创建基础配置
export function createBaseConfig(options?: BaseConfigOptions) {
  const { projectRoot } = options || {}
  return defineConfig({
    plugins: [
      vue(),
      VueMcp(),
      UnoCSS(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true,
        },
        dts: 'types/auto-imports.d.ts',
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dirs: ['src/components'],
        extensions: ['vue', 'tsx'],
        deep: true,
        dts: 'types/components.d.ts',
      }),
    ],
    resolve: {
      alias: createAllAliases(projectRoot),
    },
    // 其他共享配置...
    ...options,
  })
}
