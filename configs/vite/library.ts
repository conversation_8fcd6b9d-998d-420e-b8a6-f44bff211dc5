import type { ConfigEnv } from 'vite'
import type { BaseConfigOptions } from './base'
/*
 * @Author: shao<PERSON>
 * @Date: 2025-03-28 09:17:05
 * @LastEditTime: 2025-04-18 16:35:01
 * @LastEditors: shaojun
 * @Description:
 */
import { resolve } from 'node:path'
import process from 'node:process'
import { mergeConfig } from 'vite'
import dts from 'vite-plugin-dts'
import { createBaseConfig } from './base'

export function createLibConfig(configEnv: ConfigEnv, options?: BaseConfigOptions) {
  const { mode } = configEnv
  const isProduction = mode === 'production'

  const baseConfig = createBaseConfig(options)
  const libConfig = {
    build: {
      outDir: 'dist',
      lib: {
        entry: resolve(process.cwd(), 'src/index.ts'),
        formats: ['es', 'cjs'],
        fileName: (format) => `index.${format === 'es' ? 'mjs' : 'cjs'}`,
      },
      minify: isProduction,
      sourcemap: !isProduction,
      emptyOutDir: true,
      rollupOptions: {
        external: ['vue', '@vueuse/core', 'pinia', 'vue-router', 'element-plus', 'lodash-es'],
        output: {
          globals: {
            'vue': 'Vue',
            'element-plus': 'ElementPlus',
            'lodash-es': '_',
            '@vueuse/core': 'VueUse',
            'pinia': 'Pinia',
            'vue-router': 'VueRouter',
          },
          exports: 'named',
        },
      },
    },
    plugins: [
      dts({
        include: ['src/**/*.ts', 'src/**/*.vue', 'src/**/*.tsx'],
        exclude: ['src/**/*.test.ts', 'src/**/*.spec.ts', 'src/**/__tests__/**'],
        outDir: 'dist',
        rollupTypes: true, // 添加此选项合并所有类型
        entryRoot: 'src', // 添加此选项移除src前缀
        staticImport: true,
      }),
    ],
  }

  return mergeConfig(mergeConfig(baseConfig, libConfig), options)
}
