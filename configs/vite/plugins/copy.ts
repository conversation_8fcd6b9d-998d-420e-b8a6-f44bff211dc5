import type { Plugin } from 'vite'
import fs from 'node:fs'
import { resolve } from 'node:path'
import process from 'node:process'
import { promisify } from 'node:util'
import fsExtra from 'fs-extra'

/*
 * @Author: shaojun
 * @Date: 2025-01-07 10:35:20
 * @LastEditTime: 2025-07-09 09:49:10
 * @LastEditors: shaojun
 * @Description:
 */

const copyFile = promisify(fs.copyFile)
const mkdir = promisify(fs.mkdir)
const readdir = promisify(fs.readdir)
const stat = promisify(fs.stat)
const readFile = promisify(fs.readFile)
const rm = promisify(fs.rm)

// 自定义插件：复制静态资源
export function copyExtensionFiles(): Plugin {
  return {
    name: 'vite:copy-extension-files',
    enforce: 'post',
    apply: 'build',
    async closeBundle() {
      const srcDir = resolve(process.cwd(), 'public')
      const targetDir = resolve(process.cwd(), 'dist')

      // 确保目标目录存在
      try {
        await mkdir(targetDir, { recursive: true })
      } catch (err) {
        if (err.code !== 'EEXIST') {
          throw err
        }
      }

      // 复制manifest.json
      const manifestPath = resolve(process.cwd(), 'manifest.json')
      if (fs.existsSync(manifestPath)) {
        await copyFile(manifestPath, resolve(targetDir, 'manifest.json'))
      }

      // 转换并复制图标
      const iconDir = resolve(process.cwd(), 'public/icons')
      if (fs.existsSync(iconDir)) {
        try {
          await mkdir(resolve(targetDir, 'icons'), { recursive: true })

          // 处理所有尺寸的图标
          const sizes = [16, 48, 128]
          for (const size of sizes) {
            const fileName = `${size}x${size}`
            const pngPath = resolve(iconDir, 'icons', `${fileName}.png`)
            const targetPngPath = resolve(targetDir, 'icons', `${fileName}.png`)
            if (fs.existsSync(pngPath)) {
              await copyFile(pngPath, targetPngPath)
              console.log(`${fileName}.png 复制成功`)
            }
          }

          // 删除可能存在的 SVG 文件
          const files = await readdir(resolve(targetDir, 'icons'))
          for (const file of files) {
            if (file.endsWith('.svg')) {
              await rm(resolve(targetDir, 'icons', file))
            }
          }
        } catch (err) {
          console.error('处理图标失败:', err)
        }
      }

      // 复制其他静态资源（排除 icons 目录和 SVG 文件）
      if (fs.existsSync(srcDir)) {
        try {
          const files = await readdir(srcDir)
          for (const file of files) {
            if (file !== 'icons') { // 跳过图标目录，因为已单独处理
              const srcPath = resolve(srcDir, file)
              const destPath = resolve(targetDir, file)
              const fileStat = await stat(srcPath)
              if (fileStat.isDirectory()) {
                // 复制整个目录
                await fsExtra.copy(srcPath, destPath)
              } else if (fileStat.isFile() && !srcPath.endsWith('.svg')) {
                // 复制文件（排除SVG）
                await copyFile(srcPath, destPath)
              }
            }
          }
        } catch (err) {
          if (err.code !== 'ENOENT') {
            throw err
          }
        }
      }
    },
  }
}
