import type { PluginOption } from 'vite'
/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2022-03-15 09:36:13
 * @LastEditTime: 2025-07-09 09:53:31
 * @LastEditors: shaojun
 * @Description:
 */
//
import legacy from '@vitejs/plugin-legacy'
import Vue from '@vitejs/plugin-vue'
import { codeInspectorPlugin } from 'code-inspector-plugin'
import UnoCSS from 'unocss/vite'
import Icons from 'unplugin-icons/vite'
import viteCompression from 'vite-plugin-compression'
import ViteRestart from 'vite-plugin-restart'
import { isBuildHybrid, isDevelopment, isProduction, useEnv } from '../../utils/env'
import { GenerateTitle } from './html'
import { configVisualizerConfig } from './visualizer'

export default (mode?: string): PluginOption[] => {
  const env = useEnv(mode)
  const plugins: PluginOption[] = [
    GenerateTitle() as PluginOption,
    Vue({
      include: [/\.vue$/],
      template: {
        compilerOptions: {
          // 标记为自定义元素
          isCustomElement: (tag) => ['iconify-icon'].includes(tag),
        },
      },
    }),
    UnoCSS(),
    Icons({
      autoInstall: true,
    }) as PluginOption,
    // 预设热重启服务
    ViteRestart({
      restart: ['.env*', 'configs/vite/*.ts', 'configs/vite/**/*'],
    }),
    // 查看打包report REPORT = true
    configVisualizerConfig(),
  ]

  if (isDevelopment(env)) {
    plugins.push(
      codeInspectorPlugin({
        bundler: 'vite',
      }),
    )
  }

  if (isProduction(env)) {
    plugins.push(viteCompression())
  }

  if (isBuildHybrid(env)) {
    plugins.push(legacy() as PluginOption)
  }

  return plugins
}
