/**
 * Used to parse the .env.development proxy configuration
 */
import type { ProxyOptions } from 'vite'
import { EnvEnum } from '../../type'
import { generateBaseUrlMap } from '../../utils'

type ProxyItem = [string, string]

type ProxyList = ProxyItem[]

type ProxyTargetList = Record<string, ProxyOptions>

const httpsRE = /^https:\/\//

/**
 * Generate proxy
 * @param list
 */
export function createProxy(list: ProxyList = []) {
  const ret: ProxyTargetList = {}
  for (const [prefix, target] of list) {
    const isHttps = httpsRE.test(target)

    // https://github.com/http-party/node-http-proxy#options
    ret[prefix] = {
      target,
      changeOrigin: true,
      ws: true,
      rewrite: (path) => path.replace(new RegExp(`^${prefix}`), ''),
      // https is require secure=false
      ...(isHttps ? { secure: false } : {}),
    }
  }
  return ret
}
/**
 * 获取开发服务器代理
 * @param {object} options - 配置选项
 * @param {boolean} [options.https] - 是否启用https
 * @param {string} [options.env] - 环境变量
 * @returns {ProxyTargetList} 代理配置
 */
export function getDevServerProxy(options?: { https?: boolean, env?: EnvEnum }): ProxyTargetList {
  // 默认配置
  const { https = false, env = EnvEnum.D2 } = options || {}
  const PROJECT_PREFIX = 'cas'
  const proxyMap = generateBaseUrlMap(PROJECT_PREFIX, {
    [EnvEnum.LOCAL]: 'http://10.1.41.198:30000',
    [EnvEnum.D1]: 'http://10.1.41.198:30000',
    [EnvEnum.D2]: 'http://10.1.41.100:30000',
    [EnvEnum.D3]: 'http://10.1.41.100:30300',
  })
  const svr = {
    target: proxyMap.get(env),
    changeOrigin: https,
  }
  console.log('server proxyMap ==>', proxyMap)
  console.log('server proxy ==>', `https = ${https};`, `env = ${env};`)
  console.log('server proxy ==>', JSON.stringify(svr))
  return {
    '/svr': svr,
    // cjia server
    '/s3UploadFile': {
      target: 'https://fat-oss1.oos-hazz.ctyunapi.cn', // fat-天翼
      changeOrigin: true, // 开启代理跨域
      rewrite: (path) => path.replace(/^\/s3UploadFile/, ''),
    },
    // cjia server
    '/ossUploadFile': {
      target: 'https://image.cjiatest.com', // pre
      changeOrigin: true, // 开启代理跨域
      rewrite: (path) => path.replace(/^\/ossUploadFile/, ''),
    },
    // cjia server
    // cjia server 公有云
    '/communalSssUploadFile': {
      target: 'https://image.cjiatest.com', // pre
      changeOrigin: true, // 开启代理跨域
      rewrite: (path) => path.replace(/^\/communalSssUploadFile/, ''),
    },
    // cjia server 私有云
    '/privateOssUploadFile': {
      target: 'https://fat-oss-private.oss-cn-hangzhou.aliyuncs.com', // pre
      changeOrigin: true, // 开启代理跨域
      rewrite: (path) => path.replace(/^\/privateOssUploadFile/, ''),
    },
  }
}
