import type { UserConfig } from 'vite'
import fs from 'node:fs'
import { resolve } from 'node:path'
import { mergeConfig } from 'vite'
import { createBaseConfig } from './base'
import { copyExtensionFiles } from './plugins/copy'

export function createExtensionConfig(customConfig: UserConfig = {}, projectRoot?: string) {
  const baseConfig = createBaseConfig({ projectRoot })
  const extensionConfig: UserConfig = {
    plugins: [
      copyExtensionFiles(),
    ],
  }
  const finalConfig = mergeConfig(mergeConfig(baseConfig, extensionConfig), customConfig)
  // console.dir(finalConfig, { depth: null })
  return finalConfig
}

// 帮助生成TAB页面入口的工具函数
export function generateTabEntries(baseDir: string) {
  const entries: Record<string, string> = {}

  const tabsDir = resolve(baseDir, 'src/tabs')

  // 检查目录是否存在
  if (!fs.existsSync(tabsDir)) {
    return entries
  }

  // 处理直接位于tabs目录下的index.html
  const rootIndexHtml = resolve(tabsDir, 'index.html')
  if (fs.existsSync(rootIndexHtml)) {
    entries['tabs/index'] = resolve(baseDir, 'src/tabs/index.html')
  }

  // 读取tabs目录下的所有子目录
  const tabFolders = fs.readdirSync(tabsDir).filter((name: string) => {
    const fullPath = resolve(tabsDir, name)
    return fs.statSync(fullPath).isDirectory()
      && fs.existsSync(resolve(fullPath, 'index.html'))
  })

  // 为每个标签页创建入口配置
  tabFolders.forEach((tabName: string) => {
    entries[`tabs/${tabName}`] = resolve(baseDir, `src/tabs/${tabName}/index.html`)
  })

  console.log('检测到以下Tab页面:', Object.keys(entries))
  return entries
}
