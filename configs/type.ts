/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-03-10 09:50:14
 * @LastEditTime: 2025-07-09 09:52:15
 * @LastEditors: shaojun
 * @Description:
 */
export type EnvEnumType
  = | 'local'
    | 'd1'
    | 'd2'
    | 'd3'
    | 't1'
    | 't2'
    | 't3'
    | 'pre'
    | 'gray'
    | 'prd'

export enum EnvEnum {
  LOCAL = 'local',
  D1 = 'd1',
  D2 = 'd2',
  D3 = 'd3',
  T1 = 't1',
  T2 = 't2',
  T3 = 't3',
  PRE = 'pre',
  GRAY = 'gray',
  PRD = 'prd',
}
