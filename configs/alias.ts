import type { <PERSON><PERSON> } from 'vite'
import path, { resolve } from 'node:path'
import process from 'node:process'
// 获取项目根目录路径（默认为当前工作目录）
function getProjectRoot(rootPath?: string): string {
  return rootPath || process.cwd()
}

// 获取包的绝对路径
function getPackagePath(packageName: string, rootPath?: string) {
  const projectRoot = getProjectRoot(rootPath)
  return resolve(projectRoot, 'packages', packageName)
}

// 根据包名创建别名配置
export function createAliasForPackage(packageName: string, rootPath?: string): Alias[] {
  const packageAlias: Alias[] = [
    {
      find: `@packages/${packageName}`,
      replacement: resolve(getPackagePath(packageName, rootPath), 'src'),
    },
    {
      find: `@${packageName}`,
      replacement: resolve(getPackagePath(packageName, rootPath), 'src'),
    },
  ]

  // allPackages 的正则表达式 ['constants', 'core', 'global', 'hooks', 'utils', 'trace', 'cli', 'components', 'shared']
  const allPackagesRegExps = /^@cfe-node\/(server|utils|koa-swagger-decorator)$/
  if (process.env.DOC_ENV === 'production') {
    packageAlias.push({
      find: allPackagesRegExps,
      replacement: path.resolve(`${rootPath}/packages/$1`),
    })
  } else {
    packageAlias.push(
      ...[
        {
          find: allPackagesRegExps,
          replacement: path.resolve(`${rootPath}/packages/$1`, 'src/index.ts'),
        },
        {
          find: /^@cfe-node\/(.+css)$/,
          replacement: path.resolve(`${rootPath}/packages/$1`, 'src/styles/index.ts'),
        },
      ],
    )
  }

  return packageAlias
}

// 为每个包创建内部 @/ 别名
export function createInternalPackageAliases(rootPath?: string): Alias[] {
  const projectRoot = getProjectRoot(rootPath)
  return allPackages.map((packageName) => ({
    find: '@',
    replacement: resolve(projectRoot, 'packages', packageName, 'src'),
    customResolver: (id, importer) => {
      // 只有当导入者在该包内时才使用这个别名
      if (importer && importer.includes(`/packages/${packageName}/`)) {
        return resolve(projectRoot, 'packages', packageName, 'src', id.slice(2))
      }
      return null
    },
  }))
}

export function createDevAlias(rootPath?: string): Alias[] {
  return [
    // dev
  ]
}

export function createPlaygroundAlias(rootPath?: string): Alias[] {
  return [
    // playground
  ]
}

export const allPackages = ['constants', 'core', 'global', 'hooks', 'utils', 'trace', 'cli', 'components', 'shared']

// 创建所有包的别名
export function createAllAliases(rootPath?: string): Alias[] {
  // console.log('createAllAliases rootPath', rootPath)
  const devAliases = createDevAlias(rootPath)
  const playgroundAliases = createPlaygroundAlias(rootPath)
  const packageAliases = allPackages.flatMap((pkg) => createAliasForPackage(pkg, rootPath))
  const internalAliases = createInternalPackageAliases(rootPath)

  return [...devAliases, ...playgroundAliases, ...packageAliases, ...internalAliases]
}
