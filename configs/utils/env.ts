import process from 'node:process'
import { loadEnv } from 'vite'

export declare interface ViteEnv {
  readonly VITE_NODE_ENV: string
  readonly VITE_GLOB_BUILD_TYPE: string
  readonly VITE_PORT: number
  readonly VITE_PUBLIC_PATH: string
  readonly VITE_PROJECT: string
  readonly VITE_USE_PWA: boolean
  readonly VITE_USE_CDN: boolean
  readonly VITE_DROP_CONSOLE: boolean
  readonly VITE_BUILD_COMPRESS: string
  readonly VITE_GLOB_APP_TITLE: string
  readonly VITE_GLOB_APP_SHORT_NAME: string
  readonly VITE_GLOB_API_URL: string
  readonly VITE_GLOB_UPLOAD_URL: string
  readonly VITE_GLOB_API_URL_PREFIX: string
  [key: string]: any
}
// 是否是开发环境
export function isDevelopment(newEnv: ViteEnv) {
  return newEnv.VITE_NODE_ENV === 'development'
}

// 是否是生产环境
export function isProduction(newEnv: ViteEnv) {
  return newEnv.VITE_NODE_ENV === 'production'
}
// 是否是hybrid mode
export function isBuildHybrid(newEnv: ViteEnv) {
  return newEnv.VITE_GLOB_BUILD_TYPE === 'LOCAL_HYBRID'
}

/**
 * Whether to generate package preview
 */
export function isReportMode(): boolean {
  return process.env.REPORT === 'true'
}
export function isCheck(): boolean {
  return stringToBoolean(process.env.CHECK || '')
}

export function isHttps(): boolean {
  return stringToBoolean(process.env.HTTPS || '')
}

function stringToBoolean(v: string) {
  return Boolean(v === 'true' || false)
}

// 获取环境变量
export function useEnv(mode = 'dev', root = '.') {
  console.log('useEnv------->', `mode = ${mode};`, `root = ${root};`)
  const env = loadEnv(mode, root)
  const {
    VITE_NODE_ENV,
    VITE_GLOB_BUILD_TYPE,
    VITE_PORT,
    VITE_PUBLIC_PATH,
    VITE_PROJECT,
    VITE_USE_PWA,
    VITE_USE_CDN,
    VITE_DROP_CONSOLE,
    VITE_BUILD_COMPRESS,
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_API_URL,
    VITE_GLOB_UPLOAD_URL,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_ERUDA,
  } = env

  const newEnv: ViteEnv = {
    VITE_NODE_ENV: String(VITE_NODE_ENV),
    VITE_PORT: Number(VITE_PORT),
    VITE_PUBLIC_PATH,
    VITE_PROJECT,
    VITE_USE_PWA: stringToBoolean(VITE_USE_PWA),
    VITE_USE_CDN: stringToBoolean(VITE_USE_CDN),
    VITE_DROP_CONSOLE: stringToBoolean(VITE_DROP_CONSOLE),
    VITE_BUILD_COMPRESS,
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_BUILD_TYPE,
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_API_URL,
    VITE_GLOB_UPLOAD_URL,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_ERUDA: stringToBoolean(VITE_GLOB_ERUDA),
  }

  for (const key in newEnv) {
    if (Object.prototype.hasOwnProperty.call(newEnv, key)) {
      process.env[key] = newEnv[key]
    }
  }

  return newEnv
}

// object to string key value
export function logEnvObject(obj: Record<string, string | number | boolean | undefined>) {
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      console.log(new EnvKeyValue(key, obj[key]).toString())
    }
  }
}
export class EnvKeyValue {
  key: string
  value: string | number | boolean | undefined
  constructor(key: string, value: string | number | boolean | undefined) {
    this.key = key
    this.value = value
  }

  toString() {
    return `[ ${this.key} : ${this.value} ]`
  }
}
