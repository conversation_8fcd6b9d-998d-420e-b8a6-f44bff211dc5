/*
 * @Author: s<PERSON><PERSON>
 * @Date: 2025-03-10 09:51:40
 * @LastEditTime: 2025-03-10 09:59:41
 * @LastEditors: shaojun
 * @Description:
 */
import { EnvEnum } from '../type'

export const defaultLocalUrl = 'http://localhost:9527'

// 生成baseUrl
export const generateBaseUrl = (targetProjectPrefix: string, env: EnvEnum, defaultUrl?: string): string | undefined => {
  if (env === EnvEnum.LOCAL) {
    return defaultUrl || defaultLocalUrl
  }

  /**
   * 1、匹配所有规则的正则 /([d|t]\d|gray-)?([a-z]*).(cjiatest|cjbnb|cjiahome|cjia).(com)/
   * 2、每个环境生成baseUrl的正则
   * @description 用于匹配不同环境的baseUrl,其中项目标识(如yzq)作为可变参数
   */
  const envBaseURLRegex = new Map<EnvEnum, RegExp>([
    [EnvEnum.LOCAL, /^http:\/\/192\.168\.16\.10:9721$/],
    [EnvEnum.D1, /^https:\/\/d1([a-z]*)\.cjiatest\.com$/],
    [EnvEnum.D2, /^https:\/\/d2([a-z]*)\.cjiatest\.com$/],
    [EnvEnum.D3, /^https:\/\/d3([a-z]*)\.cjiatest\.com$/],
    [EnvEnum.T1, /^https:\/\/t1([a-z]*)\.cjiatest\.com$/],
    [EnvEnum.T2, /^https:\/\/t2([a-z]*)\.cjiatest\.com$/],
    [EnvEnum.T3, /^https:\/\/t3([a-z]*)\.cjiatest\.com$/],
    [EnvEnum.PRE, /^https:\/\/([a-z]*)\.cjbnb\.com$/],
    [EnvEnum.GRAY, /^https:\/\/gray-([a-z]*)\.cjiahome\.com$/],
    [EnvEnum.PRD, /^https:\/\/([a-z]*)\.cjiahome\.com$/],
  ])

  const regex = envBaseURLRegex.get(env)

  if (!regex) {
    console.error(`env ${env} 不存在`)
    return
  }

  const baseUrl = regex.toString() // '/^https:\\/\\/d1([a-z]*)\\.cjiatest\\.com$/'
    // 去掉正则相关字符
    .replace('/^', '')
    .replace('$/', '')
    .replace(/\\/g, '')
    // 替换([a-z]*)为targetProjectPrefix
    .replace('([a-z]*)', targetProjectPrefix)
  return baseUrl
}

/**
 * 生成baseUrl map
 * @param targetProjectPrefix 项目前缀
 * @param localUrl 本地环境地址
 * @param fixedMap 使用fixedMap
 * @returns baseUrl map
 * @description 生成baseUrl map
 */
export const generateBaseUrlMap = (targetProjectPrefix: string, fixedMap: Partial<Record<EnvEnum, string>>) => {
  // 根据EnvEnum枚举类型生成list
  const envList = Object.values(EnvEnum)
  const baseUrlMap = new Map<EnvEnum, string>()
  envList.forEach((env) => {
    // if (env === EnvEnum.LOCAL) {
    //   baseUrlMap.set(env, localUrl || defaultLocalUrl)
    //   return
    // }
    if (fixedMap[env]) {
      baseUrlMap.set(env, fixedMap[env])
      return
    }
    const baseUrl = generateBaseUrl(targetProjectPrefix, env)
    baseUrl && baseUrlMap.set(env, baseUrl)
  })
  return baseUrlMap
}
