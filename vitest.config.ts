/// <reference types="vitest" />
import { resolve } from 'node:path'
import process from 'node:process'
import { defineConfig } from 'vitest/config'
import { createAllAliases } from './configs/alias'

// 创建包的别名配置
function createPackageAlias(packageName: string) {
  return {
    '@': resolve(process.cwd(), `./packages/${packageName}/src`),
  }
}
const projectRoot = resolve(process.cwd(), '.')

export default defineConfig({
  resolve: {
    alias: createAllAliases(projectRoot),
  },
  test: {
    globals: true,
    // 基础配置
    // environment: 'node',
    environment: 'happy-dom',
    setupFiles: [resolve(__dirname, './tests/setupFiles/index.ts')],
    environmentOptions: {
      happyDOM: {
        settings: {
          navigator: {
            userAgent: 'happy-dom',
          },
        },
      },
    },
    server: {
      deps: {
        inline: [/vue/, /element-plus/, /minidev/],
      },
    },
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/.{idea,git,cache,output,temp}/**',
      '**/__template.test.ts',
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'coverage/**',
        'dist/**',
        '**/[.]**',
        'packages/*/test?(s)/**',
        '**/*.d.ts',
        '**/virtual:*',
        '**/__mocks__/*',
        '**/__tests__/**',
        '**/__template.test.ts',
      ],
    },
    // projects: [
    //   // 根目录配置
    //   {
    //     test: {
    //       name: 'root',
    //       include: ['tests/**/*.{test,spec}.{js,ts}'],
    //     },
    //   },
    //   // koa-swagger-decorator
    //   {
    //     extends: './vite.config.ts',
    //     test: {
    //       name: '@cfe-node/koa-swagger-decorator',
    //       include: ['packages/koa-swagger-decorator/tests/**/*.{test,spec}.{js,ts}'],
    //       alias: createPackageAlias('koa-swagger-decorator'),
    //     },
    //   },
    //   // server
    //   {
    //     test: {
    //       name: '@cfe-node/server',
    //       include: ['packages/server/tests/**/*.{test,spec}.{js,ts}'],
    //       alias: createPackageAlias('server'),
    //     },
    //   },
    //   // utils
    //   {
    //     test: {
    //       name: '@cfe-node/utils',
    //       include: ['packages/utils/tests/**/*.{test,spec}.{js,ts}'],
    //       alias: createPackageAlias('utils'),
    //     },
    //   },
    // ],
  },
})
