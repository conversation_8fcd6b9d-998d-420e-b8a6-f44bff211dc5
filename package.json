{"name": "cfe-node", "private": true, "scripts": {"clean": "rimraf packages/*/dist", "predev": "pnpm run -r stub", "dev": "pnpm -F @cfe-node/server dev", "dev:staging": "pnpm -F @cfe-node/server dev:staging", "dev:build": "pnpm run build:lib && pnpm -F @cfe-node/server dev", "stub": "pnpm -r run stub", "build": "pnpm run clean && pnpm -r run build", "build:lib": "pnpm run clean && pnpm -r run build:lib", "deploy:development": "pnpm run build:lib && pnpm -F @cfe-node/server run deploy:development", "deploy:staging": "pnpm run build:lib && pnpm -F @cfe-node/server deploy:staging", "deploy:prod": "pnpm run build:lib && pnpm -F @cfe-node/server deploy:prod", "test": "vitest", "test:coverage": "vitest run --coverage", "test:server": "vitest packages/server", "test:utils": "vitest packages/utils", "docs:dev": "pnpm -F @cfe-node/docs dev", "docs:build": "pnpm -F @cfe-node/docs build", "docs:preview": "pnpm -F @cfe-node/docs preview", "lint": "eslint .", "lint:debug": "eslint . --debug", "lint:output": "eslint . --format compact --output-file eslintreport.txt", "lint:fix": "eslint . --fix", "lint:lint-staged": "pnpm lint-staged"}, "dependencies": {"@cfe-node/koa-swagger-decorator": "workspace:*", "@cfe-node/utils": "workspace:*", "@vueuse/core": "^10.7.2"}, "devDependencies": {"@antfu/eslint-config": "^4.16.2", "@antfu/ni": "^24.3.0", "@types/node": "^20.17.27", "@unocss/eslint-plugin": "^66.0.0", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "bumpp": "^10.1.0", "chalk": "^5.4.1", "code-inspector-plugin": "^0.20.9", "consola": "^3.4.2", "cross-env": "^7.0.3", "cross-fetch": "^4.1.0", "eslint": "^9.30.1", "eslint-plugin-format": "^1.0.1", "eslint-plugin-vue": "^9.30.1", "esno": "^4.8.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "happy-dom": "^17.4.4", "jsdom": "^26.1.0", "msw": "^2.7.4", "npm-run-all": "^4.1.5", "rimraf": "^5.0.10", "taze": "^19.0.4", "typescript": "^5.8.3", "unbuild": "^3.5.0", "unocss": "^66.0.0", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "0.16.6", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "^4.5.3", "vite-plugin-inspect": "^11.0.0", "vite-plugin-restart": "^0.4.2", "vite-plugin-vue-mcp": "^0.3.2", "vitest": "^3.2.4", "vue": "^3.3.8", "vue-eslint-parser": "^9.1.0", "vue-tsc": "^2.2.10"}}